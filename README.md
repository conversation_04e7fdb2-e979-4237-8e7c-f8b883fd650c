# Cisco FMC Host Object Management

This project provides a Python script to bulk create host objects in Cisco Firepower Management Center (FMC) using CSV data.

## Files Included

- `main.py` - Main script for creating host objects via FMC REST API
- `sample.csv` - Sample CSV file with host, network, and range objects
- `test_csv_processing.py` - Test script to validate CSV processing without network connectivity
- `output.json` - Generated JSON output from CSV processing
- `README.md` - This documentation file

## Setup

1. **Install Python Dependencies:**
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install requests
   ```

2. **Configure FMC Connection:**
   Edit `main.py` and update these variables:
   ```python
   address = "YOUR_FMC_IP_ADDRESS"
   username = "YOUR_USERNAME"
   password = "YOUR_PASSWORD"
   ```

## Usage

### Main Script
```bash
python main.py
```

### Test CSV Processing (No Network Required)
```bash
python test_csv_processing.py
```

## CSV Format

The CSV file should have the following columns:
- `name` - Object name
- `description` - Object description  
- `type` - Object type (Host, Network, Range)
- `value` - IP address or network range

Example:
```csv
name,description,type,value
Host-test1,Host-test1,Host,**********0
Host-test2,Host-test2,Host,**********
Network-test,Network-test,Network,*********/24
```

## Features

- ✅ Filters CSV data by object type (Host objects only)
- ✅ Generates JSON payload for FMC API
- ✅ Error handling for file operations and API requests
- ✅ SSL certificate verification disabled (for testing)
- ✅ Bulk object creation support

## Security Notes

- Hardcoded credentials should be replaced with environment variables or secure config files
- SSL verification is disabled (`verify=False`) - enable for production use
- Consider using API keys or token-based authentication

## Troubleshooting

1. **Authentication Failed**: Check FMC IP address, credentials, and network connectivity
2. **CSV File Not Found**: Ensure `sample.csv` is in the same directory as the script
3. **No Host Objects**: Verify CSV contains rows with `type` column set to "Host"

## API Endpoints Used

- Authentication: `POST /api/fmc_platform/v1/auth/generatetoken`
- Host Creation: `POST /api/fmc_config/v1/domain/{DOMAIN_UUID}/object/hosts?bulk=true` 