#!/usr/bin/env python3
"""
FMC Migration Toolkit v2.0 - Refactored with fmcapi patterns

This version combines the best of both worlds:
- Clean fmcapi-style object-oriented patterns
- Robust migration orchestration with checkpointing  
- Fallback compatibility with our existing connection logic
- Enhanced error handling and phantom object detection

Author: AI Assistant
Version: 2.0
Date: 2025-08-04
"""

import json
import time
import sys
import os
import datetime
import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from pathlib import Path

# Ensure current directory is in Python path for imports
if os.path.dirname(os.path.abspath(__file__)) not in sys.path:
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Try to import fmcapi, fallback to our custom implementation
try:
    import fmcapi
    FMCAPI_AVAILABLE = True
except ImportError:
    FMCAPI_AVAILABLE = False

# Always import the custom implementation for fallback
try:
    from fmc_api_executor import FMCAPIExecutor
except ImportError as e:
    FMCAPIExecutor = None
    IMPORT_ERROR_MSG = str(e)
except Exception as e:
    FMCAPIExecutor = None
    IMPORT_ERROR_MSG = f"Unexpected error: {str(e)}"
else:
    IMPORT_ERROR_MSG = None

@dataclass
class MigrationResult:
    """Result of a migration operation"""
    success: bool
    action: str  # 'created', 'updated', 'found', 'failed', 'skipped'
    object_type: str
    object_name: str
    object_id: Optional[str] = None
    message: Optional[str] = None
    data: Optional[Dict] = None
    phantom_object: bool = False

@dataclass
class PhaseResult:
    """Result of a migration phase"""
    phase_name: str
    total_objects: int
    created: int = 0
    updated: int = 0
    failed: int = 0
    skipped: int = 0
    details: List[str] = None
    duration_seconds: float = 0.0
    success_rate: float = 0.0
    
    def __post_init__(self):
        if self.details is None:
            self.details = []
        self.success_rate = ((self.created + self.updated + self.skipped) / self.total_objects * 100) if self.total_objects > 0 else 0

class FMCObjectBase:
    """Base class for FMC objects following fmcapi patterns"""
    
    def __init__(self, fmc_connection, name: str = None):
        self.fmc = fmc_connection
        self.name = name
        self.data = {}
        self.id = None
        self.type = self.__class__.__name__
        
    def get(self, name: str = None) -> MigrationResult:
        """Get object by name using appropriate connection method"""
        search_name = name or self.name
        if not search_name:
            return MigrationResult(
                success=False,
                action='failed',
                object_type=self.type,
                object_name='UNKNOWN',
                message="No name provided for object lookup"
            )
            
        try:
            # Determine if we should use fmcapi or custom implementation
            use_fmcapi = (FMCAPI_AVAILABLE and 
                         hasattr(self.fmc, '__enter__') and 
                         str(type(self.fmc)).find('fmcapi') != -1)
            
            if use_fmcapi:
                # Use fmcapi method with proper context manager
                try:
                    with self.fmc as fmc_conn:
                        obj = self._get_fmcapi_object(fmc_conn)
                        obj.name = search_name
                        obj.get()

                        # fmcapi stores object data differently - check for id as success indicator
                        # After get(), the object should have an id if found
                        if hasattr(obj, 'id') and obj.id:
                            # Object was found successfully
                            self.data = {}
                            self.id = obj.id

                            # Capture fmcapi object attributes
                            if hasattr(obj, 'name') and obj.name:
                                self.data['name'] = obj.name
                            if hasattr(obj, 'value') and obj.value:
                                self.data['value'] = obj.value
                            if hasattr(obj, 'description') and obj.description:
                                self.data['description'] = obj.description
                            if hasattr(obj, 'type') and obj.type:
                                self.data['type'] = obj.type

                            # Also try to get the data attribute if it exists
                            if hasattr(obj, 'data') and obj.data:
                                self.data.update(obj.data)

                            return MigrationResult(
                                success=True,
                                action='found',
                                object_type=self.type,
                                object_name=search_name,
                                object_id=self.id,
                                data=self.data
                            )
                        else:
                            # Object was not found - this is normal for new objects
                            return MigrationResult(
                                success=False,
                                action='failed',
                                object_type=self.type,
                                object_name=search_name,
                                message=f"Object '{search_name}' not found in FMC (will be created)"
                            )
                except Exception as fmcapi_error:
                    return MigrationResult(
                        success=False,
                        action='failed',
                        object_type=self.type,
                        object_name=search_name,
                        message=f"fmcapi get() failed: {str(fmcapi_error)}"
                    )
            else:
                # Use our custom implementation
                if hasattr(self.fmc, '_live_lookup_object_by_name'):
                    endpoint_for_custom = getattr(self, 'custom_endpoint', self.endpoint)
                    result = self.fmc._live_lookup_object_by_name(endpoint_for_custom, search_name)
                    # Custom implementation returns object data directly (not wrapped in success/data)
                    if result and isinstance(result, dict) and result.get('id'):
                        self.data = result
                        self.id = result.get('id')
                        return MigrationResult(
                            success=True,
                            action='found',
                            object_type=self.type,
                            object_name=search_name,
                            object_id=self.id,
                            data=self.data
                        )
                    else:
                        return MigrationResult(
                            success=False,
                            action='failed',
                            object_type=self.type,
                            object_name=search_name,
                            message=f"Custom lookup failed: Object not found or invalid format"
                        )
                else:
                    return MigrationResult(
                        success=False,
                        action='failed',
                        object_type=self.type,
                        object_name=search_name,
                        message=f"No valid connection method available. Connection type: {type(self.fmc)}"
                    )
                    
        except Exception as e:
            return MigrationResult(
                success=False,
                action='failed',
                object_type=self.type,
                object_name=search_name,
                message=f"Exception during lookup: {str(e)}"
            )
    
    def post(self) -> MigrationResult:
        """Create new object using appropriate connection method"""
        if not self.data:
            return MigrationResult(
                success=False,
                action='failed',
                object_type=self.type,
                object_name=self.name or 'UNKNOWN',
                message="No data provided for object creation"
            )
            
        try:
            # Determine if we should use fmcapi or custom implementation  
            use_fmcapi = (FMCAPI_AVAILABLE and 
                         hasattr(self.fmc, '__enter__') and 
                         str(type(self.fmc)).find('fmcapi') != -1)
            
            if use_fmcapi:
                # Use fmcapi method with proper context manager
                try:
                    with self.fmc as fmc_conn:
                        obj = self._get_fmcapi_object(fmc_conn)
                        # Only set essential fmcapi attributes (not all data fields)
                        if 'name' in self.data:
                            obj.name = self.data['name']
                        if 'value' in self.data:
                            obj.value = self.data['value']
                        if 'description' in self.data:
                            obj.description = self.data['description']
                        # For ProtocolPortObjects
                        if 'protocol' in self.data:
                            obj.protocol = self.data['protocol']
                        if 'port' in self.data:
                            obj.port = self.data['port']
                        
                        result = obj.post()
                        if hasattr(obj, 'id') and obj.id:
                            self.id = obj.id
                            return MigrationResult(
                                success=True,
                                action='created',
                                object_type=self.type,
                                object_name=self.name,
                                object_id=self.id,
                                data=getattr(obj, 'data', {})
                            )
                        else:
                            return MigrationResult(
                                success=False,
                                action='failed',
                                object_type=self.type,
                                object_name=self.name,
                                message=f"fmcapi post() failed - no ID returned. Result: {result}"
                            )
                except Exception as fmcapi_error:
                    error_msg = str(fmcapi_error)
                    # Check if this is an "already exists" error
                    if "already exists" in error_msg.lower():
                        # Try to find the existing object
                        try:
                            get_result = self.get()
                            if get_result.success:
                                return MigrationResult(
                                    success=True,
                                    action='found',
                                    object_type=self.type,
                                    object_name=self.name,
                                    object_id=get_result.object_id,
                                    data=get_result.data,
                                    message=f"Object already exists, found existing: {self.name}"
                                )
                        except Exception as get_error:
                            # If GET also fails, continue with original error
                            pass

                    return MigrationResult(
                        success=False,
                        action='failed',
                        object_type=self.type,
                        object_name=self.name,
                        message=f"fmcapi post() failed: {error_msg}"
                    )
            else:
                # Use our custom implementation
                if hasattr(self.fmc, 'create_object_with_retry'):
                    # Get existing objects as required by the method signature
                    try:
                        existing_objects = self.fmc.get_existing_objects()
                        if existing_objects is None:
                            existing_objects = {}  # Fallback to empty dict
                    except Exception as e:
                        existing_objects = {}  # Fallback to empty dict if method fails

                    endpoint_for_custom = getattr(self, 'custom_endpoint', self.endpoint)
                    result = self.fmc.create_object_with_retry(endpoint_for_custom, self.data, existing_objects, max_retries=10)
                    if result.get('success'):
                        self.id = result.get('data', {}).get('id')
                        return MigrationResult(
                            success=True,
                            action='created',
                            object_type=self.type,
                            object_name=self.name,
                            object_id=self.id,
                            data=result.get('data'),
                            phantom_object=result.get('phantom_object', False)
                        )
                    else:
                        return MigrationResult(
                            success=False,
                            action='failed',
                            object_type=self.type,
                            object_name=self.name,
                            message=f"Custom creation failed: {result.get('message', 'No details')}",
                            phantom_object=result.get('phantom_object', False)
                        )
                else:
                    return MigrationResult(
                        success=False,
                        action='failed',
                        object_type=self.type,
                        object_name=self.name,
                        message=f"No valid creation method available. Connection type: {type(self.fmc)}"
                    )
                    
        except Exception as e:
            return MigrationResult(
                success=False,
                action='failed',
                object_type=self.type,
                object_name=self.name,
                message=f"Exception during creation: {str(e)}"
            )
    
    def put(self) -> MigrationResult:
        """Update existing object using appropriate connection method"""
        if not self.id:
            return MigrationResult(
                success=False,
                action='failed',
                object_type=self.type,
                object_name=self.name or 'UNKNOWN',
                message="No object ID available for update"
            )
            
        try:
            # Determine if we should use fmcapi or custom implementation
            use_fmcapi = (FMCAPI_AVAILABLE and 
                         hasattr(self.fmc, '__enter__') and 
                         str(type(self.fmc)).find('fmcapi') != -1)
            
            if use_fmcapi:
                # Use fmcapi method with proper context manager
                try:
                    with self.fmc as fmc_conn:
                        obj = self._get_fmcapi_object(fmc_conn)
                        obj.id = self.id
                        # Only set essential fmcapi attributes (not all data fields)
                        if 'name' in self.data:
                            obj.name = self.data['name']
                        if 'value' in self.data:
                            obj.value = self.data['value']
                        if 'description' in self.data:
                            obj.description = self.data['description']
                        # For ProtocolPortObjects
                        if 'protocol' in self.data:
                            obj.protocol = self.data['protocol']
                        if 'port' in self.data:
                            obj.port = self.data['port']
                            
                        result = obj.put()
                        return MigrationResult(
                            success=True,
                            action='updated',
                            object_type=self.type,
                            object_name=self.name,
                            object_id=self.id,
                            data=getattr(obj, 'data', {})
                        )
                except Exception as fmcapi_error:
                    return MigrationResult(
                        success=False,
                        action='failed',
                        object_type=self.type,
                        object_name=self.name,
                        message=f"fmcapi put() failed: {str(fmcapi_error)}"
                    )
            else:
                # Use our custom implementation
                # Need to construct full API path for make_api_call
                endpoint_mapping = {
                    'hosts': '/api/fmc_config/v1/domain/{domain_uuid}/object/hosts',
                    'networks': '/api/fmc_config/v1/domain/{domain_uuid}/object/networks', 
                    'protocolportobjects': '/api/fmc_config/v1/domain/{domain_uuid}/object/protocolportobjects'
                }
                base_endpoint = endpoint_mapping.get(self.endpoint, f'/api/fmc_config/v1/domain/{{domain_uuid}}/object/{self.endpoint}')
                endpoint_with_id = f"{base_endpoint}/{self.id}"
                result = self.fmc.make_api_call('PUT', endpoint_with_id, self.data)
                if result.get('success'):
                    return MigrationResult(
                        success=True,
                        action='updated',
                        object_type=self.type,
                        object_name=self.name,
                        object_id=self.id,
                        data=result.get('data')
                    )
                else:
                    return MigrationResult(
                        success=False,
                        action='failed',
                        object_type=self.type,
                        object_name=self.name,
                        message=result.get('message', 'Update failed')
                    )
                    
        except Exception as e:
            return MigrationResult(
                success=False,
                action='failed',
                object_type=self.type,
                object_name=self.name,
                message=f"Exception during update: {e}"
            )
            
    def _get_fmcapi_object(self, fmc_conn):
        """Get the appropriate fmcapi object for this type"""
        # This should be overridden by subclasses
        raise NotImplementedError("Subclasses must implement _get_fmcapi_object")

class HostObject(FMCObjectBase):
    """FMC Host Object following fmcapi patterns"""
    
    def __init__(self, fmc_connection, name: str = None, value: str = None, description: str = ""):
        super().__init__(fmc_connection, name)
        self.endpoint = "hosts"  # For fmcapi compatibility
        self.custom_endpoint = "host"  # For custom implementation compatibility
        
        if name and value:
            self.data = {
                "name": name,
                "type": "Host",
                "value": value,
                "description": description
            }
            
    def _get_fmcapi_object(self, fmc_conn):
        """Get fmcapi Hosts object"""
        if FMCAPI_AVAILABLE:
            return fmcapi.Hosts(fmc=fmc_conn)
        raise NotImplementedError("fmcapi not available")

class NetworkObject(FMCObjectBase):
    """FMC Network Object following fmcapi patterns"""
    
    def __init__(self, fmc_connection, name: str = None, value: str = None, description: str = ""):
        super().__init__(fmc_connection, name)
        self.endpoint = "networks"  # For fmcapi compatibility
        self.custom_endpoint = "network"  # For custom implementation compatibility
        
        if name and value:
            self.data = {
                "name": name,
                "type": "Network",
                "value": value,
                "description": description
            }
            
    def _get_fmcapi_object(self, fmc_conn):
        """Get fmcapi Networks object"""
        if FMCAPI_AVAILABLE:
            return fmcapi.Networks(fmc=fmc_conn)
        raise NotImplementedError("fmcapi not available")

class ProtocolPortObject(FMCObjectBase):
    """FMC Protocol Port Object following fmcapi patterns"""

    def __init__(self, fmc_connection, name: str = None, protocol: str = "TCP",
                 port: Union[int, str] = None, description: str = ""):
        super().__init__(fmc_connection, name)
        self.endpoint = "protocolportobjects"  # For fmcapi compatibility
        self.custom_endpoint = "service"  # For custom implementation compatibility

        if name and port:
            self.data = {
                "name": name,
                "type": "ProtocolPortObject",
                "protocol": protocol.upper(),
                "port": str(port),
                "description": description
            }

    def _get_fmcapi_object(self, fmc_conn):
        """Get fmcapi ProtocolPortObjects object"""
        if FMCAPI_AVAILABLE:
            return fmcapi.ProtocolPortObjects(fmc=fmc_conn)
        raise NotImplementedError("fmcapi not available")

class NetworkGroup(FMCObjectBase):
    """FMC Network Group Object following fmcapi patterns"""

    def __init__(self, fmc_connection, name: str = None, objects: List[Dict] = None, description: str = ""):
        super().__init__(fmc_connection, name)
        self.endpoint = "networkgroups"  # For fmcapi compatibility
        self.custom_endpoint = "networkgroup"  # For custom implementation compatibility

        if name:
            self.data = {
                "name": name,
                "type": "NetworkGroup",
                "description": description,
                "objects": objects or []
            }

    def _get_fmcapi_object(self, fmc_conn):
        """Get fmcapi NetworkGroups object"""
        if FMCAPI_AVAILABLE:
            return fmcapi.NetworkGroups(fmc=fmc_conn)
        raise NotImplementedError("fmcapi not available")

class PortObjectGroup(FMCObjectBase):
    """FMC Port Object Group following fmcapi patterns"""

    def __init__(self, fmc_connection, name: str = None, objects: List[Dict] = None, description: str = ""):
        super().__init__(fmc_connection, name)
        self.endpoint = "portobjectgroups"  # For fmcapi compatibility
        self.custom_endpoint = "servicegroup"  # For custom implementation compatibility

        if name:
            self.data = {
                "name": name,
                "type": "PortObjectGroup",
                "description": description,
                "objects": objects or []
            }

    def _get_fmcapi_object(self, fmc_conn):
        """Get fmcapi PortObjectGroups object"""
        if FMCAPI_AVAILABLE:
            return fmcapi.PortObjectGroups(fmc=fmc_conn)
        raise NotImplementedError("fmcapi not available")

class AccessRule(FMCObjectBase):
    """FMC Access Rule following fmcapi patterns"""

    def __init__(self, fmc_connection, name: str = None, action: str = "ALLOW",
                 source_networks: List = None, destination_networks: List = None,
                 source_ports: List = None, destination_ports: List = None,
                 applications: List = None, enabled: bool = True,
                 log_begin: bool = False, log_end: bool = True):
        super().__init__(fmc_connection, name)
        self.endpoint = "accessrules"  # For fmcapi compatibility
        self.custom_endpoint = "accessrule"  # For custom implementation compatibility

        if name:
            self.data = {
                "name": name,
                "type": "AccessRule",
                "action": action,
                "sourceNetworks": source_networks,
                "destinationNetworks": destination_networks,
                "sourcePorts": source_ports,
                "destinationPorts": destination_ports,
                "applications": applications,
                "enabled": enabled,
                "logBegin": log_begin,
                "logEnd": log_end
            }

    def _get_fmcapi_object(self, fmc_conn):
        """Get fmcapi AccessRules object"""
        if FMCAPI_AVAILABLE:
            return fmcapi.AccessRules(fmc=fmc_conn)
        raise NotImplementedError("fmcapi not available")

class FMCMigrationEngine:
    """
    Enhanced FMC Migration Engine v2.0
    
    Features:
    - fmcapi pattern compatibility
    - Phase-based migration with checkpointing
    - Phantom object detection and handling
    - Comprehensive logging and reporting
    - Resume capability from any phase
    """
    
    def __init__(self, fmc_host: str, username: str, password: str,
                 verify_ssl: bool = False, overwrite: bool = False, quiet: bool = False):
        
        self.session_start_time = time.time()
        self.checkpoint_dir = Path("migration_checkpoints")
        self.checkpoint_dir.mkdir(exist_ok=True)
        
        # Initialize FMC connection with robust fallback
        self.fmc = None
        self.connection_type = "unknown"
        
        if FMCAPI_AVAILABLE:
            try:
                # Try fmcapi first - simplified connection test
                host_clean = fmc_host.replace('https://', '').replace('http://', '')

                # Create FMC connection object
                self.fmc = fmcapi.FMC(host=host_clean, username=username, password=password,
                                    autodeploy=False)
                self.connection_type = "fmcapi"

            except Exception:
                self.fmc = None
        
        # Fall back to custom implementation if fmcapi failed or unavailable
        if self.fmc is None:
            if FMCAPIExecutor is None:
                error_details = f" - {IMPORT_ERROR_MSG}" if IMPORT_ERROR_MSG else ""
                raise ImportError(f"Both fmcapi and custom FMCAPIExecutor are unavailable{error_details}")

            self.fmc = FMCAPIExecutor(fmc_host, username, password,
                                    verify_ssl=verify_ssl, overwrite=overwrite)
            self.connection_type = "custom"
            
        # Migration state tracking
        self.completed_phases = {}
        self.current_session_id = f"migration_{int(self.session_start_time)}"
        self.phantom_objects = set()
        self.quiet = quiet

        # Setup logging
        self.setup_logging()

    def print_info(self, message: str, force: bool = False):
        """Print message only if not in quiet mode or if forced"""
        if not self.quiet or force:
            print(message)

    def setup_logging(self):
        """Setup comprehensive logging with Unicode support"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_files = {
            'main': log_dir / f"fmc_migration_v2_{timestamp}.log",
            'errors': log_dir / f"fmc_errors_v2_{timestamp}.log",
            'debug': log_dir / f"fmc_debug_v2_{timestamp}.log"
        }
        
        # Configure logger
        self.logger = logging.getLogger('fmc_migration_v2')
        self.logger.setLevel(logging.DEBUG)
        
        # File handler with UTF-8 encoding
        fh = logging.FileHandler(self.log_files['main'], encoding='utf-8')
        fh.setLevel(logging.INFO)
        
        # Debug file handler
        debug_fh = logging.FileHandler(self.log_files['debug'], encoding='utf-8')
        debug_fh.setLevel(logging.DEBUG)
        
        # Console handler - only if not in quiet mode
        if not self.quiet:
            ch = logging.StreamHandler()
            ch.setLevel(logging.WARNING)  # Only show warnings and errors in console
        else:
            ch = None
        
        formatter = logging.Formatter('%(asctime)s | %(levelname)s | %(message)s')
        fh.setFormatter(formatter)
        debug_fh.setFormatter(formatter)

        self.logger.addHandler(fh)
        self.logger.addHandler(debug_fh)

        if ch is not None:
            ch.setFormatter(formatter)
            self.logger.addHandler(ch)
        
        # Detect emoji support
        self.emoji_support = self._detect_emoji_support()
        
        self.logger.info("=" * 80)
        self.logger.info("FMC Migration Engine v2.0 Started")
        self.logger.info(f"Session ID: {self.current_session_id}")
        self.logger.info(f"Connection Type: {self.connection_type}")
        
        # Test connection and log details
        self._test_connection()
        
        self.logger.info("=" * 80)
    
    def _detect_emoji_support(self) -> bool:
        """Detect if the current environment supports emoji characters"""
        try:
            # Try to encode a simple emoji
            test_emoji = "✅"
            test_emoji.encode('cp1252')
            return True
        except UnicodeEncodeError:
            return False
        except:
            # Default to safe mode
            return False
    
    def _safe_log(self, level: str, message: str, emoji_fallback: str = None):
        """Log message with emoji support detection"""
        if self.emoji_support:
            log_message = message
        else:
            # Replace common emojis with text equivalents
            replacements = {
                '✅': '[OK]',
                '❌': '[FAIL]',
                '⚠️': '[WARN]',
                '📊': '[INFO]',
                '📁': '[FILE]',
                '🎉': '[SUCCESS]',
                '🚀': '[START]',
                '⏭️': '[SKIP]',
                '📈': '[PROGRESS]',
                '⏱️': '[TIME]',
                '👻': '[PHANTOM]',
                '🔄': '[RETRY]',
                '📖': '[LOAD]',
                '📄': '[DOC]',
                '🛑': '[STOP]'
            }
            
            log_message = message
            for emoji, replacement in replacements.items():
                log_message = log_message.replace(emoji, replacement)
                
            # Use explicit fallback if provided
            if emoji_fallback:
                log_message = emoji_fallback
        
        # Use the appropriate logging level
        getattr(self.logger, level.lower())(log_message)
    
    def _test_connection(self):
        """Test and diagnose FMC connection"""
        self._safe_log('info', "🔍 Connection Diagnostic:")
        self.logger.info(f"   • FMC Object Type: {type(self.fmc)}")
        self.logger.info(f"   • fmcapi Available: {FMCAPI_AVAILABLE}")
        
        # Test what methods are available
        methods_available = []
        if hasattr(self.fmc, '__enter__'):
            methods_available.append("__enter__ (context manager)")
        if hasattr(self.fmc, '_live_lookup_object_by_name'):
            methods_available.append("_live_lookup_object_by_name")
        if hasattr(self.fmc, 'create_object_with_retry'):
            methods_available.append("create_object_with_retry")
        if hasattr(self.fmc, 'get'):
            methods_available.append("get")
        if hasattr(self.fmc, 'post'):
            methods_available.append("post")
        if hasattr(self.fmc, 'put'):
            methods_available.append("put")
            
        self.logger.info(f"   • Available Methods: {', '.join(methods_available) if methods_available else 'None detected'}")
        
        # Test fmcapi object creation if available
        if FMCAPI_AVAILABLE and hasattr(self.fmc, '__enter__'):
            try:
                import fmcapi
                with self.fmc as fmc_conn:
                    test_host = fmcapi.Hosts(fmc=fmc_conn)
                    self.logger.info(f"   • fmcapi Hosts object created successfully")
                    self.logger.info(f"   • fmcapi Hosts methods: {[m for m in dir(test_host) if not m.startswith('_')][:10]}...")
            except Exception as e:
                self.logger.warning(f"   • fmcapi object creation failed: {e}")
        
        self._safe_log('info', "🔍 Connection diagnostic complete")
    
    def save_checkpoint(self, phase_name: str, phase_result: PhaseResult) -> str:
        """Save migration checkpoint"""
        # Convert all PhaseResult objects to dictionaries for JSON serialization
        completed_phases_dict = {}
        for name, result in self.completed_phases.items():
            if isinstance(result, PhaseResult):
                completed_phases_dict[name] = asdict(result)
            else:
                completed_phases_dict[name] = result
        
        checkpoint = {
            'session_id': self.current_session_id,
            'timestamp': datetime.datetime.now().isoformat(),
            'connection_type': self.connection_type,
            'completed_phases': completed_phases_dict,
            'current_phase': phase_name,
            'phase_result': asdict(phase_result),
            'phantom_objects': list(self.phantom_objects)
        }
        
        checkpoint_file = self.checkpoint_dir / f"{self.current_session_id}_{phase_name}.json"
        with open(checkpoint_file, 'w') as f:
            json.dump(checkpoint, f, indent=2)
            
        self._safe_log('info', f"📁 Checkpoint saved: {checkpoint_file}")
        return str(checkpoint_file)
    
    def is_phase_completed(self, phase_name: str) -> bool:
        """Check if a phase has already been completed"""
        return phase_name in self.completed_phases

    def _create_object_instance(self, object_class, obj_data: Dict):
        """Helper method to create object instances with proper error handling"""
        try:
            obj_name = obj_data.get('name')
            if object_class == HostObject:
                return object_class(
                    self.fmc,
                    name=obj_data.get('name'),
                    value=obj_data.get('value'),
                    description=obj_data.get('description', 'Migrated from ASA')
                )
            elif object_class == NetworkObject:
                return object_class(
                    self.fmc,
                    name=obj_data.get('name'),
                    value=obj_data.get('value'),
                    description=obj_data.get('description', 'Migrated from ASA')
                )
            elif object_class == ProtocolPortObject:
                return object_class(
                    self.fmc,
                    name=obj_data.get('name'),
                    protocol=obj_data.get('protocol', 'TCP'),
                    port=obj_data.get('port'),
                    description=obj_data.get('description', 'Migrated from ASA')
                )
            elif object_class == NetworkGroup:
                return object_class(
                    self.fmc,
                    name=obj_data.get('name'),
                    objects=obj_data.get('objects', []),
                    description=obj_data.get('description', 'Migrated from ASA')
                )
            elif object_class == PortObjectGroup:
                return object_class(
                    self.fmc,
                    name=obj_data.get('name'),
                    objects=obj_data.get('objects', []),
                    description=obj_data.get('description', 'Migrated from ASA')
                )
            elif object_class == AccessRule:
                return object_class(
                    self.fmc,
                    name=obj_data.get('name'),
                    action=obj_data.get('action', 'ALLOW'),
                    source_networks=obj_data.get('sourceNetworks'),
                    destination_networks=obj_data.get('destinationNetworks'),
                    source_ports=obj_data.get('sourcePorts'),
                    destination_ports=obj_data.get('destinationPorts'),
                    applications=obj_data.get('applications'),
                    enabled=obj_data.get('enabled', True),
                    log_begin=obj_data.get('logBegin', False),
                    log_end=obj_data.get('logEnd', True)
                )
            else:
                # Fallback for other object types
                return object_class(self.fmc, **obj_data)
        except Exception as e:
            self.logger.error(f"Failed to create object instance for {obj_name}: {e}")
            return None

    def _optimize_api_delays(self):
        """Optimize API delays based on connection type and performance"""
        if self.connection_type == "custom" and hasattr(self.fmc, 'api_delay'):
            # Reduce API delay for better performance
            original_delay = self.fmc.api_delay
            self.fmc.api_delay = max(0.05, original_delay * 0.5)  # Reduce by 50%, minimum 50ms
            self.logger.info(f"Optimized API delay from {original_delay}s to {self.fmc.api_delay}s")
            return original_delay
        return None
    
    def migrate_objects(self, object_type: str, objects_data: List[Dict],
                       object_class, description: str) -> PhaseResult:
        """
        Generic object migration method with performance optimizations

        Args:
            object_type: Type of objects being migrated (e.g., 'hosts', 'networks')
            objects_data: List of object data dictionaries
            object_class: Class to use for object creation (HostObject, NetworkObject, etc.)
            description: Human-readable description for logging
        """
        phase_name = f"phase1_{object_type}"
        start_time = time.time()

        # Check if phase was already completed
        if self.is_phase_completed(phase_name):
            self._safe_log('info', f"⏭️  Phase {description} already completed - skipping")
            cached_result = self.completed_phases[phase_name]
            # Ensure we return a PhaseResult object
            if isinstance(cached_result, dict):
                # Convert dict back to PhaseResult if needed
                cached_result = PhaseResult(**cached_result)
            return cached_result

        self.print_info(f"🚀 Starting {description} migration ({len(objects_data)} objects)...")

        # Performance optimization: Reduce API delays
        original_delay = self._optimize_api_delays()

        # Initialize results
        result = PhaseResult(
            phase_name=phase_name,
            total_objects=len(objects_data),
            details=[]
        )

        # Performance optimization: Use bulk processing for better efficiency
        batch_size = 50  # Process objects in batches
        batches = [objects_data[i:i + batch_size] for i in range(0, len(objects_data), batch_size)]

        self.print_info(f"📦 Processing {len(batches)} batches of up to {batch_size} objects each...")

        # Process each batch
        total_processed = 0
        for batch_idx, batch_data in enumerate(batches, 1):
            self.print_info(f"📦 Processing batch {batch_idx}/{len(batches)} ({len(batch_data)} objects)...")

            # Process each object in the batch
            for obj_idx, obj_data in enumerate(batch_data, 1):
                total_processed += 1

                # Show progress for overall migration
                if not self.quiet and (total_processed % 50 == 0 or len(objects_data) <= 20 or total_processed == len(objects_data)):
                    progress = (total_processed / len(objects_data)) * 100
                    print(f"  Progress: {total_processed}/{len(objects_data)} ({progress:.1f}%)", end='\r')

                try:
                    obj_name = obj_data.get('name', f'UNKNOWN_{total_processed}')

                    # Debug: Log object data structure for first few objects
                    if total_processed <= 3:
                        self.logger.debug(f"Processing {object_type} {total_processed}: {obj_name}")
                        self.logger.debug(f"Object data keys: {list(obj_data.keys())}")
                        self.logger.debug(f"Object data: {obj_data}")

                    # Skip phantom objects
                    if obj_name in self.phantom_objects:
                        result.skipped += 1
                        result.details.append(f"👻 Skipped phantom object: {obj_name}")
                        continue

                    # Create object instance with filtered parameters
                    obj = self._create_object_instance(object_class, obj_data)
                    if obj is None:
                        result.failed += 1
                        error_msg = f"❌ Failed to create {object_type[:-1]} object '{obj_name}': Invalid object data"
                        result.details.append(error_msg)
                        self.logger.error(f"Object creation error for {obj_name}: Invalid object data")
                        continue

                    # Try to get existing object first
                    get_result = obj.get()

                    # Log detailed error information for first few objects
                    if total_processed <= 5:
                        self.logger.debug(f"GET result for {obj_name}: success={get_result.success}, message='{get_result.message}'")

                    if get_result.success:
                        # Object exists, update it
                        put_result = obj.put()
                        if total_processed <= 5:
                            self.logger.debug(f"PUT result for {obj_name}: success={put_result.success}, message='{put_result.message}'")

                        if put_result.success:
                            result.updated += 1
                            if not self.quiet and result.updated <= 5:
                                result.details.append(f"✅ Updated {object_type[:-1]}: {obj_name}")
                        else:
                            result.failed += 1
                            error_msg = f"❌ Failed to update {object_type[:-1]}: {obj_name} - {put_result.message}"
                            if result.failed <= 10:
                                result.details.append(error_msg)
                            # Log first few detailed errors
                            if result.failed <= 5:
                                self.logger.error(f"Update failed for {obj_name}: {put_result.message}")
                            if hasattr(put_result, 'phantom_object') and put_result.phantom_object:
                                self.phantom_objects.add(obj_name)
                    else:
                        # Object doesn't exist, create it
                        post_result = obj.post()
                        if total_processed <= 5:
                            self.logger.debug(f"POST result for {obj_name}: success={post_result.success}, message='{post_result.message}'")

                        if post_result.success:
                            if post_result.action == 'found':
                                result.updated += 1
                                if not self.quiet and result.updated <= 5:
                                    result.details.append(f"✅ Found existing {object_type[:-1]}: {obj_name}")
                            else:
                                result.created += 1
                                if not self.quiet and result.created <= 5:
                                    result.details.append(f"✅ Created {object_type[:-1]}: {obj_name}")
                        else:
                            result.failed += 1
                            error_msg = f"❌ Failed to create {object_type[:-1]}: {obj_name} - {post_result.message}"
                            if result.failed <= 10:  # Show first 10 failures
                                result.details.append(error_msg)
                            self.logger.error(f"Creation failed for {obj_name}: {post_result.message}")
                            if hasattr(post_result, 'phantom_object') and post_result.phantom_object:
                                self.phantom_objects.add(obj_name)

                except Exception as e:
                    result.failed += 1
                    result.details.append(f"❌ Exception with {object_type[:-1]} {obj_data.get('name', 'UNKNOWN')}: {e}")
                    self.logger.error(f"Exception processing {obj_data.get('name', 'UNKNOWN')}: {e}")
        
        # Calculate final results
        result.duration_seconds = time.time() - start_time
        
        # Clear progress line and show results
        if not self.quiet:
            print()  # New line after progress
        self.print_info(f"✅ {description}: {result.created} created, {result.updated} updated, {result.failed} failed ({result.success_rate:.1f}% success)")
        
        # Restore original API delay if it was modified
        if original_delay is not None and hasattr(self.fmc, 'api_delay'):
            self.fmc.api_delay = original_delay
            self.logger.info(f"Restored original API delay: {original_delay}s")

        # Save checkpoint
        self.completed_phases[phase_name] = result
        self.save_checkpoint(phase_name, result)

        return result
    
    def run_full_migration(self, migration_config_file: str) -> Dict[str, PhaseResult]:
        """Run complete migration from configuration file"""
        
        self.print_info(f"📖 Loading configuration: {migration_config_file}")

        try:
            with open(migration_config_file, 'r') as f:
                config = json.load(f)
        except Exception as e:
            self.logger.error(f"❌ Failed to load configuration: {e}")
            raise

        # Count total objects for progress tracking
        total_objects = 0
        if 'api_calls' in config:
            for section in ['host_objects', 'network_objects', 'service_objects', 'object_groups', 'service_groups', 'access_rules']:
                if section in config['api_calls']:
                    count = len(config['api_calls'][section].get('data', []))
                    total_objects += count

        self.print_info(f"📊 Found {total_objects} total objects to migrate")
        
        results = {}
        
        # Handle both v1.0 and v2.0 config formats
        # v1.0 format: config['api_calls']['host_objects']['data']
        # v2.0 format: config['host_objects']
        
        # Phase 1: Host Objects
        host_data = None
        if 'host_objects' in config:
            host_data = config['host_objects']
        elif 'api_calls' in config and 'host_objects' in config['api_calls']:
            host_data = config['api_calls']['host_objects'].get('data', [])
            
        if host_data:
            results['hosts'] = self.migrate_objects(
                'hosts',
                host_data,
                HostObject,
                "Host Objects"
            )
        
        # Phase 2: Network Objects
        network_data = None
        if 'network_objects' in config:
            network_data = config['network_objects']
        elif 'api_calls' in config and 'network_objects' in config['api_calls']:
            network_data = config['api_calls']['network_objects'].get('data', [])
            
        if network_data:
            results['networks'] = self.migrate_objects(
                'networks',
                network_data,
                NetworkObject,
                "Network Objects"
            )
        
        # Phase 3: Service Objects
        service_data = None
        if 'service_objects' in config:
            service_data = config['service_objects']
        elif 'api_calls' in config and 'service_objects' in config['api_calls']:
            service_data = config['api_calls']['service_objects'].get('data', [])

        if service_data:
            results['services'] = self.migrate_objects(
                'services',
                service_data,
                ProtocolPortObject,
                "Protocol Port Objects"
            )

        # Phase 4: Network Groups (Object Groups)
        object_groups_data = None
        if 'object_groups' in config:
            object_groups_data = config['object_groups']
        elif 'api_calls' in config and 'object_groups' in config['api_calls']:
            object_groups_data = config['api_calls']['object_groups'].get('data', [])

        if object_groups_data:
            results['object_groups'] = self.migrate_objects(
                'object_groups',
                object_groups_data,
                NetworkGroup,
                "Network Groups"
            )

        # Phase 5: Service Groups (Port Object Groups)
        service_groups_data = None
        if 'service_groups' in config:
            service_groups_data = config['service_groups']
        elif 'api_calls' in config and 'service_groups' in config['api_calls']:
            service_groups_data = config['api_calls']['service_groups'].get('data', [])

        if service_groups_data:
            results['service_groups'] = self.migrate_objects(
                'service_groups',
                service_groups_data,
                PortObjectGroup,
                "Port Object Groups"
            )

        # Phase 6: Access Rules
        access_rules_data = None
        if 'access_rules' in config:
            access_rules_data = config['access_rules']
        elif 'api_calls' in config and 'access_rules' in config['api_calls']:
            access_rules_data = config['api_calls']['access_rules'].get('data', [])

        if access_rules_data:
            results['access_rules'] = self.migrate_objects(
                'access_rules',
                access_rules_data,
                AccessRule,
                "Access Rules"
            )

        # Generate final summary
        self.generate_migration_summary(results)

        return results
    
    def generate_migration_summary(self, results: Dict[str, PhaseResult]):
        """Generate comprehensive migration summary"""
        self.logger.info("=" * 80)
        self.logger.info("MIGRATION SUMMARY")
        self.logger.info("=" * 80)
        
        total_created = sum(r.created for r in results.values())
        total_updated = sum(r.updated for r in results.values())
        total_failed = sum(r.failed for r in results.values())
        total_skipped = sum(r.skipped for r in results.values())
        total_objects = sum(r.total_objects for r in results.values())
        
        self._safe_log('info', f"📊 OVERALL RESULTS:")
        self.logger.info(f"   • Total Objects: {total_objects}")
        self.logger.info(f"   • Created: {total_created}")
        self.logger.info(f"   • Updated: {total_updated}")
        self.logger.info(f"   • Failed: {total_failed}")
        self.logger.info(f"   • Skipped: {total_skipped}")
        
        overall_success_rate = ((total_created + total_updated + total_skipped) / total_objects * 100) if total_objects > 0 else 0
        self.logger.info(f"   • Success Rate: {overall_success_rate:.1f}%")
        
        if self.phantom_objects:
            self._safe_log('warning', f"👻 Phantom Objects Detected: {len(self.phantom_objects)}")
            for phantom in self.phantom_objects:
                self.logger.warning(f"   • {phantom}")
        
        self.logger.info("=" * 80)
        
        # Save summary to file
        summary_file = f"migration_summary_{self.current_session_id}.json"
        
        # Convert PhaseResult objects to dictionaries for JSON serialization
        results_dict = {}
        for name, result in results.items():
            if isinstance(result, PhaseResult):
                results_dict[name] = asdict(result)
            else:
                results_dict[name] = result
        
        summary_data = {
            'session_id': self.current_session_id,
            'connection_type': self.connection_type,
            'timestamp': datetime.datetime.now().isoformat(),
            'results': results_dict,
            'totals': {
                'total_objects': total_objects,
                'created': total_created,
                'updated': total_updated,
                'failed': total_failed,
                'skipped': total_skipped,
                'success_rate': overall_success_rate
            },
            'phantom_objects': list(self.phantom_objects)
        }
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, indent=2)
            
        self._safe_log('info', f"📁 Summary saved: {summary_file}")

def main():
    """Main entry point for FMC Migration Engine v2.0"""
    
    if len(sys.argv) < 2:
        print("Usage: python fmc_migration_v2.py <migration_config.json> [--overwrite] [--resume] [--quiet]")
        print("")
        print("Options:")
        print("  --overwrite  Overwrite existing objects")
        print("  --resume     Resume from previous migration")
        print("  --quiet      Reduce output verbosity")
        print("")
        print("Examples:")
        print("  python fmc_migration_v2.py fmc_migration_config.json")
        print("  python fmc_migration_v2.py fmc_migration_config.json --overwrite --quiet")
        print("  python fmc_migration_v2.py --resume")
        sys.exit(1)
    
    # Parse arguments
    config_file = sys.argv[1] if not sys.argv[1].startswith('--') else None
    overwrite = '--overwrite' in sys.argv
    resume = '--resume' in sys.argv
    quiet = '--quiet' in sys.argv
    
    # Default connection parameters (make these configurable as needed)
    fmc_host = "https://*************"
    username = "admin"
    password = "!Techn0l0gy01!"
    
    try:
        # Initialize migration engine
        engine = FMCMigrationEngine(
            fmc_host=fmc_host,
            username=username,
            password=password,
            verify_ssl=False,
            overwrite=overwrite,
            quiet=quiet
        )
        
        if resume:
            print("[RESUME] Resume functionality will be implemented in next iteration")
            return
        
        if not config_file or not os.path.exists(config_file):
            engine._safe_log('error', f"❌ Configuration file not found: {config_file}")
            sys.exit(1)
        
        # Run migration
        results = engine.run_full_migration(config_file)
        
        # Check for critical failures
        total_failed = sum(r.failed for r in results.values())
        if total_failed > 0:
            engine._safe_log('warning', f"⚠️  Migration completed with {total_failed} failures")
            sys.exit(1)
        else:
            engine._safe_log('info', "🎉 Migration completed successfully!")
            
    except KeyboardInterrupt:
        print("\n[STOP] Migration interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"[FAIL] Migration failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()