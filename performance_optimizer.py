#!/usr/bin/env python3
"""
FMC Migration Performance Optimizer

This script provides additional performance optimizations for the FMC migration process.
It can be used to:
1. Analyze migration performance bottlenecks
2. Optimize configuration settings
3. Provide performance recommendations

Usage:
    python performance_optimizer.py --analyze config.json
    python performance_optimizer.py --optimize config.json
    python performance_optimizer.py --recommend
"""

import json
import sys
import argparse
import time
from pathlib import Path
from typing import Dict, List, Any

class FMCPerformanceOptimizer:
    """Performance optimizer for FMC migration operations"""
    
    def __init__(self):
        self.recommendations = []
        
    def analyze_config(self, config_file: str) -> Dict[str, Any]:
        """Analyze migration configuration for performance bottlenecks"""
        print(f"🔍 Analyzing configuration: {config_file}")
        
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
        except Exception as e:
            print(f"❌ Failed to load configuration: {e}")
            return {}
        
        analysis = {
            'total_objects': 0,
            'object_breakdown': {},
            'estimated_time': 0,
            'bottlenecks': [],
            'optimizations': []
        }
        
        # Count objects by type
        object_types = ['host_objects', 'network_objects', 'service_objects', 
                       'object_groups', 'service_groups', 'access_rules']
        
        for obj_type in object_types:
            count = 0
            if obj_type in config:
                count = len(config[obj_type])
            elif 'api_calls' in config and obj_type in config['api_calls']:
                count = len(config['api_calls'][obj_type].get('data', []))
            
            if count > 0:
                analysis['object_breakdown'][obj_type] = count
                analysis['total_objects'] += count
        
        # Estimate migration time (conservative estimate: 2 seconds per object)
        analysis['estimated_time'] = analysis['total_objects'] * 2
        
        # Identify bottlenecks
        if analysis['total_objects'] > 1000:
            analysis['bottlenecks'].append("Large number of objects (>1000) - consider batch processing")
        
        if analysis['object_breakdown'].get('access_rules', 0) > 500:
            analysis['bottlenecks'].append("Many access rules - these are typically slower to process")
        
        # Generate optimizations
        if analysis['total_objects'] > 100:
            analysis['optimizations'].append("Use batch processing with smaller batch sizes")
            analysis['optimizations'].append("Reduce API delays for better throughput")
            analysis['optimizations'].append("Enable quiet mode to reduce logging overhead")
        
        return analysis
    
    def optimize_config(self, config_file: str) -> bool:
        """Create an optimized version of the configuration"""
        print(f"⚡ Creating optimized configuration...")
        
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
        except Exception as e:
            print(f"❌ Failed to load configuration: {e}")
            return False
        
        # Add performance optimizations to config
        if 'performance_settings' not in config:
            config['performance_settings'] = {}
        
        config['performance_settings'].update({
            'batch_size': 25,  # Smaller batches for better error handling
            'api_delay': 0.05,  # Reduced delay
            'max_retries': 5,   # Fewer retries for faster failure handling
            'quiet_mode': True,  # Reduce logging overhead
            'skip_existing_check': False,  # Keep validation for safety
            'optimized': True,
            'optimization_timestamp': time.time()
        })
        
        # Save optimized config
        optimized_file = config_file.replace('.json', '_optimized.json')
        with open(optimized_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ Optimized configuration saved to: {optimized_file}")
        return True
    
    def generate_recommendations(self) -> List[str]:
        """Generate general performance recommendations"""
        return [
            "🚀 Performance Recommendations:",
            "",
            "1. **Batch Processing**: Use smaller batch sizes (25-50 objects) for better error handling",
            "2. **API Delays**: Reduce api_delay to 0.05s or lower if FMC can handle it",
            "3. **Quiet Mode**: Enable quiet mode to reduce console output overhead",
            "4. **Parallel Processing**: Consider running different object types in parallel",
            "5. **Network**: Ensure low latency connection to FMC",
            "6. **Memory**: Monitor memory usage for large migrations",
            "7. **Checkpointing**: Use checkpointing to resume failed migrations",
            "8. **Rate Limiting**: Monitor FMC rate limits and adjust accordingly",
            "",
            "🔧 Quick Performance Fixes:",
            "",
            "• Add --quiet flag to reduce output",
            "• Use optimized configuration files",
            "• Run during off-peak hours",
            "• Monitor FMC CPU and memory usage",
            "• Consider splitting very large migrations",
            "",
            "⚠️  Safety Notes:",
            "",
            "• Always test optimizations in a lab environment first",
            "• Keep backups of original configurations",
            "• Monitor FMC performance during migration",
            "• Don't disable all safety checks for speed"
        ]
    
    def print_analysis(self, analysis: Dict[str, Any]):
        """Print detailed analysis results"""
        print("\n" + "="*60)
        print("📊 MIGRATION ANALYSIS RESULTS")
        print("="*60)
        
        print(f"\n📈 Object Summary:")
        print(f"   Total Objects: {analysis['total_objects']}")
        print(f"   Estimated Time: {analysis['estimated_time']//60:.0f}m {analysis['estimated_time']%60:.0f}s")
        
        if analysis['object_breakdown']:
            print(f"\n📋 Object Breakdown:")
            for obj_type, count in analysis['object_breakdown'].items():
                print(f"   • {obj_type.replace('_', ' ').title()}: {count}")
        
        if analysis['bottlenecks']:
            print(f"\n⚠️  Potential Bottlenecks:")
            for bottleneck in analysis['bottlenecks']:
                print(f"   • {bottleneck}")
        
        if analysis['optimizations']:
            print(f"\n⚡ Recommended Optimizations:")
            for optimization in analysis['optimizations']:
                print(f"   • {optimization}")
        
        print("\n" + "="*60)

def main():
    parser = argparse.ArgumentParser(description='FMC Migration Performance Optimizer')
    parser.add_argument('--analyze', metavar='CONFIG', help='Analyze configuration file')
    parser.add_argument('--optimize', metavar='CONFIG', help='Create optimized configuration')
    parser.add_argument('--recommend', action='store_true', help='Show performance recommendations')
    
    args = parser.parse_args()
    
    optimizer = FMCPerformanceOptimizer()
    
    if args.analyze:
        analysis = optimizer.analyze_config(args.analyze)
        if analysis:
            optimizer.print_analysis(analysis)
    
    elif args.optimize:
        success = optimizer.optimize_config(args.optimize)
        if success:
            print("\n✅ Configuration optimized successfully!")
            print("💡 Use the optimized configuration with fmc_migration_v2.py")
    
    elif args.recommend:
        recommendations = optimizer.generate_recommendations()
        for rec in recommendations:
            print(rec)
    
    else:
        parser.print_help()
        print("\n💡 Quick start:")
        print("   python performance_optimizer.py --recommend")
        print("   python performance_optimizer.py --analyze your_config.json")
        print("   python performance_optimizer.py --optimize your_config.json")

if __name__ == "__main__":
    main()
