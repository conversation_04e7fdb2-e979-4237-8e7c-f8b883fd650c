# FMC Migration Toolkit - Command Reference

## 🚨 **IMPORTANT: Which Version Are You Running?**

Based on your logs showing "FMC Migration Engine v2.0 Started", you appear to be running the v2.0 engine. Here's how to run each version correctly:

## 📝 **Command Syntax**

### **v2.0 Engine (Recommended)**
```bash
# Basic migration
python fmc_migration_v2.py fmc_migration_config.json --overwrite

# Resume from checkpoint  
python fmc_migration_v2.py --resume

# Help
python fmc_migration_v2.py
```

### **v1.0 Engine (Original)**
```bash
# Basic migration with fast mode
python fmc_api_executor.py fmc_migration_config.json --overwrite --fast

# Resume from checkpoint
python fmc_api_executor.py --resume

# Help  
python fmc_api_executor.py
```

## 🔍 **Configuration Format Support**

The v2.0 engine now supports **BOTH** config formats:

### **v1.0 Format (Your Current File)**
```json
{
  "api_calls": {
    "host_objects": {
      "data": [
        {"name": "HOST1", "type": "Host", "value": "*******"}
      ]
    },
    "network_objects": {
      "data": [
        {"name": "NET1", "type": "Network", "value": "***********/24"}
      ]
    }
  }
}
```

### **v2.0 Format (Alternative)**
```json
{
  "host_objects": [
    {"name": "HOST1", "type": "Host", "value": "*******"}
  ],
  "network_objects": [
    {"name": "NET1", "type": "Network", "value": "***********/24"}
  ]
}
```

## 🎯 **What You Should Run Now**

Based on your logs, you should run:

```bash
# This will now properly read your v1.0 config format
python fmc_migration_v2.py fmc_migration_config.json --overwrite
```

## 🆔 **How to Tell Which Version You're Running**

### **v2.0 Engine Output:**
```
[OK] Using fmcapi library for FMC connectivity
INFO:fmc_migration_v2:FMC Migration Engine v2.0 Started
[DOC] Detected v1.0 config format (api_calls structure)
[INFO] Found 629 host objects in v1.0 format
```

### **v1.0 Engine Output:**
```
2025-08-04 04:10:40 | INFO     | ==================================================================
2025-08-04 04:10:40 | INFO     | FMC Migration Session Started
2025-08-04 04:10:40 | INFO     | Phase 1: Creating Host objects...
```

## 🐛 **Troubleshooting**

### **Problem: "Total Objects: 0"**
- **Cause**: Config format mismatch between engine versions
- **Solution**: Use the updated v2.0 engine which supports both formats

### **Problem: Wrong engine running**
- **Check**: Look at the log output to confirm which engine is actually running
- **Solution**: Use the correct python command for your desired version

### **Problem: Unicode errors on Windows**
- **Cause**: Emoji characters in log messages
- **Solution**: v2.0 engine automatically handles this with text fallbacks