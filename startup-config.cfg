: Saved

: 
: Serial Number: FCH19277G9U
: Hardware:   ASA5525, 8192 MB RAM, CPU Lynnfield 2400 MHz, 1 CPU (4 cores)
: Written by enable_15 at 12:26:50.141 EDT Fri Jul 11 2025
!
ASA Version 9.12(3)9 
!
hostname ASA01
domain-name nlh.org
enable password Yn8Esq3NcXIHL35v encrypted
service-module 0 keepalive-timeout 4
service-module 0 keepalive-counter 6
service-module ips keepalive-timeout 4
service-module ips keepalive-counter 6
service-module cxsc keepalive-timeout 4
service-module cxsc keepalive-counter 6
service-module sfr keepalive-timeout 4
service-module sfr keepalive-counter 6
xlate per-session deny tcp any4 any4
xlate per-session deny tcp any4 any6
xlate per-session deny tcp any6 any4
xlate per-session deny tcp any6 any6
xlate per-session deny udp any4 any4 eq domain
xlate per-session deny udp any4 any6 eq domain
xlate per-session deny udp any6 any4 eq domain
xlate per-session deny udp any6 any6 eq domain
passwd Yn8Esq3NcXIHL35v encrypted
names
name ************** bsneny
name *************** HAdams
name ************** PaceGlobal
name ************ Hypertype
name *********** jtownSerial
name ************ MVP
name ************* MycekPC1
name ************** GHXServer
name *************** VeriquestPC
name *********** VeriquestSite
name ************ HFMBocesPOP
name ************ DrVerma
name ************ DrRoweButton
name ************* RadBellevue
name ************ DrYoungworth
name 24.97.6.227 DrMilitar
name 12.11.83.67 DrLomes
name 24.97.27.74 RadAmsMem
name 24.97.17.75 DrSalwin
name 24.97.29.35 DrKaro
name 24.97.17.43 DrWatt
name 24.97.28.203 DrGill
name 24.97.17.90 RadSaratoga
name 24.97.29.57 RadStMarys
name 24.97.17.67 DrLau
name 24.97.28.227 DrHaber
name 64.97.15.123 RadSeton
name 192.168.176.1 MAGICA
name 156.30.21.128 questlab
name 63.236.75.87 smileycentral
name 192.168.250.194 tranmailadr
name 204.97.180.95 atecgroup
name 204.97.174.6 LeaderHFTPsite
name 10.27.1.0 eTrans1
name 10.29.1.0 eTrans2
name ************* LAN
name 192.168.225.0 tri-delta
name 10.0.6.30 IRIS
name 192.168.184.143 P_FS_SEC
name 192.168.184.142 P_FS_MGR
name 6************ Sodexho
name *************** CarogaLake
name ************ CarogaDSL
name ********* GEserviceNET
name *************** speculator
name ********** GEInsight
name ************* GE.VPN
name ************** PetLinks2
name ************** PetLinks1
name ************* cpc.rtr
name ********** IRISvpn
name ************* stentor.com
name *************** TeleVideo1
name ************* AE_NIGHTHAWK
name ************* TeleRadPC
name ************* AE_NIGHTHAWK2
name ************* AE_NIGHTHAWK3
name ************ iPEOPLEremote
name ************* MAGICE
name ************* MAGICD
name ************* MAGICC
name ********** Mill.PACS.NET
name *********** MilleniumVPN
name *************** Infotrak
name ************ RALSplus
name ************ RALSnat
name ************* RALSplusLAN
name ************** RALSplusServer
name *************** attRIG2
name ************** attRIG4
name *************** attRIG3
name *************** attRIG1
name ************* ATT_VPN_HOST
name ************* ATT_CERT
name ************ attGIG08
name ************ attGIG07
name *********** attGIG01
name ************ attGIG05
name ************ attGIG03
name ********** attGIG09
name ************ attGIG04
name ************ attGIG06
name ************ attGIG02
name *********** attGIG15
name *********** attGIG11
name *********** attGIG16
name *********** attGIG12
name ********** attGIG10
name *********** attGIG18
name ********** attGIG13
name *********** attGIG17
name ********** attGIG14
name *********** attGIG19
name ************** CARDIOLOGY.RTR
name ************** RAMANI.RTR
name ************** SHAH.RTR
name ************ WWW.UPTODATE.COM
name ************* Cardinal195
name ************* Cardinal194
name ************* Cardinal177
name ************* Cardinal176
name ************* Cardinal145
name ************* Cardinal144
name ************* Cardinal133
name ************* Cardinal132
name ************* PPC.LAN
name *************** ppc.pix
name ************* RandF
name ************* Mammo
name ************* Ultrasound
name ************* CTScanner
name ************* Medinote
name ************ medinote1
name ************** medinote2
name *************** pacs.net
name *********** PhilipsSupport
name *********** Medent.VPN.net
name *************** PACS
name ************** VeriquestServer
name ************** TeleMed_1
name ************** nfuse.nlh.org
name ************** mail.nlh.org
name ************* DI.NET
name *********** DI.NETold
name *************** EUGENE3.dmz
name *************** NATHAN3.dmz
name ************* MilleniumPACS1
name ************* MilleniumPACS2
name *********** MilleniumPEER
name ************** RoomB description Digital Xray B
name ************** RoomA description Digital XrayA
name ************** TPC.vpn.rtr
name ************** TPC.net
name *********** EAGAN.net
name *************** MEDENT
name ************* MVOrtho
name ************ MVOrtho.peer
name *********** MVOrtho.net
name ************ BadDNS14
name ************ BadDNS13
name ************** TelevidED
name 192.168.185.203 Televid2 description TelevidED
name 192.132.237.9 TeleMedVT2
name ************ TeleMedVT1
name ************* TelemedVT5
name ************ TelemedVT4
name ************ TeleMedVT3
name *************** PACS_STORE2
name *************** PACS_STORE1
name *************** PACS_CACHE
name 66.210.121.2 CastlePeer
name 204.16.164.254 Nhawk1Peer
name 10.1.1.0 CastleNet
name 172.17.12.0 DrResnick.LAN
name *********** ResnickPACS2
name *********** ResnickPacs1
name 192.168.253.96 HLink
name **************5 NATHAN5.dmz
name ************** PETScanCT
name ************** mri
name ************** CatScan
name ************** INFINIA
name ************** XELERIS
name ************* NexTalk247
name ************* NexTalk246
name ************* NexTalk245
name ************* NexTalk244
name ************* NexTalk243
name ************* NexTalk242
name 74.40.187.198 HLink.Peer
name 207.135.141.69 NexTalk1
name 206.197.236.253 sftp.lifethc.org description Excellus File transfer
name ************ MedentRemote description Medent RemoteHost
name ************* Medent.Peer
name 4.78.136.166 Spantel.Peer
name 198.197.196.0 SpantelTest.Net
name 198.197.195.0 Spantel.Net
name ************* SpantelHL7.test
name ************* Spantel.Prod
name **************4 EUGENE4.dmz
name 192.168.184.73 P_PHA_WS1
name ************** eRXcenter2
name ************ eRXcenter3
name ************* eRXcenter1
name 192.168.249.0 JPC
name 24.97.24.242 JPC_MTK_VPN
name 24.97.145.251 NELLIS.peer
name 204.97.163.129 LeaderHFTPsite2
name ************ Medent.RPTS
name 12.145.95.237 LabCorp1
name 12.145.95.235 LabCorp2
name 12.39.198.49 LabCorpPeer
name 10.48.239.199 LabCorp3
name 10.48.236.66 LabCorpProd
name 10.48.236.65 LabCorpDev
name ************* NexTalkPrime
name ************** NexTalkSec
name **************6 EUGENE5.dmz
name **************3 NATHAN4.dmz
name 10.57.249.193 SMHApacs3
name 10.57.249.191 SMHApacs1
name 192.168.183.72 PACS_READ3
name ************** PACS_READ2
name ************** PACS_READ1
name 66.192.125.140 SMHApeer
name ************* SMHApacsSUBNET
name ********** SMHA.ps4
name ********** SMHA.ps3
name ********** SMHA.ps2
name ********** SMHA.ps1
name *********** SMHA.syn2
name *********** SMHA.syn1
name 74.102.18.9 SpantelTestPeer
name *********** SMHA.orpc2
name *********** SMHA.orpc1
name *********** SMHA.orpc3
name *************** P_PAT_REP3
name *************** P_PCC_BILL1
name *************** P_PAT_REP1
name ************** ivans.user5
name ************** ivans.user4
name ************** ivans.user3
name ************** ivans.user2
name ************** ivans.user1
name ************** Ivans.Gateway
name ************** ivans.user6
name ************ STpc.rtr
name *************** SteeleAve
name *********** smha.pacsed30
name ********* smha.pacrd06
name ************ TOGARM description Pat. Accts Ftp
name *************** P_PAT_MGR description Gus
name **************0 EUGENE6.dmz
name **************9 NATHAN6.dmz
name ************ Harland description Print Server Program
name *************** P_PAT_REP4
name *************** P_PAT_REP6
name ************** ivans.user.17
name *************** P_PAT_REP5
name **************7 Eugene1.dmz
name ************** ivans.user.18
name ************** eRxChicago
name ************* eRxDallas
name ************ SMHA.KPServer description Dicom server port 104
name ************ SMHA.read3
name ************ SMHA.read2
name ************ SMHA.read1
name *********** SMHA.read4
name ************* MilleniumPACS5
name ************* MilleniumPACS4
name ************* MilleniumPACS3
name *********** MilleniumPACSnat
name *************** SOPHOSEMAIL
name ************** NightHawkChicago.net
name ************ NightHawkChi.peer
name ************* NightHawkPhoenix.net
name *********** NightHawkPhx.peer
name ************** KonicaRdr3
name ************** KonicaRdr2
name ************** Konicardr1
name ************** KonicaJM
name ************** Ultrasound3
name ************** Ultrasound2
name ************** Ultrasound1
name *********** smha.mammo
name ************** PACS_TECH
name ************** MOMMO41
name ************** MAMMO40
name ************ JPC.Peer
name *************** PacsTEST
name *************** NATHAN1.dmz
name ************* MilleniumPEER2
name *************** CPC.vpn.peer
name ************** STAv.vpn.peer
name ************** BobShwajlyk
name *************** M_PPC_DOC3
name ************** Provation-out
name ************** SophosMailExt
name ********* Guest_Wireless description Guest  Wireless VLAN
name ************** AlbAdvImaging_NYOH_peer
name *********** AAI.125
name *********** AAI.124 description AlbanyAdvancedImaging PACS124
name *********** AAI.120
name *************** PACS_STORE144
name ************* impulse3
name ************** impulse1
name ************** impulse4
name ************** impulse2
name ************** Dictation2
name ************** Dictation1
name ************* MAGICG
name ************* MAGICF
name ************* MAGICB
name ************ eTrans.PEER
name ************* DrResnickNet description pNAT to ***********
name *************** ResnickPeer
name ************ TOG.peer
name ************ TheOutsourceGroup
name ********* TeleradIT.peer
name ************** TeleradIT_Millenium2
name ************** TeleradIT_Millenium1
name ********** MVOatJSC.net
name ************* MVOatJSC.peer
name ************** ORTIZ_LT
name ************* Dolby.Peer
name ************* HealthTouch.peer
name *********** RAMANI
name *************** SENTRYDS
name ************* ALLSCRIPTS.OUT.2
name ************** ALLSCRIPTS.OUT.1
name ************* ALBANYMED.IN.2
name *************** ALBANYMED.PEER
name ************ hixny.com_prod
name ************ hixny.com_integration
name *********** SENTRYDS.NET
name *************** ADT.SENTRYDS.COM.IN
name *************** SHEN
name ************* DHCP
name ************** MVORTHO_AMST_NEW
name ************* CASTLEDR
name ************* Saratoga_Care
name ************** Olympus.Insde
name ************** Olympus.Peer
name ************* eTrans.Mmodal..Peer
name ************ iPeople.Peer2
name ********* STUDENT_VLAN
name ************** PACS_OR3
name *************** PACS_OR1
name *********** MVO_MEDENT
name ************* PACS_VCE
name ************** P_PHA_WS2
name ************ Schumacher.Peer
name ************ FastChart.Inside
name *************** NATHAN7.dmz
name *************** EUGENE2.dmz
name *************** NATHAN2.dmz
name ************** Ellis.Peer
name ********** Ellis.inside
name ************** MEDENT2
name ************ Matt_DHCP
name ************** Matt_DHCP2
name ************ Matt_DHCP3
name ************** Matt_DHCP4
name *************** SMHA.Peer.RAD
name ************** Matt_DHCP5
name *************** MDI.dmz
name ************* NighthawkPhoenix.Internal
name ************ Matt_DHCP6
name ************** PATIENT_PORTAL
name ************ portal.e-imo.com description IMO portal
name ************ Matt_DHCP7
name *************** MEDENT_NAS
name *************** NATHAN16.dmz
name *************** NATHAN15.dmz
name *************** NATHAN14.dmz
name *************** NATHAN13.dmz
name *************** NATHAN12.dmz
name *************** NATHAN11.dmz
name *************** NATHAN10.dmz
name *************** NATHAN9.dmz
name *************29 MISCTX01
name *************28 Nathan16
name *************27 Nathan15
name *************25 Nathan13
name *************24 Nathan12
name ************** T_PRINTSHOP
name *************** BHAMMONS1
name ************* Matt_DHCP8
name ************* Matt_DHCP9
name ************* Matt_DHCP10
name *********** Matt_DHCP_11
name ************** Dolbey1
name ************** Dolbey2
name ************** Dolbey3
name ************ OLYMPUS_NEW.PEER
name ************** Matt
name *********** Olympus.Inside.New
name ************** PACS_OR2
name *************** PERTH_DEXA_LUNAR
name *************** securityoffice
name *************** pat_ser_mgr
name 192.168.187.112 P_SECURITY
name 192.168.180.200 p_rals_ims
name 192.168.186.110 p_ppc_pas2
name 192.168.184.75 P_PHA_WS3
name 192.168.184.251 P_MR_SCAN1
name 192.168.181.142 P_LAB_PATH1
name *************** P_HL_SEC
name ************** P_GPC_TRANS2
name *************** P_HEALTH_01
name ************** P_ENV_DIR
name *************** p_cpc_scan
name *************** M_ADM_NXTK1
name *************** LFHP3
name ************** P_BENEFITS
name *************** NLHTEST01
name ************** NLHENDO02
name ************** FOODSVC
name ************** NLHENDO01
name *************00 IMO_SERVER
name *************** p_mis_netadmin
name *************** CITRIX_STOREFRONT
name *************** NETSCALERSUBNETIP
name *************51 EUGENE07
name *************52 EUGENE08
name *************** STOREFRONT.DMZ
name ************** NLHDC01
name *************** NLHDC02
name *************** p_mis_sysadm
name *************** NETSCALER.WEB
name *************54 NETSCALER.INTERNAL
name *************** NETSCALER.VPX
name *************** easyeeg
name ************** MDITEST
name *************** MDILIVE description MDI Interface Engine
name ************* webservices.hixny.com
name ************* integration.hixny.com
name *********** AAI.51
name *********** AAI.50
name *********** AAI.46
name ************** EEG_MACHINE
name *********** AAI.52
name *************** P_MIS_TECH
name ************** MOX.NLH.ORG
name ************* Hixny.com
name ************ Direct.Hixny.Com
name *************** Hixny.net
name ************* Healthstream.SMPT.Peer
name ************* Hixney.net_2
name ************* NELLIS_OUTDATED
name ************** PACS_NEW
name ************** PACS_NEW1
name ************** p_pacs_cdburn
name ************ ALVAREZ.INTERNAL
name *************** ALBANYMED.IN description PACS
name ************** MOBILE.NLH.ORG
name *************** NLH-WEB01-WS01
name *************** ALVAREZ_NAZ
name *************** ALVAREZ_MEDENT
name *************** ALVAREZ_FAX
name *************** dbserver.statrad.com
name *************** statrad.hl7.test
name *************** P_PAT_FIN
name *************26 NATHAN06
name *************** CITRIXFS02
name *************** NLHCA
name *************** NLHPROV_TEST
name *************** NLHPROV_ORACLE
name *************** P_DI_NUMED
name *************2 ENDOWORKS03
name *************1 ENDOWORKS02
name *************0 ENDOWORKS01
name *************** p_mis_netadmin2
name *********** SMHA.read6
name *********** SMHA.read5
name *********** SHMA.read7
name *********** SMHA.read8
name ************ SMHA.read9
name *************** PACS_DI
name *********** SMHA.read10
name *************** P_CISCO_01
name ************** remote.nlh.org
name ************** Connectplus
name ************** HOSP.NY.MILLEN1
name *************** sophosweb
name ************** DIRECT.NLH.ORG description USED FOR PACE AND OTHERS
name ************** MEDENT_REPORT
name ************ NUVODIA_PACS_************-23
name ************** NUVODIA_PACS_**************-28
name ************ FASTCHART.PEER.NEW
name ************** BACKLINE.LDAP.INTERNAL
name *********** MBMS.MILLENIUMBILLING.EXTERNAL.PEER
name ************ QUEST.VPN.EXTERNAL.PEER.2
name ************** QUEST.VPN.EXTERNAL.PEER.1
name ************** BACKLINE.VPN.EXTERNAL.PEER
name ************ SPCC.VPN.EXTERNAL.PEER
name ************* CHC.VPN.EXTERNAL.PEER
name ************** HIXNY.VPN.EXTERNAL.PEER
name ************** SCHUMACHER.VPN.EXTERNAL.PEER
name ************ HEALTHOUCH.VPN.EXTERNAL.PEER
name ************** NYOH.VPN.EXTERNAL.PEER
name ************* SENTRYDS.VPN.EXTERNAL.PEER
name *********** MVORTHOR.VPN.EXTERNAL.PEER
name *********** SMHA.RAD.VPN.EXTERNAL.PEER
name ************ STRATEGIC.VPN.EXTERNAL.PEER
name ************* MEDENT.VPN.EXTERNAL.PEER
name ************ BACKLINE.VPN.EXTERNAL.PEER.LDAP
name *************** NLHEXCHANGE.NLH.ORG
name *************** SMHA.RAD.EXTERNAL.PEER
name ************** NLHDHCP01.NLH.ORG
no mac-address auto
ip local pool VPN2.Pool **************1-*************45 mask *************

!
interface GigabitEthernet0/0
 description External Port TWC
 nameif outside
 security-level 0
 ip address 208.125.81.163 *************24 
!
interface GigabitEthernet0/1
 nameif inside
 security-level 100
 ip address 192.168.178.2 ************* 
!
interface GigabitEthernet0/2
 description DMZ Network
 nameif DMZ
 security-level 10
 ip address 192.168.250.1 ************* 
!
interface GigabitEthernet0/3
 nameif Guest
 security-level 5
 ip address ********** ************* 
!
interface GigabitEthernet0/4
 nameif Vendor
 security-level 5
 ip address ********** ************* 
!
interface GigabitEthernet0/5
 shutdown
 no nameif
 no security-level
 no ip address
!
interface GigabitEthernet0/6
 shutdown
 no nameif
 no security-level
 no ip address
!
interface GigabitEthernet0/7
 shutdown
 no nameif
 no security-level
 no ip address
!
interface Management0/0
 management-only
 no nameif
 no security-level
 no ip address
!
!
time-range TimeRange
!
boot system disk0:/asa9-12-3-9-smp-k8.bin
boot system disk0:/asa964-smp-k8.bin
ftp mode passive
clock timezone EST -5
clock summer-time EDT recurring
dns domain-lookup inside
dns server-group DefaultDNS
 name-server NLHDC01 inside
 domain-name nlh.org
object network RadSaratoga
 host 24.97.17.90
object network RadAmsMem
 host 24.97.27.74
object network RadStMarys
 host 24.97.29.57
object network RadSeton
 host 64.97.15.123
object network RadBellevue
 host *************
object network CITRIXFS02
 host ***************
object network XENAPP30
 host *************52
object network MAGIC_C-iDRAC
 host **************0
object network NLHNAS12
 host 192.168.177.178
object network MAGIC-D_iDRAC
 host **************5
object network XENAPP02
 host *************51
object network NLHNAS11
 host 192.168.177.179
object network MAGIC_A-iDRAC
 host 192.168.177.177
object network MAGIC_E-iDRAC
 host **************2
object network MAGIC_D-NEWiSCSI
 host **************3
object network UNITY-SDC_iSCSI
 host **************4
object network NLH-WEB01-WS01
 host ***************
object network MAGICA
 host 192.168.176.1
object network MAGICC
 host *************
object network MAGICD
 host *************
object network MAGICE
 host *************
object network MAGICB
 host *************
object network MAGICF
 host *************
object network MAGICG
 host *************
object network DICTATION02
 host **************
object network MDILIVE.NLH.ORG
 host ***************
object network P_FS_SEC
 host 192.168.184.143
object network CTScanner
 host *************
object network Mammo
 host *************
object network RandF
 host *************
object network PetLinks1
 host **************
object network PetLinks2
 host **************
object network MAGICD3_DRAC
 host 192.168.178.50
object network LIEBERT.2FL
 host **************
object network Cardinal132
 host *************
object network Cardinal133
 host *************
object network Cardinal144
 host *************
object network Cardinal145
 host *************
object network Cardinal176
 host *************
object network Cardinal177
 host *************
object network Cardinal194
 host *************
object network Cardinal195
 host *************
object network Medinote
 host *************
object network medinote2
 host **************
object network medinote1
 host ************
object network NATHAN3.dmz
 host ***************
object network NATHAN5.dmz
 host **************5
object network NATHAN1.dmz
 host ***************
object network NATHAN4.dmz
 host **************3
object network NATHAN9.dmz
 host ***************
object network NATHAN10.dmz
 host ***************
object network NATHAN11.dmz
 host ***************
object network MilleniumPACS2
 host *************
object network MilleniumPACS1
 host *************
object network MilleniumPACS3
 host *************
object network MilleniumPACS4
 host *************
object network MilleniumPACS5
 host *************
object network NLHEXCHANGE.NLH.ORG
 host ***************
object network NLH-ISWEB_VIP_NETSCALER1
 host **************1
object network TeleMedVT3
 subnet ************ *************
object network TelemedVT4
 subnet ************ *************
object network TelemedVT5
 subnet ************* *************
object network TeleMedVT1
 subnet ************ *************
object network PACS
 host ***************
object network PACS_CACHE
 host ***************
object network PACS_STORE1
 host ***************
object network PACS_STORE2
 host ***************
object network PACS_STORE144
 host ***************
object network ResnickPacs1
 host ***********
object network ResnickPACS2
 host ***********
object network TeleRadPC
 host *************
object network CatScan
 host **************
object network PERTH_MRI
 host **************
object network PETScanCT
 host **************
object network XELERIS
 host **************
object network INFINIA
 host **************
object network D5000
 host **************
object network Ultrasound1
 host **************
object network Ultrasound2
 host **************
object network Ultrasound3
 host **************
object network KonicaJM
 host **************
object network Konicardr1
 host **************
object network KonicaRdr2
 host **************
object network KonicaRdr3
 host **************
object network PACS_NEW
 host **************
object network US_LOGI_E9
 host **************
object network NexTalk242
 host *************
object network NexTalk243
 host *************
object network NexTalk244
 host *************
object network NexTalk245
 host *************
object network NexTalk246
 host *************
object network NexTalk247
 host *************
object network NexTalkPrime
 host *************
object network NexTalkSec
 host **************
object network Medent.VPN.net
 subnet *********** *************
object network Spantel.Prod
 host *************
object network SpantelHL7.test
 host *************
object network eRXcenter2
 host **************
object network eRXcenter3
 host ************
object network eRXcenter1
 host *************
object network eRxChicago
 host **************
object network eRxDallas
 host *************
object network MedentRemote
 host ************
object network Medent.RPTS
 host ************
object network STpc.rtr
 host ************
object network spc.rtr
 host ************
object network ppc.pix
 host ***************
object network SMHA.ps1
 host **********
object network SMHA.ps2
 host **********
object network SMHA.ps3
 host **********
object network SMHA.ps4
 host **********
object network SMHA.syn1
 host ***********
object network SMHA.syn2
 host ***********
object network SMHA.orpc1
 host ***********
object network SMHA.orpc2
 host ***********
object network SMHA.orpc3
 host ***********
object network SMHA.read1
 host ************
object network SMHA.read2
 host ************
object network SMHA.read3
 host ************
object network SMHA.KPServer
 host ************
object network SMHA.read4
 host ***********
object network smha.mammo
 host ***********
object network smha.pacsed30
 host ***********
object network smha.pacrd06
 host *********
object network SMHApacsSUBNET
 subnet ************* *************40
object network SMHA.read5
 host ***********
object network SMHA.read6
 host ***********
object network SHMA.read7
 host ***********
object network SMHA.read8
 host ***********
object network SMHA.read9
 host ************
object network SMHA.read10
 host ***********
object network SMHA.Synapse.Dest
 host **********
object network P-DI-MGR
 host **************
object network PACS_READ3_NEW
 host **************
object network P_CIO1
 host **************0
object network P_DI_NUMED
 host ***************
object network MAMMO40
 host **************
object network MOMMO41
 host **************
object network philipstst
 host ***************
object network pacs.net
 subnet *************** *************40
object network NLHSP19WEB.NLH.ORG
 host **************
object network PACS_VCE
 subnet ************* *************
object network P_PAT_REP1
 host ***************
object network P_PAT_REP5
 host ***************
object network P_PCC_BILL1
 host ***************
object network P_PAT_REP6
 host ***************
object network P_PAT_REP3
 host ***************
object network P_PAT_REP4
 host ***************
object network SOPHOSEMAIL
 host ***************
object network SOPHOSWEB
 host ***************
object network NATHAN6.dmz
 host **************9
object network pacs.net_1
 subnet *************** ***************
object network ORTIZ_LT
 host **************
object network p_mis_netadmin
 host ***************
object network PACS_OR3
 host **************
object network PACS_OR1
 host ***************
object network PACS_OR2
 host **************
object network PACS_DI
 host ***************
object network NLHDC1_IPMI
 host 192.168.177.108
object network NLHDC2_IPMI
 host 192.168.177.109
object network AAI.120
 host ***********
object network AAI.124
 host ***********
object network AAI.125
 host ***********
object network AAI.52
 host ***********
object network INTERLACE
 host **************
object network NLHUTILITY
 host **************
object network NLHTEST01-NIC2
 host 192.168.178.151
object network BPC-UPS
 host **************
object network ALBANYMED.IN.2
 host *************
object network ALBANYMED.IN
 host ***************
object network hixny.com_integration
 host ************
object network hixny.com_prod
 host ************
object network webservices.hixny.com
 host *************
object network Olympus.Inside.New
 subnet *********** *************
object network MDITEST
 host **************
object network MEDENT
 host ***************
object network MEDENT03
 host **************
object network NLH-ISWEB_VIRTUALIP
 host ***************
object network NLH-ISWEB_VIRTUALIP_NETSCALER2
 host ***************
object network MEDENT05
 host **************
object network P_PHA_WS3
 host 192.168.184.75
object network P_PHA_WS2
 host **************
object network P_MR_SCAN1
 host 192.168.184.251
object network easyeeg
 host ***************
object network VENUE50_p_pacs_cdburn
 host **************
object network integration.hixny.com
 host *************
object network Hixney.net_2
 host *************
object network CITRIX_STOREFRONT
 host ***************
object network P-IT-MGR
 host ***************
object network NETSCALER.VPX
 host ***************
object network NETSCALER.WEB
 host ***************
object network NETSCALERSUBNETIP
 host ***************
object network NLHDC01.NLH.ORG
 host **************
object network NLHDC02.NLH.ORG
 host ***************
object network P_CISCO_01
 host ***************
object network p_mis_netadmin2
 host ***************
object network Direct.Hixny.Com
 host ************
object network Healthstream.SMPT.Peer
 host *************
object network Hixny.net
 host ***************
object network Hixny.com
 host *************
object network statrad.hl7.test
 host ***************
object network P_PAT_FIN
 host ***************
object network NLHENDO01
 host **************
object network NLHENDO01.NLH.ORG
 host *************0
object network ENDOWORKS02
 host *************1
object network ENDOWORKS03
 host *************2
object network P_IS_PACS
 host 192.168.191.227
object network retsolinc2.com
 host 64.250.235.186
object network retsolinc3.com
 host 74.113.61.50
object network SophosMailExt
 host **************
object network speculator
 subnet *************** *************24
object network GEserviceNET
 subnet ********* 255.255.0.0
object network IRIS
 host 10.0.6.30
object network Mill.PACS.NET
 subnet ********** 255.255.0.0
object network smtp.biz.rr.com
 host 24.30.201.200
object network Hypertype
 host ************
object network MVP
 host ************
object network LeaderHFTPsite
 host 204.97.174.6
object network LeaderHFTPsite2
 host 204.97.163.129
object network stentor.com
 host *************
object network TOGARM
 host ************
object network Infotrak
 host ***************
object network sftp.lifethc.org
 host 206.197.236.253
object network DI.NET
 subnet ************* ***************
object network TeleVideo1
 host ***************
object network Televid2
 host 192.168.185.203
object network STUDENT_VLAN
 subnet ********* *************
object network CONNECTPLUS01
 host **************
object network VeriquestPC
 host ***************
object network VeriquestSite
 host ***********
object network PATIENT_PORTAL_1
 host **************
object network questlab
 subnet 156.30.21.128 ***************
object network HYPER-_REPLICA_BROKER
 host **************
object network iPEOPLEremote
 subnet ************ *************
object network Sodexho
 host 6************
object network Provation-out
 host **************
object network VeriquestServer
 host **************
object network LAN
 subnet ************* *************
object network Harland
 host ************
object network IMO_2
 host **************
object network WWW.UPTODATE.COM
 host ************
object network XENAPP22
 host **************
object network HYPER-V_CLUSTER
 host 192.168.176.12
object network PATIENTPORTAL.EXTERNAL
 host **************
object network remote.nlh.org
 host **************
object network mail.nlh.org
 host **************
object network DIRECT.NLH.ORG
 host **************
object network TeleMed_1
 host **************
object network MDI.dmz
 host ***************
object network NLHCISCO
 host **************
object network RALSplusLAN
 subnet ************* *************
object network PhilipsSupport
 subnet *********** ***************
object network STRAT_SOL.NET.INTERNAL1
 subnet *********** *************
object network MVOrtho.net
 subnet *********** *************
object network LAN_1
 subnet ************* *************
object network LabCorp3
 host 10.48.239.199
object network LabCorpDev
 host 10.48.236.65
object network LabCorpProd
 host 10.48.236.66
object network MilleniumPACSnat
 subnet *********** *************
object network TheOutsourceGroup
 host ************
object network TeleradIT_Millenium1
 host **************
object network TeleradIT_Millenium2
 host **************
object network MVOatJSC.net
 subnet ********** *************
object network SENTRYDS.NET
 subnet *********** *************48
object network FastChart.Inside
 host ************
object network Ellis.inside
 host **********
object network STATRAD.DR.SVR
 host *************
object network SENTRYDS
 subnet *************** *************40
object service obj-tcp-eq-80
 service tcp destination eq www 
object service obj-tcp-eq-15002
 service tcp destination eq 15002 
object service obj-tcp-eq-15331
 service tcp destination eq 15331 
object service obj-tcp-eq-3389
 service tcp destination eq 3389 
object service obj-tcp-eq-2222
 service tcp destination eq 2222 
object service obj-tcp-eq-6544
 service tcp destination eq 6544 
object service obj-tcp-eq-2020
 service tcp destination eq 2020 
object service obj-tcp-eq-23
 service tcp destination eq telnet 
object service obj-tcp-eq-15031
 service tcp destination eq 15031 
object service obj-tcp-eq-5631
 service tcp destination eq pcanywhere-data 
object service obj-udp-eq-15032
 service udp destination eq 15032 
object service obj-udp-eq-5632
 service udp destination eq pcanywhere-status 
object service obj-tcp-eq-25
 service tcp destination eq smtp 
object service obj-tcp-eq-443
 service tcp destination eq https 
object service obj-tcp-eq-55443
 service tcp destination eq 55443 
object service obj-tcp-eq-3401
 service tcp destination eq 3401 
object service obj-tcp-eq-53048
 service tcp destination eq 53048 
object service obj-tcp-eq-53372
 service tcp destination eq 53372 
object network NLHTEST01
 host ***************
object service obj-tcp-eq-53050
 service tcp destination eq 53050 
object service obj-tcp-eq-53374
 service tcp destination eq 53374 
object service obj-tcp-eq-21
 service tcp destination eq ftp 
object network NLH.ORG.EXTERNAL.FORMS
 host **************
object network pacs.net-01
 subnet *************** ***************
object network LAN-01
 subnet ************* *************
object network obj-************
 host ************
object service obj-tcp-source-eq-80
 service tcp source eq www 
object service obj-tcp-source-eq-15002
 service tcp source eq 15002 
object service obj-tcp-source-eq-3389
 service tcp source eq 3389 
object service obj-tcp-source-eq-15331
 service tcp source eq 15331 
object service obj-tcp-source-eq-2222
 service tcp source eq 2222 
object service obj-tcp-source-eq-6544
 service tcp source eq 6544 
object service obj-tcp-source-eq-2020
 service tcp source eq 2020 
object service obj-tcp-source-eq-23
 service tcp source eq telnet 
object service obj-tcp-source-eq-5631
 service tcp source eq pcanywhere-data 
object service obj-tcp-source-eq-15031
 service tcp source eq 15031 
object service obj-udp-source-eq-5632
 service udp source eq pcanywhere-status 
object service obj-udp-source-eq-15032
 service udp source eq 15032 
object service obj-tcp-source-eq-25
 service tcp source eq smtp 
object service obj-tcp-source-eq-443
 service tcp source eq https 
object service obj-tcp-source-eq-55443
 service tcp source eq 55443 
object service obj-tcp-source-eq-3401
 service tcp source eq 3401 
object service obj-tcp-source-eq-53372
 service tcp source eq 53372 
object service obj-tcp-source-eq-53048
 service tcp source eq 53048 
object service obj-tcp-source-eq-53374
 service tcp source eq 53374 
object service obj-tcp-source-eq-53050
 service tcp source eq 53050 
object service obj-tcp-source-eq-21
 service tcp source eq ftp 
object network obj-***********
 host ***********
object network obj-***********
 host ***********
object network obj-************
 host ************
object network obj-***********
 host ***********
object network obj-************
 host ************
object network MilleniumPACSnat-*************
 range *********** *************
object network obj-************
 host ************
object network obj-*************-*************
 range ************* *************
object network obj_any
 subnet 0.0.0.0 0.0.0.0
object network Ellis.Peer.New
 host **********
object network HEALTHTOUCH.PEER.INTERNAL.1
 host ************
object network Medent.Peer.New.
 host *************
object network HIXNY.MBMS.MILLENIUMBILLING.PEER
 host ***********
object network HIXNY.MBMS.MILLENIUMBILLING.INTERNAL1
 host ***************
object network MCKESSON.MC.PHARM
 host *************5
object network newsync3.mkesson.com
 host **************
object network obj-0.0.0.0
 host 0.0.0.0
object network obj_any-03
 subnet 0.0.0.0 0.0.0.0
 description DMZ ANY
object network SMHA.pacs1
 host **********
object network SMHA.pacs2
 host ***********
object network SMHA.pacs3
 host **********
object network PACS.VCE1
 host **************
object network PACS.VCE2
 host ***************
object network PACS.VCE3
 host ***************
object network PACS.VCE4
 host ***************
object network MEDENT_NAS_INTERNAL
 host ***************
object network HEALTHTOUCH01
 host **************
object network HEALTHTOUCH02
 host **************
object network PDX.Internal
 host **************
object network PDX.External
 host *************
object network HIXNY.PEER.NEW
 host **************
object network HIXNY.INTERNAL1
 host ************
object network XCHANGEWORX.PEER
 host **************
object network NETSCALER.NLHRESTAPI
 host ***************
 description NAT ADDRESS FOR NLH-RESTAPI
object network NUVODIA_NETWORK_3
 subnet ************* *************48
object network NUVODIA_VPN_NLH_PEER1
 host **************
object network NUVODIA_VPN_NLH_PEER2
 host **************
object network FIREPOWER_VM_ESXI
 host **************
object network SMHA.READ.10
 host ***********
object network ESRS_EMC_VIRTUAL_APPLIANCE
 host **************
object network NLH-ISWEB.INTERNAL
 host **************
object network NLH-ISWEB.DMZ
 host **************0
object network RESTFULAPI.DMZ
 host ***************
object network NYOH.INTERNAL.1
 host ***********
object network NYOH.INTERNAL.2
 host ************
object network NYOH.EXTERNAL.PEER
 host **************
object network NUVODIA.INTERNAL.1
 host **************
object network NUVODIA.INTERNAL.2
 host **************
object network P_MIS52_DMZ
 host **************4
object network MDITEST_SENDTRYDS
 host ***********
object network XENAPP25
 host **************
object network skype.nlh.org_external
 host **************
 description External Skype Address
object network st_netadmin
 host ***************
object network SMHA.RAD.EXTERNAL
 host ***********
object network MVO_AMST_PEER_NEW
 host ***********
object network GUEST_INTERFACE_EXTERNAL
 host **************
object network GUEST_WLAN_NAT
 subnet ********** *************
object network GUEST_NETWORK
 subnet ********** *************
object network VENDOR_WLAN_NAT
 subnet ********** *************
object network VENDOR_EXTERNAL_INTERFACE
 host **************
object network p_mis_netadmin.dmz
 host **************3
object network NLH-ISWEB.DMZVR
 host **************3
object network CREDITCARD_CAFE_EXTERNAL1
 subnet ************* *************40
object network CREDITCARD_CAFE2_EXTERNAL2
 subnet 178.255.82.64 *************24
object network CREDITCARD_CAFE_EXTERNAL
 subnet 199.66.200.32 *************24
object network AMC.PACS.NEW
 host 10.31.4.151
object network BRIAN_DHCP
 host 24.194.105.160
object network BPC.External
 host 208.105.166.66
object network STRAT_SOL.NET.INTERNAL2
 subnet 192.0.103.0 *************
object network P_MIS_CISCOMON
 host **************4
object network XENAPP17
 host 192.168.178.86
object network XENAPP18
 host 192.168.178.85
object network XENAPP19
 host 192.168.178.87
object network P_MIS52.WAYNE
 host 192.168.178.110
object network NETADMIN.DMZ.TEST
 host **************1
object network EUGENE10
 host 192.168.178.108
object network MEDITECHAPIVIP1
 host **************0
object network MEDITECHAPIVIP2
 host **************2
object network StratSolution.Peer
 host ************
object network P_PHA_PDX1
 host 192.168.184.105
object network NLHPRTG01
 host **************
object network RCARE-SERVER
 host *************6
object network NLHDMZ01_SWITCH
 host 192.168.250.254
object network XENAPP01
 host **************
 description Delivery Controller
object network PRTG.NLH.ORG.EXTERNAL
 host **************
object network BACKLINE.VPN.PEER
 host **************
object network UNITEDLABNETWORK.VPN.PEER
 host 67.231.240.202
object network NETWORK_OBJ_**************
 host **************
object network BACKLINE.LDAP.INTERNAL
 host **************
object network MEDENT.NIMBLE.INSIDE.1
 host 192.168.182.25
object network MEDENT.NIMBLE.OPENVPN.OUTSIDE.1
 host 66.152.110.67
object network MEDENT.NIMBLE.OPENVPN.OUTSIDE.2
 host ***********
object network ROBOT_GE_VOT_TRAIN
 host 192.168.184.18
object network BILL_BAIRD
 host *************
object network EXPANSE_VLAN1
 subnet ********** *************
object network EXPANSE_VLAN2
 subnet ********** *************
object network EXPANSE_VLAN3
 subnet 10.10.21.0 *************
object network EXPANSE_VLAN4
 subnet ********** *************
object network LUCIUS29-iDRAC
 host *************2
object network DOLBEY
 host *************26
object network DOLBEYTEST
 host *************0
object network NLH_DCDS_9300s
 host ***************
object network Schumacher.Inside1.new.ADTPROD
 host 207.229.88.25
object network Schumacher.Inside2.new.ADTTEST
 host 207.229.88.26
object network Schumacher.VPN.Peer.New
 host **************
object network MEDENT-EXPORT
 host *************6
object network QUEST.VPN.PEER.2
 host ************
object network QUEST.VPN.EXTERNAL.2
 subnet 66.42.179.80 *************40
object network QUEST.VPN.INTERNAL.2
 host ***********
object network Wayne
 host 192.168.178.110
object network HIXNY.PEER.INTERNAL.TEST
 host 172.21.0.239
object network HIXNY.PEER.INTERNAL.PROD
 host 172.21.0.127
object network NLHSP19OFCWEB.NLH.ORG
 host **************8
object network PATIENTPORTAL.DMZ
 host 192.168.250.33
object network mtrestexpapis-live01.nlh.org.external
 host **************
object network mtrestexpapis-test01.nlh.org.external
 host **************
object network mtrestexpapis-test01.nlh.org.DMZ
 host 192.168.250.32
object network mtrestexpapis-live01.nlh.org.DMZ
 host 192.168.250.31
object service PATIENTPORTALTEST
 service tcp source eq 8443 
object network CHANGE.HEALTHCARE.EXTERNAL.PEER
 host *************
object network CHANGE.HEALTHCARE.EXTERNAL.IP1.PROD
 host ***************
object network CHANGE.HEALTHCARE.EXTERNAL.IP2.TEST
 host ***************
object network NLI.T.BG01
 host ***********
object network CHC.EXTERNAL.1
 host ***************
object network CHC.EXTERNAL.2
 host ***************
object network NLI-T-BG01.CHC.NAT
 host ************
object network MDILIVE.CHC.NAT
 host ************
object network MDITEST.CHC.NAT
 host ************
object network NLI-T-BG01
 host ***********
object network NLHFTP01.NLH.ORG
 host ***************
object network SR_STACK_01
 host **************
object network NLI-BG01.nlh.org
 host ***********
object network NLI-BG04.CHC.NAT
 host ************
object network CHC.OPTUM.NAT.INTERNAL.SUB
 range *************** ***************
object service DIRECT.MESSAGE.TEST.NLI
 service tcp source eq 53101 
object service DIRECT.MESSAGE.LIVE.NLI
 service tcp source eq 53100 
object network NLI-BG04
 host ***********
object network CHC.EXTERNAL.3
 host ***************
object network NYOH.INTERNAL.3
 host ***********
object network WEBSSO.MEDITECH.COM
 host **************
object network WEBSSO2FA.MEDITECH.COM
 host *************
object network HIXNY.INTERNAL.PUSH_SERVER
 host ************
object network HIXNY.INTERNAL.TESTHUB
 host ************
object network FIRECALL_JSC
 host **************
object network FIRECALLSYSTEM_ENDPOINTS1
 host ************
object network FIRECALLSYSTEM_ENDPOINTS2
 host ************
object network BACKLINE.VPN.PEER2
 host ************
object network BACKLINE.LDAP.INTERNAL2
 host *************
object network NETWORK_OBJ_*************
 host *************
object network BANDWIDTH_TEST
 host ***************
object network P_IS_1
 host **************
object network P_IS_3
 host **************
object network P_IT_COOR
 host ***************
object network P_IT_TECH1
 host *************
object network HIRAM
 host **************
object network RYAN
 host **************
object network NICK
 host **************
object network IT_TEST
 host **************6
object network P-BOARDROOM1
 host **************
object network LUCIUS08
 host *************
object network LUCIUS21-iLO
 host *************
object network NLHADMINCENTER
 host **************
object network XENAPP24
 host **************
object network SQL01.NLH.ORG
 host *************3
object network FAXSERVER.NLH.ORG
 host *************5
object network NLHFUSION
 host **************
object network BACKUPEXEC01
 host *************7
object network ARCHIVE.NLH.ORG
 host *************1
object network PRINT
 host *************2
object network NLHBACKUP
 host *************3
object network INTERLACETEST
 host *************6
object network NLHMONITOR01
 host *************7
object network SANPHNHM
 host *************8
object network CENTRALINK_BCR
 host *************0
object network CENTRALINK_VISTA2
 host *************1
object network CENTRALINK_VISTA1
 host *************2
object network CENTRALINK_LCM
 host *************3
object network CENTRALINK
 host *************4
object network LUCIUS31-iDRAC
 host *************5
object network XENAPP21
 host ***************
object network NLHCITRIXGATEWAY
 host ***************
object network ST_NETADMIN2
 host ***************
object network DR_CECIL
 host ***************
object network P_IS_RAMANI
 host ***************
object network US_LOGU_E9_2
 host **************
object network NYOH.INTERNAL.4
 host **************
object network NLI-BG13
 host ***********
object service NLI-BG13-FTP
 service tcp source eq 1433 destination eq 1433 
object network NLHTESTMOBILE
 host ***************
 description Jumpbox
object network NOVA.NLH.ORG.EXTERNAL
 host **************
 description interlace
object service W32.MYDOOM.OLD
 service udp source eq 3127 destination eq 3127 
object network BANDWIDTH_TEST_2
 host ***************
object network NETWORK_OBJ_***************
 host ***************
object network NETWORK_OBJ_************
 host ************
object network Barracuda.Web.NLH.Internal
 host ***************
object network Barracuda.Email.NLH.Internal
 host ***************
object network HEALTHTOUCH.EXTERNAL.PEER
 host *************
object network HEALTHTOUCH.PEER.INTERNAL.2
 host *************
object network NETWORK_OBJ_*************
 host *************
object network DMZ_TEST.NLH.ORG
 host **************
object network DUOTEST.NLH.ORG
 host **************
object network DUOTEST.NLH.ORG.DMZ
 host **************
object service GREYCASTLE_VPN
 service udp source eq 51820 destination eq 51820 
object network BARRACUDA.EMAIL.INSIDE
 host ***************
object service IMO_CLOUD
 service tcp destination eq 42045 
 description Cloud.Connection.Port
object network NLH.CORE.INTERNAL
 host **************
object network DCDS.CORE.INTERNAL
 host ***************
object network GPC_STACK
 host **************
object network NLHSSI
 host **************
 description SSI SERVER FOR PFS
object network NLHBRAUNPUMPS.INTERNAL
 host ***************
object network NLHBRAUNPUMPS.EXTERNAL
 host *************
object network P-ITMGR
 host ***************
object network MEDIVATOR66838147
 host **************
object network MEDIVATOR66838143
 host **************
object network AMC.VPN.PEER.NEW
 host ***************
object network NUVODIA.INTERNAL.NEW.1
 host ************
object network NUVODIA.INTERNAL.NEW.2
 host ************
object network ULN.VPN.INTERNAL
 host ************
object network CISCOPRIME.INTERNAL
 host ***************
object network CISCOPRIMEINF
 host ***************
object network MIS_TEST2
 host ***************
object network CISCONMON
 host ***************
object network SYSLOGSERVER
 host **************
object network NOVA-QIE.INTERNAL
 host **************
 description interlace
object service NOVA-8070-TCP
 service tcp source eq 8070 destination eq 8070 
object network NOVA.INTERLACE.PEER.EXTERNAL
 host 20.252.34.216
object network WLC1
 host 192.168.183.101
object network sendgrid.net.virus
 host 159.183.73.151
object network NOVA.INTERLACE.PEER.EXTERNAL2
 host 52.188.157.76
object network love.explorethebest.com.spam.2
 host 51.158.26.81
object network love.explorethebest.com.spam.1
 host 45.129.14.31
object network love.explorethebest.com.spam.3
 host 45.129.14.30
object network CISCO.WSA.INTERNAL
 host 192.168.177.126
object network HARRIET.NLH.ORG
 host 192.168.177.103
object network LUCIUS32.NLH.ORG
 host 192.168.176.104
object network LUCIUS10A.NLH.ORG
 host *************08
object network LUCIUS19B.NLH.ORG
 host 192.168.177.12
object network WILLYWONKA.NLH.ORG
 host 192.168.177.17
object network NLHSYN01.NLH.ORG
 host *************9
object network NLHSYN02.NLH.ORG
 host 192.168.177.73
object network NLHSYN03.NLH.ORG
 host 192.168.177.75
object network NLHSYN04.NLH.ORG
 host 192.168.177.78
object network NLHSP19APP.NLH.ORG
 host 192.168.177.89
object network LUCIUS18C.NLH.ORG
 host 192.168.177.6
object network LUCIUS19C.NLH.ORG
 host 192.168.177.14
object network LUCIUS14.NLH.ORG
 host *************8
object network ACRONIS.EXTERNAL.RANGE1
 range 185.151.160.1 185.151.160.127
object network ACRONIS.EXTERNAL.RANGE2
 range 162.244.6.1 162.244.6.254
object network LUCIUS26D.NLH.ORG
 host *************5
object network DDPC.FIREALARM
 host 192.168.179.26
object network LUCUIS16B.NLH.ORG
 host *************32
object network LUCIUS17B.NLH.ORG
 host 192.168.177.179
object network LUCIUS19A.NLH.ORG
 host 192.168.177.10
object network SUMMIT.NLH.ORG
 host 192.168.178.118
object network LUCIUS25A.NLH.ORG
 host **************
object network ONEVIEW.NLH.ORG
 host 192.168.177.125
object network DR1.NLH.ORG
 host 192.168.177.123
object network LUCIUS26B.NLH.ORG
 host *************1
object network NLHBACKUP02.NLH.ORG
 host **************5
object network KRONOSNEW.NLH.ORG
 host 192.168.177.88
object network SMHA.RAD.EXTERNAL.NEW
 host ***************
object network BARRACUDA.CLOUD.EXTERNAL
 range 209.222.80.0 209.222.87.254
object network ACRONIS.EXTERNAL.RANGE3
 subnet 162.244.6.0 *************
object network ACRONIS.EXTERNAL.RANGE4
 subnet 185.151.160.0 *************
object network BARRACUDA.LDAP.EXTERNAL.PEER.1
 host 35.170.131.81
object network BARRACUDA.LDAP.EXTERNAL.PEER.2
 host 54.156.244.63
object network BARRACUDA.LDAP.EXTERNAL.PEER.3
 host 54.209.169.44
object network REYHEALTH.EXTERNAL.EXTERNAL.1
 host 206.227.220.30
object network REYHEALTH.EXTERNAL.EXTERNAL.2
 host 206.227.216.30
object service REYHEALTH.EXTERNAL.PORT1
 service tcp source eq 18009 destination eq 18009 
object service REYHEALTH.EXTERNAL.PORT2
 service tcp source eq 18005 destination eq 18005 
object network CHC.OPTUM.EXTERNAL.VPN.PEER
 host **************
object network LUCIUS18D.NLH.ORG
 host 192.168.177.8
object network STREAMTASK.NLH.ORG
 host 192.168.178.156
object network GPSUPPORT.VPN.EXTERNAL.PEER
 host *************
object network GESUPPORT.INTERNAL.NET
 subnet ********* 255.255.0.0
object network DI.AWSERVER
 host *************22
object network DI.AWSERVER.ILO
 host 192.168.178.47
object network DI.CTSCANNER
 host **************
object network DI.CT.ADV.WS
 host 192.168.251.26
object network DI.GE.MAMMO.INTERFACE
 host 192.168.251.76
object network DI.MAMMO.SHUTTLE
 host 192.168.251.77
object network DI.MRI.ALLIANCE
 host 192.168.251.52
object network DI.MUSE01
 host **************
object network DI.MUSE02
 host **************
object network DI.MUSE03
 host **************
object network DI.MAMMO
 host **************
object network DI.NUCMEDCAMERA
 host **************
object network DI.PETCTVIEWER
 host 192.168.251.36
object network DI.PERTH.XRAY
 host 192.168.178.56
object network DI.R.AND.F
 host **************
object network DI.ROOMA
 host **************
object network DI.XELERIS.NM
 host **************
object network NYOH.INTERNAL.5
 host 10.29.31.120
object network CLEARWATER1
 host 139.87.116.247
object network CLEARWATER2
 host 139.87.107.37
object network CLEARWATER3
 range 64.39.96.1 64.39.96.254
object network CLEARWATER4
 range 139.87.112.1 139.87.113.254
object network JELMENDORFSPAM
 host 212.102.33.206
object service NOVA.TOPAZ
 service tcp source eq 47290 destination eq 47290 
object network LUCIUS25C.NLH.ORG
 host **************
object network PROVMDAPP.NLH.ORG
 host **************
object network NLHPROVMDORACLE.NLH.ORG
 host **************
object network NLHMUSE01.NLH.ORG
 host **************
object network NLHMUSE02.NLH.ORG
 host **************
object network LUCIUS16A.NLH.ORG
 host ***************
object network DESIGO.NLH.ORG
 host ***************
object network backblazeb2.com
 fqdn v4 backblazeb2.com
object network HANYS.EXTERNAL.1
 fqdn v4 46453879m.hanys.org
object network HANYS.INTERNAL.2
 fqdn v4 mta0102-101.cd.hanys.org
object network HANYS.EXTERNAL.3
 fqdn v4 mta0203-229.cd.hanys.org
object network PATIENT.CONNECT.ARTERA.EXTERNAL.PEER
 host **************
object network PATIENT.CONNECT.ARTERA.INTERNAL.PEER.1
 host ***********
object network PATIENT.CONNECT.ARTERA.INTERNAL.PEER.2
 host ************
object network LUCIOUS01
 host **************
object network NLHPRTGPROBE04
 host **************
object network LUCIUS10C
 host ***************
object network LUCIUS28
 host **************
object network PATIENT.CONNECT.ARTERA.INTERNAL.PEER.3
 host *************
object network PATIENT.CONNECT.ARTERA.INTERNAL.PEER.4
 host **************
object network LUCIUS07.NLH.ORG
 host ***************
object network NURSECALLAPP.NLH.ORG
 host **************
object network NLHBRAUNPUMPS.NLH.ORG
 host ***************
object network BRAUNWEB
 host **************
object network LUCIUS09A.NLH.ORG
 host ***************
object network LUCIUS09B.NLH.ORG
 host ***************
object network LUCIUS09C.NLH.ORG
 host ***************
object network NLHCISCO.NLH.ORG
 host **************
object network LUCIUS13.NLH.ORG
 host **************
object network SQLTEST.NLH.ORG
 host ***************
object network NLHMONITOR.NLH.ORG
 host *************7
object network NLHPRTG01.NLH.ORG
 host **************
object network NLHKIWISYSLOG01.NLH.ORG
 host **************
object network LUCIUS17A.NLH.ORG
 host ***************
object network XENAPP01.NLH.ORG
 host **************
object network CITRIXSF.NLH.ORG
 host ***************
object network NLHWEB01..NLH.ORG
 host ***************
object network AVAYACALLACCT.NLH.ORG
 host **************
object network NLHSSI.NLH.ORG
 host **************
object network TEMPTRAK.NLH.ORG
 host **************
object network PRINT.NLH.ORG
 host *************2
object network QUICKCHARGE.NLH.ORG
 host **************
object network NLH3M.NLH.ORG
 host **************
object network LUCIUS19D.NLH.ORG
 host **************
object network NLHAV01.NLH.ORG
 host ***************
object network LUCIUS23A.NLH.ORG
 host **************
object network LUCIUS23B.NLH.ORG
 host *************7
object network LUCIUS23C.NLH.ORG
 host *************9
object network LUCIUS23D.NLH.ORG
 host *************1
object network NLHDHCP01.NLH.ORG
 host **************
object network LUCIUS25B.NLH.ORG
 host **************
object network LUCIUS21.NLH.ORG
 host *************
object network CENTRALINK.NLH.ORG
 host *************4
object network LUCIUS27.NLH.ORG
 host ***************
object network HEALTHTOUCH02.NLH.ORG
 host **************
object network MUSE03.NLH.ORG
 host **************
object network KRONOSTEST.NLH.ORG
 host **************
object network MUSE-TEST.NLH.ORG
 host ***************
object network INTERLACETEST.NLH.ORG
 host *************6
object network NLHINT-TEST.NLH.ORG
 host **************2
object network LUCIUS29.NLH.ORG
 host *************5
object network NLHFUSION.NLH.ORG
 host **************
object network MUSE-CCGHL7.NLH.ORG
 host **************
object network LUCIUS31.NLH.ORG
 host ***************
object network LUCIUS10B.NLH.ORG
 host ***************
object network LUCIUS10C.NLH.ORG
 host ***************
object network NLHCA.NLH.ORG
 host ***************
object network NLHPRTGPROBE3.NLH.ORG
 host ***************
object network LUCIUS10D.NLH.ORG
 host ***************
object network CODONICS.NLH.ORG
 host **************
object network MDITEST.NLH.ORG
 host **************
object network CITRIXFS02.NLH.ORG
 host ***************
object network XENAPP02.NLH.ORG
 host ***************
object network MEDENTPRINT01.NLH.ORG
 host ***************
object network HEALTHTOUCH01.NLH.ORG
 host **************
object network LUCIUS18A.NLH.ORG
 host *************
object network INTERLACE.NLH.ORG
 host **************
object network NOVA-QIE.NLH.ORG
 host **************
object network LUCIUS18B.NLH.ORG
 host *************
object network NLHUTILITY.NLH.ORG
 host **************
object network NLHCODONICS.NLH.ORG
 host ***************
object network NLHLICENSE.NLH.ORG
 host **************
object network HPDMAN.NLH.ORG
 host ***************
object network SCVMM.NLH.ORG
 host ***************
object network LUCIUS24A.NLH.ORG
 host **************
object network LUCIUS24B.NLH.ORG
 host *************5
object network LUCIUS24C.NLH.ORG
 host *************7
object network LUCIUS24D.NLH.ORG
 host *************9
object network LUCIUS26A.NLH.ORG
 host **************
object network ESICALLACCT26A.NLH.ORG
 host **************
object network ESRS.NLH.ORG
 host **************
object network NLHDRFIRST.NLH.ORG
 host ***************
object network NLHELOCK.NLH.ORG
 host **************
object network LUCIUS26C.NLH.ORG
 host **************
object network COBAS.NLH.ORG
 host **************
object network PRADEV.NLH.ORG
 host ***************
object network NLHADMINCENTER.NLH.ORG
 host **************
object network LUCIUS28.NLH.ORG
 host **************
object network NURSECALLHD.NLH.ORG
 host **************
object network NLH-iUV.NLH.ORG
 host **************
object network LUCIUS30.NLH.ORG
 host **************
object network MUSE-APP.NLH.ORG
 host ***************
object network MUSE-NXWEB.NLH.ORG
 host **************
object network Clearwater.External.Peer
 host ************
object network Clearwater.Internal.Peer.Range
 subnet 172.16.201.32 *************40
object network ASA01
 host 192.168.178.2
object network ASA02
 host 192.168.178.1
object network NLH.Firewall.Range.Internal
 subnet 192.168.178.0 *************52
object network NETWORK_OBJ_*************
 host *************
object network NETWORK_OBJ_192.168.178.2
 host 192.168.178.2
object network QUICKCHARGE.EXTERNAL.WHITELIST.PEER.1
 host 75.2.77.89
object network QUICKCHARGE.EXTERNAL.WHITELIST.PEER.2
 host 3.33.185.57
object network SSI.EXTERNAL.PEER.1
 subnet 64.6.43.0 *************
object network MICROSOFTSTREAM.COM
 fqdn v4 MICROSOFTSTREAM.COM
object network MMI.BILLING.EXTERNAL.PEER
 host ************
object network MMI.BILLING.INTERNAL.PEER
 host 10.100.2.56
object network NYOH.INTERNAL.MEDICOM
 host 10.216.0.13
object network NYOH.INTERNAL.AMBRA
 host 10.216.0.11
object network NYOH.INTERNAL.POWERSHARE
 host 10.216.0.12
object network NYOH.INTERNAL.CLOUD
 host 10.216.0.14
object network Nuvodia.OneOncology.Cloud.External.Peer
 host ************
object network Nuvodia.OneOncology.Cloud.Internal.Peer
 host 172.25.136.106
object network NETWORK_OBJ_162.245.33.10
 host 162.245.33.10
object network NUVODIA.INTERNAL.NEW.3
 host 162.245.33.10
object network FRESHWORKS.EXCLUSIONS.1
 host 34.229.27.241
object network FRESHWORKS.EXCLUSIONS.2
 host 54.87.5.255
object network FRESHWORKS.EXCLUSIONS.3
 host 52.70.115.44
object network FRESHWORKS.EXCLUSIONS.5
 host 44.206.73.233
object network FRESHWORKS.EXCLUSIONS.6
 host 44.206.73.234
object network FRESHWORKS.EXCLUSIONS.7
 host 44.206.73.239
object-group service PaceGlobalgrp tcp
 description Pace Global Energy Systems tcp group
 port-object eq 6544
 port-object eq 2222
 port-object eq 2020
 port-object eq www
 port-object eq telnet
 port-object eq 53374
 port-object eq 53372
 port-object eq 53050
 port-object eq 53048
object-group service timeservice tcp
 port-object range 123 123
 port-object eq daytime
object-group service timeserviceUDP udp
 port-object eq 13
 port-object eq time
 port-object eq ntp
object-group network Medivators
 network-object object MEDIVATOR66838143
 network-object object MEDIVATOR66838147
object-group service QUEST tcp
 port-object range 57010 57013
object-group service citrixXML tcp
 description ICA and XML
 port-object eq citrix-ica
 port-object eq www
 port-object eq 8080
 port-object eq https
object-group service GatewayDMZ tcp
 description Services to allow from inside to DMZ server
 port-object eq citrix-ica
 port-object eq ftp
 port-object eq 3389
 port-object eq 8080
 port-object eq www
 port-object eq 2598
 port-object eq https
 port-object eq ldap
 port-object eq 8008
object-group service RSA tcp
 port-object range 5505 5570
object-group service HFMBoces tcp
 port-object eq pop3
 port-object eq www
 port-object eq https
 port-object eq smtp
object-group network NUVODIA.INTERNAL.GROUP.NEW
 network-object object NUVODIA.INTERNAL.NEW.1
 network-object object NUVODIA.INTERNAL.NEW.2
object-group network NUVODIA.INTERNAL.PEER.NET1
 network-object object NUVODIA.INTERNAL.1
 network-object object NUVODIA.INTERNAL.2
object-group network DM_INLINE_NETWORK_4
 group-object NUVODIA.INTERNAL.GROUP.NEW
 group-object NUVODIA.INTERNAL.PEER.NET1
object-group network DM_INLINE_NETWORK_6
 network-object object STRAT_SOL.NET.INTERNAL1
 network-object object STRAT_SOL.NET.INTERNAL2
object-group network FoodService
 network-object object P_FS_SEC
object-group service GEinbound tcp
 description GE remote support services inbound ports to allow
 port-object eq 3128
 port-object eq 8100
 port-object eq 4444
 port-object eq ftp-data
 port-object eq ftp
 port-object eq 5800
 port-object eq exec
 port-object eq www
 port-object range 2327 2328
 port-object range 3276 3277
 port-object eq 8080
 port-object eq 5900
 port-object eq telnet
 port-object eq 3003
object-group service GEoutbound tcp
 description GE remote services Outbound ports to allow
 port-object eq ftp
 port-object range 6000 6200
 port-object eq 8002
 port-object eq ftp-data
 port-object eq www
 port-object eq 7979
object-group network DI.Net.Group
 network-object object CTScanner
 network-object object Mammo
 network-object object RandF
object-group service PetLinks tcp
 description Mobile Pet CT remote access for image upload.
 port-object range 21000 21004
 port-object range 16000 16004
 port-object eq 8899
object-group network STRATEGICSOLUTIONS.EXTERNAL.GROUP
 network-object object STRAT_SOL.NET.INTERNAL1
 network-object object STRAT_SOL.NET.INTERNAL2
object-group service TeleVideoTcpUdp tcp-udp
 port-object range 3230 3253
 port-object eq 1503
 port-object eq 1731
 port-object eq 1300
 port-object range 1718 1720
object-group service GEPACS tcp
 description GE PACS tcp ports
 port-object eq 522
 port-object eq ldap
 port-object eq www
 port-object eq 3120
 port-object eq 20000
 port-object eq 3320
 port-object range 953 999
 port-object eq 3389
object-group service ExchangePorts tcp
 port-object eq https
 port-object eq 20443
 port-object eq 137
 port-object eq 161
 port-object eq 9100
object-group service PrintPorts tcp
 port-object eq lpd
 port-object eq 9100
object-group service PrinterPorts tcp
 port-object eq lpd
 port-object eq 9100
object-group network Cardinal
 description Cardinal Health Controlled Subs orders Pharmacy 4080 outbound
 network-object object Cardinal132
 network-object object Cardinal133
 network-object object Cardinal144
 network-object object Cardinal145
 network-object object Cardinal176
 network-object object Cardinal177
 network-object object Cardinal194
 network-object object Cardinal195
object-group network medinotes
 network-object object Medinote
 network-object object medinote2
 network-object object medinote1
object-group service IPSEC_ISAKMP udp
 port-object eq isakmp
 port-object eq 4500
object-group service EmdeonPorts tcp
 port-object eq 5002
 port-object eq https
object-group service in_any_to_out_any_tcp tcp
 description TCP ports always allowed from inside to outside.
 port-object eq 8080
 port-object eq citrix-ica
 port-object eq 1755
 port-object eq ssh
 port-object range 6101 6102
 port-object eq 3389
 port-object eq www
 port-object eq https
 port-object eq ftp
 port-object eq ftp-data
 port-object eq 9080
 port-object eq netbios-ssn
 port-object eq 138
 port-object eq 137
 port-object range 55443 55443
 port-object range 25859 25859
 port-object range 8383 8383
 port-object eq telnet
 port-object range 9999 9999
 port-object range 27014 27050
 port-object range 4080 4080
 port-object range 6881 6999
 port-object eq 1119
 port-object eq 5101
 port-object eq 3653
 port-object eq 2591
 port-object range 1852 1855
 port-object range 48000 49000
 port-object range 1688 1688
 port-object range 8150 8150
 port-object range 11001 11001
 port-object range 1024 1029
 port-object eq 2048
 port-object range sip 5061
 port-object range 30000 31000
 port-object eq 25565
 port-object eq 57773
 port-object eq 5938
 port-object eq 5228
 port-object range 8000 8000
 port-object eq 990
 port-object eq 1935
 port-object eq 5500
 port-object eq 50025
 port-object range 65007 65008
 port-object eq 6101
 port-object eq 900
 port-object eq 4080
 port-object eq 9443
 port-object eq 65009
 port-object eq 8443
 port-object eq 2443
 port-object eq 5494
 port-object eq 49210
 port-object eq 47290
object-group network CitrixServers.dmz
 network-object object NATHAN3.dmz
 network-object object NATHAN5.dmz
 network-object object NATHAN1.dmz
 network-object object NATHAN4.dmz
 network-object object NATHAN9.dmz
 network-object object NATHAN10.dmz
 network-object object NATHAN11.dmz
object-group service RAMSOFTports tcp
 port-object range 12800 12820
object-group network MilleniumPACS
 network-object object MilleniumPACS2
 network-object object MilleniumPACS1
 network-object object MilleniumPACS3
 network-object object MilleniumPACS4
 network-object object MilleniumPACS5
object-group service CoreFTP tcp
 description CoreFTP is used by Pat Accounts Manager
 port-object eq 990
 port-object range 1900 1930
object-group service PhilipsPacs tcp
 port-object eq 104
 port-object eq ldap
 port-object eq https
 port-object eq 7575
 port-object eq 8192
 port-object eq 2068
 port-object eq telnet
 port-object eq 3211
 port-object eq ssh
 port-object eq 6464
 port-object eq 1155
 port-object eq www
object-group network ExchangeServers
 network-object object NLHEXCHANGE.NLH.ORG
object-group network TeleMedVT
 network-object object TeleMedVT3
 network-object object TelemedVT4
 network-object object TelemedVT5
 network-object object TeleMedVT1
object-group network PacsServers
 network-object object PACS
 network-object object PACS_CACHE
 network-object object PACS_STORE1
 network-object object PACS_STORE2
 network-object object PACS_STORE144
object-group service Pacs tcp
 port-object eq 6464
 port-object eq https
 port-object eq 7575
object-group service NexTalk1 tcp
 port-object eq 2591
object-group service NexTalkTcpUdp tcp-udp
 port-object eq 1853
object-group service CastleSys tcp
 port-object eq 9011
 port-object eq 9006
object-group network MDI.OUT.Allow
 network-object object Medent.VPN.net
 network-object object Spantel.Prod
 network-object object SpantelHL7.test
object-group network eRXdataCenters
 network-object object eRXcenter2
 network-object object eRXcenter3
 network-object object eRXcenter1
 network-object object eRxChicago
 network-object object eRxDallas
object-group network Medent.Interface
 network-object object MedentRemote
 network-object object Medent.RPTS
object-group service FTPpsv5500 tcp
 description Passive FTP rang 5500-5700
 port-object range 5500 5700
 port-object eq ftp
object-group service Labcorp tcp-udp
 port-object eq 3611
 port-object eq 30032
object-group service Labcorptcp tcp
 port-object eq ftp
 port-object eq ftp-data
 port-object eq 3611
 port-object eq 30032
object-group network SMHA.RAD
 description St Mary's Hospital Radiologist Access
 network-object object SMHA.ps1
 network-object object SMHA.ps2
 network-object object SMHA.ps3
 network-object object SMHA.ps4
 network-object object SMHA.syn1
 network-object object SMHA.syn2
 network-object object SMHA.orpc1
 network-object object SMHA.orpc2
 network-object object SMHA.orpc3
 network-object object SMHA.read1
 network-object object SMHA.read2
 network-object object SMHA.read3
 network-object object SMHA.KPServer
 network-object object SMHA.read4
 network-object object smha.mammo
 network-object object smha.pacsed30
 network-object object smha.pacrd06
 network-object object SMHApacsSUBNET
 network-object ************ *************55
 network-object ********** *************55
 network-object ************ *************55
 network-object ************ *************55
 network-object ********** *************55
 network-object *********** *************55
 network-object object SMHA.read5
 network-object object SMHA.read6
 network-object object SHMA.read7
 network-object object SMHA.read8
 network-object object SMHA.read9
 network-object object SMHA.read10
 network-object ************* *************55
 network-object ************* *************55
 network-object ************ *************55
 network-object ************ *************55
 network-object object SMHA.Synapse.Dest
 network-object object SMHA.pacs1
 network-object object SMHA.pacs2
 network-object object SMHA.pacs3
 network-object host ***********
 network-object host ***********
 network-object host ************
 network-object host **********
 network-object object SMHA.READ.10
object-group network RAD.PACS.READ
 description Radiology PACS Reading Stations
 network-object object P-DI-MGR
 network-object object PACS_READ3_NEW
 network-object object P_CIO1
 network-object object P_DI_NUMED
 network-object object US_LOGI_E9
 network-object object MAMMO40
 network-object object MOMMO41
 network-object object PACS
 network-object object PACS_CACHE
 network-object object PACS_STORE1
 network-object object PACS_STORE2
 network-object object philipstst
 network-object object pacs.net
 network-object object PACS_VCE
 network-object object INTERLACE
object-group service IVANStcp tcp
 description IVANS Tcp ports to allow to DMZ
 port-object eq 5053
 port-object eq daytime
 port-object eq 9920
 port-object eq ldap
 port-object eq 709
 port-object eq ftp
 port-object eq 5080
 port-object eq 3101
object-group service IVANSudp udp
 port-object eq isakmp
 port-object eq 5081
 port-object eq domain
object-group network SOPHOS
 network-object object SOPHOSWEB
 network-object object SOPHOSEMAIL
object-group service Sophos tcp
 port-object eq 10443
 port-object eq ssh
 port-object eq 444
 port-object eq smtp
 port-object eq www
 port-object eq https
object-group service any_in_udp_to_any_out udp
 port-object eq nameserver
 port-object eq www
 port-object eq domain
 port-object eq time
 port-object eq ntp
 port-object eq netbios-ns
 port-object eq 139
 port-object eq netbios-dgm
 port-object range 27000 27015
 port-object range 6881 6999
 port-object eq 1119
 port-object eq isakmp
 port-object eq 201
 port-object eq 4500
 port-object range sip 5061
 port-object eq 1854
 port-object eq 1855
 port-object eq 1853
 port-object eq 1852
 port-object range 30000 31000
 port-object eq 34788
object-group network CitrixServers1
 network-object object XENAPP02
 network-object object XENAPP30
object-group network CitrixServers1_ref
 network-object object NATHAN3.dmz
 network-object object NATHAN4.dmz
 network-object object NATHAN5.dmz
 network-object object NATHAN6.dmz
 network-object object NATHAN1.dmz
object-group service SophosMail tcp
 port-object eq 10443
 port-object eq smtp
 port-object eq https
object-group service BobSFTP tcp
 description 21000-21100 Special Secure FTP
 port-object range 21000 21100
object-group network MVO_Allow_OUTBOUND_Group
 network-object object pacs.net_1
 network-object object ORTIZ_LT
 network-object object p_mis_netadmin
 network-object object PACS_OR3
 network-object object PACS_OR1
 network-object object PACS_OR2
 network-object object PACS_DI
 network-object object DR_CECIL
object-group network ProvationServers
 network-object object NLHDC1_IPMI
 network-object object NLHDC2_IPMI
object-group network AAI.NYOH.PACS
 network-object object AAI.120
 network-object object AAI.124
 network-object object AAI.125
 network-object object AAI.52
object-group service Impulse.UDP udp
 description Impulse Monitoric INC for Dr Shen
 port-object eq isakmp
 port-object eq 4500
object-group service ImpulseTCP tcp
 description Impulse Mpnitoring INC for Dr Shen
 port-object eq 51
 port-object eq 50
object-group service TEMP_TRACK1 tcp
 port-object range 1001 1001
object-group network Dolby_OUT
 description Dolby Co for remote setup of servers TEMP site to site
 network-object ********* *************
object-group network Dolby_Servers
 description Dolby Server Group
 network-object object INTERLACE
 network-object object DICTATION02
 network-object object DOLBEY
 network-object object DOLBEYTEST
object-group network Healthtouch.out
 network-object *********** *************55
object-group network FoodSVC
 network-object object HEALTHTOUCH01
 network-object object HEALTHTOUCH02
object-group service PatPortal tcp
 port-object range 55443 55443
object-group service ALLSCRIPT_PORTAL tcp
 port-object range 55443 55443
object-group service testgroup tcp
 port-object range 8383 8383
object-group service ALBANYMEDPACS tcp
 port-object range exec exec
 port-object range 104 104
object-group network ALBANYPACS.GROUP
 network-object object ALBANYMED.IN.2
 network-object object ALBANYMED.IN
object-group network HIXNY
 network-object object hixny.com_integration
 network-object object hixny.com_prod
 network-object object webservices.hixny.com
object-group service Guest_Wireless tcp
 port-object eq www
 port-object eq https
object-group service SOPHOSFTP tcp
 port-object range 5000 51000
object-group network Olympus.inside.group
 description Inside Addresses for Olympus Network
 network-object object Olympus.Inside.New
object-group service BOCES_IPADS tcp
 port-object eq 1640
 port-object eq www
 port-object eq 5223
 port-object range 2195 2196
 port-object eq https
object-group network MDI_Group
 network-object object MDILIVE.NLH.ORG
 network-object object MDITEST
object-group network Brian_DHCP
 network-object object BRIAN_DHCP
object-group network Schumacher.Inside
 network-object ************* *************55
 network-object ************* *************55
 network-object object Schumacher.Inside1.new.ADTPROD
 network-object object Schumacher.Inside2.new.ADTTEST
object-group service TEST tcp
 port-object range 9960 9969
 port-object eq 29900
 port-object eq 18120
 port-object eq 18000
 port-object range 1024 1124
 port-object eq 18060
 port-object eq 28910
 port-object eq 27900
object-group network MEDENTHQ
 network-object ************ *************55
 network-object ************** *************55
 network-object *********** *************55
 network-object *********** *************55
 network-object *********** *************55
object-group network MEDENT_GROUP
 network-object object MEDENT
 network-object object MEDENT03
 network-object object MEDENT05
 network-object object MEDENT_NAS_INTERNAL
 network-object object MEDENT-EXPORT
object-group service IMO_Ports tcp
 port-object eq 42053
 port-object eq 42027
 port-object eq 42051
 port-object eq 42011
 port-object eq 42045
object-group service TeamViewer tcp
 port-object eq 5938
object-group service CCD_MESSAGING tcp
 port-object eq 53374
 port-object eq 53050
 port-object eq 53372
 port-object eq 53048
 port-object eq www
object-group network WINDOWS_XP_DENY
 network-object object easyeeg
 network-object object VENUE50_p_pacs_cdburn
 network-object object INFINIA
object-group network APPLE.OUT
 description Apple IP addresses to stop retries on inside
 network-object ************** *************55
 network-object ************** *************55
 network-object ************ *************
 network-object ************ *************
 network-object ************ *************
 network-object ************** *************55
 network-object ************** *************55
 network-object ************** *************55
 network-object ************** *************55
object-group service Apple_Services tcp-udp
 port-object range 16380 16390
object-group network ProviderOrg.External
 network-object ************ *************55
 network-object ********** *************55
 network-object object hixny.com_integration
 network-object object hixny.com_prod
 network-object object integration.hixny.com
 network-object object webservices.hixny.com
 network-object ************** *************55
 network-object ************** *************55
 network-object ************** *************55
 network-object ************** *************55
 network-object 199.244.76.209 *************55
 network-object 199.244.76.201 *************55
 network-object 199.244.76.202 *************55
 network-object 199.244.76.167 *************55
 network-object 199.244.76.203 *************55
 network-object 199.244.76.204 *************55
 network-object 199.244.76.164 *************55
 network-object 199.244.76.165 *************55
 network-object 199.244.76.166 *************55
 network-object 199.244.76.174 *************55
 network-object 199.244.76.173 *************55
 network-object object Hixney.net_2
 network-object 10.30.33.36 *************55
 network-object 10.30.32.54 *************55
 network-object 10.30.33.29 *************55
 network-object ************* *************55
object-group service ProviderOrg tcp
 port-object eq 9443
 port-object eq www
 port-object eq https
object-group network CITRIX_EXTERNAL
 network-object object XENAPP02
 network-object object XENAPP30
 network-object object CITRIX_STOREFRONT
 network-object object P-IT-MGR
 network-object object XENAPP01
object-group network CITRIXGATEWAY.DMZ
 network-object object NETSCALER.VPX
 network-object object NETSCALER.WEB
 network-object object NETSCALERSUBNETIP
 network-object object MEDITECHAPIVIP2
 network-object object NETADMIN.DMZ.TEST
 network-object object DMZ_TEST.NLH.ORG
object-group network CITRIX_INTERNAL_TO_DMZ
 network-object object XENAPP02
 network-object object XENAPP30
 network-object object CITRIX_STOREFRONT
 network-object object P-IT-MGR
 network-object object NLHDC01.NLH.ORG
 network-object object NLHDC02.NLH.ORG
 network-object object p_mis_netadmin
 network-object object P_MIS_CISCOMON
 network-object object XENAPP17
 network-object object XENAPP18
 network-object object XENAPP19
 network-object object P_MIS52.WAYNE
 network-object object EUGENE10
 network-object object NLH-ISWEB.INTERNAL
 network-object object NLHTEST01
 network-object object NLHPRTG01
 network-object object XENAPP01
 network-object object LUCIUS29-iDRAC
 network-object object IT_TEST
 network-object object LUCIUS31-iDRAC
 network-object object NLHUTILITY
object-group network MIS_TEST
 network-object object p_mis_netadmin
 network-object object st_netadmin
 network-object object p_mis_netadmin2
 network-object object BILL_BAIRD
 network-object object Wayne
 network-object object BANDWIDTH_TEST
 network-object host ***************
 network-object object ST_NETADMIN2
 network-object object BANDWIDTH_TEST_2
 network-object host **************
 network-object object P-ITMGR
 network-object object CISCONMON
 network-object host **************
object-group service MAIL_VIRUS tcp
 port-object eq 9780
 port-object eq 63595
object-group network MARKETO_SPAMMER
 network-object ************ *************
 network-object ************ *************
 network-object ************ *************
 network-object ************ *************
object-group service STAT_RAD tcp
 port-object eq 5406
object-group service StatRadService tcp
 port-object eq 5406
object-group service PAT_ACCTS_FTP tcp
 port-object range 49730 49750
 port-object range 49000 50000
object-group service UDP_TEST udp
 port-object eq 59266
 port-object eq 51239
object-group network ENDOWORKS
 network-object object NLHENDO01
 network-object object ENDOWORKS02
 network-object object ENDOWORKS03
 network-object object NLHENDO01.NLH.ORG
object-group service CE000SVC tcp
 port-object eq 8277
object-group network CE2000.INTERNAL
 network-object object P_IS_PACS
 network-object object P_IS_RAMANI
object-group network CE2000.EXTERNAL
 network-object object retsolinc2.com
 network-object object retsolinc3.com
object-group service CE2000 tcp
 port-object eq 8277
object-group network Group_24.97.36.3
 network-object object LAN
object-group network Group_*************
 network-object object MDILIVE.NLH.ORG
 network-object object MDITEST
object-group network Group_12.39.198.49
 network-object object LabCorp3
 network-object object LabCorpDev
 network-object object LabCorpProd
object-group network Group_*************
 network-object object pacs.net_1
object-group network Group_************
 network-object object MDILIVE.NLH.ORG
 network-object object MDITEST
object-group network HIXNY.MBMS.INTERNAL
 network-object object HIXNY.MBMS.MILLENIUMBILLING.INTERNAL1
object-group service mckesson tcp
 port-object range 1025 65535
object-group network NUVODIA_VPN_NLH_PEER
 network-object object NUVODIA_VPN_NLH_PEER1
 network-object object NUVODIA_VPN_NLH_PEER2
object-group network CLEARWATERTEST
 network-object object CLEARWATER1
 network-object object CLEARWATER2
 network-object object CLEARWATER3
 network-object object CLEARWATER4
object-group network HIXNY.INTERNAL.GROUP
 network-object object HIXNY.INTERNAL1
 network-object object HIXNY.PEER.INTERNAL.PROD
 network-object object HIXNY.PEER.INTERNAL.TEST
 network-object object HIXNY.INTERNAL.TESTHUB
 network-object object HIXNY.INTERNAL.PUSH_SERVER
object-group network MDI.GROUP
 network-object object MDILIVE.NLH.ORG
 network-object object MDITEST
object-group network NYOH.INTERNAL.NET
 network-object object NYOH.INTERNAL.1
 network-object object NYOH.INTERNAL.3
 network-object object NYOH.INTERNAL.5
 network-object object NYOH.INTERNAL.2
 network-object object NYOH.INTERNAL.AMBRA
 network-object object NYOH.INTERNAL.CLOUD
 network-object object NYOH.INTERNAL.MEDICOM
 network-object object NYOH.INTERNAL.POWERSHARE
object-group network NUVODIA.VPN.SENDPOINT.MASTER
 network-object object NUVODIA.INTERNAL.NEW.1
 network-object object NUVODIA.INTERNAL.NEW.2
 network-object object NUVODIA_VPN_NLH_PEER1
 network-object object NUVODIA_VPN_NLH_PEER2
object-group network DM_INLINE_NETWORK_7
 group-object MVO_Allow_OUTBOUND_Group
 group-object NUVODIA_VPN_NLH_PEER
object-group network DM_INLINE_NETWORK_8
 network-object object SENTRYDS
 network-object object SENTRYDS.NET
object-group network DM_INLINE_NETWORK_9
 network-object object SENTRYDS
 network-object object SENTRYDS.NET
object-group network SMHA.RAD.NEW
 network-object object SMHA.syn1
 network-object object SMHA.syn2
 network-object object SMHA.Synapse.Dest
object-group service DM_INLINE_SERVICE_1
 service-object tcp destination eq domain 
 service-object tcp destination eq www 
 service-object tcp destination eq https 
 service-object udp destination eq domain 
object-group network DM_INLINE_NETWORK_10
 network-object ************* *************
 network-object object NLHDMZ01_SWITCH
object-group network MEDENT.NIMBLE.OPENVPN
 network-object object MEDENT.NIMBLE.INSIDE.1
object-group network MEDENT.NIMBLE.OPENVPN.OUTSIDE.GROUP
 network-object object MEDENT.NIMBLE.OPENVPN.OUTSIDE.1
 network-object object MEDENT.NIMBLE.OPENVPN.OUTSIDE.2
object-group network MEDENT.NIMBLE.OPENVPN.INSIDE.GROUP
 network-object object MEDENT.NIMBLE.INSIDE.1
object-group service MEDENT_TELEMED tcp-udp
 port-object eq 1443
 port-object eq 3478
object-group protocol TCPUDP
 protocol-object udp
 protocol-object tcp
object-group network EXPANSE_VLANS
 network-object object EXPANSE_VLAN1
 network-object object EXPANSE_VLAN2
 network-object object EXPANSE_VLAN3
 network-object object EXPANSE_VLAN4
object-group network SmartNet_Devices
 network-object object NLH_DCDS_9300s
 network-object object SR_STACK_01
 network-object object NLH.CORE.INTERNAL
 network-object object DCDS.CORE.INTERNAL
 network-object object GPC_STACK
object-group network Domain.Controllers.Group
 network-object object NLHDC01.NLH.ORG
 network-object object NLHDC02.NLH.ORG
object-group network Quest.NLH2Quest.Internal
 network-object object MAGICA
 network-object object NLI-BG04
object-group network CHANGE.HEALTHCARE.EXTERNAL.GROUP
 network-object object CHANGE.HEALTHCARE.EXTERNAL.IP1.PROD
 network-object object CHANGE.HEALTHCARE.EXTERNAL.IP2.TEST
object-group network CHANGE.HEALTHCARE.NLH.INTERNAL.GROUP
 network-object object NLI.T.BG01
object-group network CHC.EXTERNAL.NETWORK
 network-object object CHC.EXTERNAL.1
 network-object object CHC.EXTERNAL.2
 network-object object CHC.EXTERNAL.3
object-group network PHINMS.GROUP
 network-object object NLHFTP01.NLH.ORG
 network-object object NLI-T-BG01
object-group service PHINMS tcp
 port-object eq 5088
 port-object eq 5089
 port-object eq 6087
object-group network NLI.INTERNAL.NAT.CHC
 network-object object NLI-BG04.CHC.NAT
 network-object object NLI-T-BG01.CHC.NAT
object-group network NLI-BG-GROUP
 network-object object NLI-BG01.nlh.org
 network-object object NLI-T-BG01
 network-object object NLI-BG04
object-group network DM_INLINE_NETWORK_11
 group-object EXPANSE_VLANS
 network-object object LAN
object-group network QUEST.VPN.INTERNAL.GROUP.2
 network-object object NLI-BG04
 network-object object NLI-T-BG01
 network-object host **************3
 network-object host **************2
 network-object object MDILIVE.NLH.ORG
 network-object object MDITEST.NLH.ORG
object-group network WEBSSO.MEDITECH.COM.EXTERNAL.GROUP
 network-object object WEBSSO.MEDITECH.COM
 network-object object WEBSSO2FA.MEDITECH.COM
object-group network BACKLINE.LDAP.NLH.INTERNAL
 network-object object NLHDC01.NLH.ORG
 network-object object NLHDC02.NLH.ORG
object-group network FIRECALLSYSTEM
 network-object object FIRECALL_JSC
 network-object object DDPC.FIREALARM
object-group network FIRECALLSYSTEM__ENDPOINTS
 network-object object FIRECALLSYSTEM_ENDPOINTS1
 network-object object FIRECALLSYSTEM_ENDPOINTS2
object-group service SALUCRO_FTP tcp-udp
 port-object range 20000 21000
object-group network IT_DEPT
 network-object object P_IT_COOR
 network-object object p_mis_netadmin
 network-object object P_IT_TECH1
 network-object object Wayne
 network-object object P-IT-MGR
 network-object object HIRAM
 network-object object RYAN
 network-object object NICK
 network-object object IT_TEST
object-group service QUEST_SFTP_NEW tcp-udp
 port-object eq 11022
object-group network FULL_PORT_ACCESS
 network-object object NLHAV01.NLH.ORG
object-group network CISCO_INTERNAL_2_EXTERNAL_ACL
 network-object object FIREPOWER_VM_ESXI
 network-object object CISCOPRIME.INTERNAL
 network-object object CISCOPRIMEINF
 network-object object SYSLOGSERVER
object-group network HEALTHTOUCH.NLH.INTERNAL
 network-object object HEALTHTOUCH01
 network-object object HEALTHTOUCH02
object-group network HEALTHTOUCH.PEER.INTERNAL
 network-object object HEALTHTOUCH.PEER.INTERNAL.2
 network-object object HEALTHTOUCH.PEER.INTERNAL.1
object-group network Greycastle_Testing_External
 network-object host ***************
 network-object host **************
 network-object host **************
 network-object host ************
 network-object host ***********
object-group network LINKBG.SPAM.GROUP
 network-object host ************
 network-object host *************
 network-object host ***************
 network-object host *************
 network-object host *************
 network-object host *************
object-group network love.explorethebest.com.spam.group
 network-object object love.explorethebest.com.spam.1
 network-object object love.explorethebest.com.spam.2
 network-object object love.explorethebest.com.spam.3
object-group network NLH.ACRONIS.GROUP.INSIDE
 network-object object HARRIET.NLH.ORG
 network-object object LUCIUS32.NLH.ORG
 network-object object NLHEXCHANGE.NLH.ORG
 network-object object LUCIUS10A.NLH.ORG
 network-object object LUCIUS19B.NLH.ORG
 network-object object WILLYWONKA.NLH.ORG
 network-object object NLHSYN01.NLH.ORG
 network-object object NLHSYN02.NLH.ORG
 network-object object NLHSYN03.NLH.ORG
 network-object object NLHSYN04.NLH.ORG
 network-object object NLHSP19WEB.NLH.ORG
 network-object object NLHSP19APP.NLH.ORG
 network-object object NLHSP19OFCWEB.NLH.ORG
 network-object object LUCIUS18C.NLH.ORG
 network-object object LUCIUS19C.NLH.ORG
 network-object object LUCIUS14.NLH.ORG
 network-object object LUCIUS26D.NLH.ORG
 network-object object LUCUIS16B.NLH.ORG
 network-object object ARCHIVE.NLH.ORG
 network-object object DR1.NLH.ORG
 network-object object FAXSERVER.NLH.ORG
 network-object object LUCIUS17B.NLH.ORG
 network-object object LUCIUS19A.NLH.ORG
 network-object object LUCIUS25A.NLH.ORG
 network-object object LUCIUS26B.NLH.ORG
 network-object object MDILIVE.NLH.ORG
 network-object object NLHBACKUP02.NLH.ORG
 network-object object NLHFTP01.NLH.ORG
 network-object object ONEVIEW.NLH.ORG
 network-object object SUMMIT.NLH.ORG
 network-object object KRONOSNEW.NLH.ORG
 network-object object NLHENDO01.NLH.ORG
 network-object object SQL01.NLH.ORG
 network-object object NLHDC01.NLH.ORG
 network-object object NLHDC02.NLH.ORG
 network-object object LUCIUS18D.NLH.ORG
 network-object object STREAMTASK.NLH.ORG
 network-object object DESIGO.NLH.ORG
 network-object object LUCIUS16A.NLH.ORG
 network-object object LUCIUS25C.NLH.ORG
 network-object object NLHMUSE01.NLH.ORG
 network-object object NLHMUSE02.NLH.ORG
 network-object object NLHPROVMDORACLE.NLH.ORG
 network-object object PROVMDAPP.NLH.ORG
 network-object object LUCIOUS01
 network-object object NLHPRTGPROBE04
 network-object object LUCIUS10C
 network-object object LUCIUS28
 network-object object COBAS.NLH.ORG
 network-object object ESICALLACCT26A.NLH.ORG
 network-object object ESRS.NLH.ORG
 network-object object LUCIUS24A.NLH.ORG
 network-object object LUCIUS24B.NLH.ORG
 network-object object LUCIUS24C.NLH.ORG
 network-object object LUCIUS24D.NLH.ORG
 network-object object LUCIUS26A.NLH.ORG
 network-object object LUCIUS26C.NLH.ORG
 network-object object LUCIUS28.NLH.ORG
 network-object object LUCIUS30.NLH.ORG
 network-object object MUSE-APP.NLH.ORG
 network-object object MUSE-NXWEB.NLH.ORG
 network-object object NLH-iUV.NLH.ORG
 network-object object NLHADMINCENTER.NLH.ORG
 network-object object NLHDRFIRST.NLH.ORG
 network-object object NLHELOCK.NLH.ORG
 network-object object NURSECALLHD.NLH.ORG
 network-object object PRADEV.NLH.ORG
object-group network NLH.ACRONIS.GROUP.EXTERNAL
 network-object host ************
 network-object host ************
 network-object host ************
 network-object host ************
 network-object host ************
 network-object host ************
 network-object host ************
 network-object host ************
 network-object host ************
 network-object host ************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host ************
 network-object host ************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object host **************
 network-object object ACRONIS.EXTERNAL.RANGE1
 network-object host ***********
 network-object host ***********
 network-object host ***********
 network-object object ACRONIS.EXTERNAL.RANGE3
 network-object object ACRONIS.EXTERNAL.RANGE4
 network-object object ACRONIS.EXTERNAL.RANGE2
 network-object host **************3
object-group network BARRACUDA.LDAP.EXTERNAL
 network-object object BARRACUDA.LDAP.EXTERNAL.PEER.1
 network-object object BARRACUDA.LDAP.EXTERNAL.PEER.2
 network-object object BARRACUDA.LDAP.EXTERNAL.PEER.3
object-group network REYHEALTH.EXTERNAL.GROUP
 network-object object REYHEALTH.EXTERNAL.EXTERNAL.1
 network-object object REYHEALTH.EXTERNAL.EXTERNAL.2
object-group service REYHEALTH.EXTERNAL.PORT.GROUP
 service-object object REYHEALTH.EXTERNAL.PORT1 
 service-object object REYHEALTH.EXTERNAL.PORT2 
object-group network EMAIL.BLACKLIST.EXTERNAL
 network-object host ***********
 network-object host *************
 network-object host *************
 network-object host *************
 network-object object JELMENDORFSPAM
object-group service DM_INLINE_SERVICE_2
 service-object object obj-tcp-eq-25 
 service-object tcp destination eq smtp 
object-group network NLH.DI.GEDEVICES
 network-object object DI.AWSERVER
 network-object object DI.AWSERVER.ILO
 network-object object DI.CT.ADV.WS
 network-object object DI.CTSCANNER
 network-object object DI.GE.MAMMO.INTERFACE
 network-object object DI.MAMMO
 network-object object DI.MAMMO.SHUTTLE
 network-object object DI.MRI.ALLIANCE
 network-object object DI.MUSE01
 network-object object DI.MUSE02
 network-object object DI.MUSE03
 network-object object DI.NUCMEDCAMERA
 network-object object DI.PERTH.XRAY
 network-object object DI.PETCTVIEWER
 network-object object DI.R.AND.F
 network-object object DI.ROOMA
 network-object object DI.XELERIS.NM
object-group network DM_INLINE_NETWORK_3
 network-object object NLI-BG04.CHC.NAT
 network-object object NLI-T-BG01.CHC.NAT
object-group network blackblazeb2.goup
 network-object host ***************
 network-object host ***************
 network-object host **************
object-group network HAYNS.EXTERNAL.GROUP
 network-object host ***************
 network-object host *************
 network-object host **************
 network-object object HANYS.EXTERNAL.1
 network-object object HANYS.EXTERNAL.3
 network-object object HANYS.INTERNAL.2
object-group network PATIENT.CONNECT.ARTERA.INTERNAL.PEER.GROUP
 network-object object PATIENT.CONNECT.ARTERA.INTERNAL.PEER.1
 network-object object PATIENT.CONNECT.ARTERA.INTERNAL.PEER.2
object-group network Incident.External
 network-object host **************
 network-object host **************
 network-object host *************
 network-object host *************
 network-object host *************
object-group network NLH.Firewall.Internal.Group
 network-object object ASA01
 network-object object ASA02
object-group network Clearwater.Internal.Group
 network-object object Clearwater.Internal.Peer.Range
object-group network QUICKCHARGE.EXTERNAL.WHITELIST.PEER.GROUP
 network-object object QUICKCHARGE.EXTERNAL.WHITELIST.PEER.1
 network-object object QUICKCHARGE.EXTERNAL.WHITELIST.PEER.2
object-group network SSI.EXTERNAL.PEER.GROUP
 network-object object SSI.EXTERNAL.PEER.1
object-group network BANDWIDTH.TEST.GROUP.OUTSIDE
 network-object host ***************
 network-object host ***************
 network-object host ************
 network-object host ***************
 network-object ************* *************
 network-object ********** *************
object-group network FRESHWORKS.EXCLUSIONS.GROUP
 network-object object FRESHWORKS.EXCLUSIONS.1
 network-object object FRESHWORKS.EXCLUSIONS.2
 network-object object FRESHWORKS.EXCLUSIONS.3
 network-object object FRESHWORKS.EXCLUSIONS.5
 network-object object FRESHWORKS.EXCLUSIONS.6
 network-object object FRESHWORKS.EXCLUSIONS.7
access-list inside_access_in extended deny ip any object-group Incident.External 
access-list inside_access_in extended deny ip any object MICROSOFTSTREAM.COM 
access-list inside_access_in extended deny ip any object-group BANDWIDTH.TEST.GROUP.OUTSIDE inactive 
access-list inside_access_in extended permit ip any any 
access-list inside_access_in extended deny ip ************* ************* object GUEST_NETWORK 
access-list inside_access_in remark Allowed TCP Traffic
access-list inside_access_in remark Allowed TCP Traffic
access-list inside_access_in extended permit tcp any4 any4 object-group in_any_to_out_any_tcp 
access-list inside_access_in extended permit tcp any4 any4 object-group IMO_Ports 
access-list inside_access_in extended permit ip interface inside object Clearwater.Internal.Peer.Range 
access-list inside_access_in remark Allowed UDP Traffic
access-list inside_access_in extended permit udp any4 any4 object-group any_in_udp_to_any_out 
access-list inside_access_in extended permit icmp any4 any4 
access-list inside_access_in extended permit object-group REYHEALTH.EXTERNAL.PORT.GROUP any object-group REYHEALTH.EXTERNAL.GROUP 
access-list inside_access_in extended permit ip any object-group FRESHWORKS.EXCLUSIONS.GROUP 
access-list inside_access_in extended permit ip object-group CISCO_INTERNAL_2_EXTERNAL_ACL any4 
access-list inside_access_in extended permit ip object-group MIS_TEST any 
access-list inside_access_in extended permit ip object-group NLH.ACRONIS.GROUP.INSIDE object-group NLH.ACRONIS.GROUP.EXTERNAL 
access-list inside_access_in extended permit ip object CISCO.WSA.INTERNAL any 
access-list inside_access_in extended permit ip object-group Domain.Controllers.Group any 
access-list inside_access_in extended permit ip object-group NLH.DI.GEDEVICES object GESUPPORT.INTERNAL.NET 
access-list inside_access_in extended permit ip object DI.NET any4 
access-list inside_access_in extended permit ip object pacs.net_1 any4 
access-list inside_access_in extended permit object-group TCPUDP any any eq domain 
access-list inside_access_in extended permit ip any4 object-group QUICKCHARGE.EXTERNAL.WHITELIST.PEER.GROUP 
access-list inside_access_in extended permit tcp object-group ExchangeServers any4 eq smtp 
access-list inside_access_in extended permit ip object EXPANSE_VLAN1 any4 
access-list inside_access_in extended permit ip object EXPANSE_VLAN2 any4 
access-list inside_access_in extended permit ip object EXPANSE_VLAN3 any4 
access-list inside_access_in extended permit ip object EXPANSE_VLAN4 any4 
access-list inside_access_in extended permit ip object NLHBRAUNPUMPS.INTERNAL object NLHBRAUNPUMPS.EXTERNAL 
access-list inside_access_in extended permit ip object NLI-BG04 any 
access-list inside_access_in extended permit ip object NLHSSI any 
access-list inside_access_in extended permit ip object NLHSSI object-group SSI.EXTERNAL.PEER.GROUP 
access-list inside_access_in extended permit ip object MDILIVE.NLH.ORG object PATIENT_PORTAL_1 
access-list inside_access_in extended permit ip object NLHPRTG01 object-group DM_INLINE_NETWORK_10 
access-list inside_access_in extended permit ip object-group NUVODIA.VPN.SENDPOINT.MASTER any4 
access-list inside_access_in extended permit ip object NLHKIWISYSLOG01.NLH.ORG any 
access-list inside_access_in extended permit ip any4 object-group NYOH.INTERNAL.NET 
access-list inside_access_in extended permit ip object-group NUVODIA.INTERNAL.GROUP.NEW object NYOH.INTERNAL.5 inactive 
access-list inside_access_in extended permit ip object NYOH.INTERNAL.5 object-group NUVODIA.INTERNAL.GROUP.NEW inactive 
access-list inside_access_in extended permit ip object-group NUVODIA.INTERNAL.PEER.NET1 any4 inactive 
access-list inside_access_in extended permit ip object-group FULL_PORT_ACCESS any 
access-list inside_access_in extended permit ip object-group IT_DEPT object-group CITRIXGATEWAY.DMZ 
access-list inside_access_in extended permit ip object-group IT_DEPT object DMZ_TEST.NLH.ORG 
access-list inside_access_in extended permit ip object-group SmartNet_Devices any4 
access-list inside_access_in extended permit ip object MAGICA object questlab 
access-list inside_access_in extended permit ip object-group FIRECALLSYSTEM object-group FIRECALLSYSTEM__ENDPOINTS 
access-list inside_access_in remark Rules for Citrix Remote Access
access-list inside_access_in extended permit ip object-group CITRIX_INTERNAL_TO_DMZ object-group CITRIXGATEWAY.DMZ 
access-list inside_access_in extended permit ip object-group MDI_Group any4 
access-list inside_access_in extended permit ip object MEDENT host ************ 
access-list inside_access_in extended permit ip object-group MEDENT_GROUP any4 
access-list inside_access_in extended permit ip object-group MEDENT.NIMBLE.OPENVPN.INSIDE.GROUP object-group MEDENT.NIMBLE.OPENVPN.OUTSIDE.GROUP 
access-list inside_access_in remark For Pharmacy Per Ramani Port 8277
access-list inside_access_in extended permit tcp object-group CE2000.INTERNAL object-group CE2000.EXTERNAL object-group CE2000 
access-list inside_access_in remark Just leaving a note here, does this server need all TCP ports open
access-list inside_access_in extended permit tcp object HYPER-V_CLUSTER any4 
access-list inside_access_in extended permit ip object PDX.Internal any4 
access-list inside_access_in extended permit ip object RCARE-SERVER any 
access-list inside_access_in extended permit ip object ROBOT_GE_VOT_TRAIN any 
access-list inside_access_in extended permit object NLI-BG13-FTP object NLI-BG01.nlh.org any 
access-list inside_access_in extended permit ip object NLHUTILITY any 
access-list inside_access_in extended permit ip object-group HEALTHTOUCH.NLH.INTERNAL object-group HEALTHTOUCH.PEER.INTERNAL 
access-list inside_access_in extended permit ip object-group Medivators any 
access-list inside_access_in extended permit ip object NOVA-QIE.INTERNAL any 
access-list inside_access_in extended permit tcp any4 any4 eq 1777 
access-list inside_access_in extended permit tcp any4 any4 eq 44315 
access-list inside_access_in extended permit tcp any4 any4 eq 10000 
access-list inside_access_in extended permit tcp any4 any4 eq 8200 
access-list inside_access_in extended permit tcp any4 any4 range 6101 6102 
access-list inside_access_in extended permit tcp any4 any4 object-group CoreFTP 
access-list inside_access_in extended permit tcp any4 any4 eq klogin 
access-list inside_access_in remark Allow iPhones to mail.mac.com to stop constant retries
access-list inside_access_in extended permit tcp any4 any4 eq 993 
access-list inside_access_in extended permit ip any4 object-group WEBSSO.MEDITECH.COM.EXTERNAL.GROUP 
access-list inside_access_in extended permit ip any4 object-group HIXNY 
access-list inside_access_in extended permit ip any4 object GEserviceNET 
access-list inside_access_in extended permit ip any4 object-group SMHA.RAD 
access-list inside_access_in extended permit ip any4 object-group medinotes 
access-list inside_access_in remark Whitelisting for Direct Messaging
access-list inside_access_in extended permit tcp object MAGICA object-group ProviderOrg.External object-group ProviderOrg 
access-list inside_access_in extended permit ip object-group FoodService object Sodexho 
access-list inside_access_in extended permit tcp object-group ProvationServers object Provation-out eq smtp 
access-list inside_access_in extended permit ip object IMO_2 any4 
access-list inside_access_in extended permit tcp object MCKESSON.MC.PHARM object newsync3.mkesson.com object-group mckesson 
access-list inside_access_in extended permit ip object ESRS_EMC_VIRTUAL_APPLIANCE any 
access-list inside_access_in extended permit tcp object-group PHINMS.GROUP any object-group PHINMS 
access-list inside_access_in remark This is for traffic for the CHC/OPTUM VPN
access-list inside_access_in extended permit ip object-group NLI.INTERNAL.NAT.CHC any 
access-list outside_access_in extended permit icmp any4 any4 
access-list outside_access_in extended deny ip object-group BANDWIDTH.TEST.GROUP.OUTSIDE any inactive 
access-list outside_access_in extended deny ip object-group Incident.External any 
access-list outside_access_in extended deny ip host *************** any 
access-list outside_access_in extended deny ip host *************** any 
access-list outside_access_in extended deny ip host ************* any 
access-list outside_access_in extended deny ip host ************ any 
access-list outside_access_in extended deny ip host *************** any 
access-list outside_access_in extended deny ip object backblazeb2.com any 
access-list outside_access_in extended deny ip object-group blackblazeb2.goup any 
access-list outside_access_in extended permit ip object GESUPPORT.INTERNAL.NET any4 
access-list outside_access_in extended deny ip object-group EMAIL.BLACKLIST.EXTERNAL any 
access-list outside_access_in remark MARKETO mktomail.com Spammer
access-list outside_access_in extended deny ip object-group MARKETO_SPAMMER any4 
access-list outside_access_in extended deny ip object sendgrid.net.virus any 
access-list outside_access_in extended deny ip object-group love.explorethebest.com.spam.group any 
access-list outside_access_in extended deny ip object-group LINKBG.SPAM.GROUP any4 
access-list outside_access_in extended permit ip object NYOH.INTERNAL.5 any4 inactive 
access-list outside_access_in extended permit ip object CHC.OPTUM.NAT.INTERNAL.SUB object-group NLI.INTERNAL.NAT.CHC 
access-list outside_access_in extended permit ip object-group HEALTHTOUCH.PEER.INTERNAL object-group HEALTHTOUCH.NLH.INTERNAL 
access-list outside_access_in extended permit ip object NOVA.INTERLACE.PEER.EXTERNAL2 object NOVA-QIE.INTERNAL 
access-list outside_access_in extended permit ip object NOVA.INTERLACE.PEER.EXTERNAL object NOVA-QIE.INTERNAL 
access-list outside_access_in extended permit ip any object NOVA-QIE.INTERNAL inactive 
access-list outside_access_in extended permit object-group DM_INLINE_SERVICE_2 object BARRACUDA.CLOUD.EXTERNAL object NLHEXCHANGE.NLH.ORG 
access-list outside_access_in extended permit ip object-group CLEARWATERTEST object NLHEXCHANGE.NLH.ORG inactive 
access-list outside_access_in extended permit tcp any object NLHEXCHANGE.NLH.ORG eq https 
access-list outside_access_in extended permit ip object Clearwater.Internal.Peer.Range interface inside 
access-list outside_access_in extended permit ip any object BARRACUDA.EMAIL.INSIDE 
access-list outside_access_in extended permit ip any4 object PATIENTPORTAL.EXTERNAL 
access-list outside_access_in extended permit ip any object PATIENTPORTAL.DMZ 
access-list outside_access_in extended permit ip any object mtrestexpapis-test01.nlh.org.DMZ 
access-list outside_access_in extended permit ip any object mtrestexpapis-live01.nlh.org.DMZ 
access-list outside_access_in extended permit ip any object mtrestexpapis-live01.nlh.org.external 
access-list outside_access_in extended permit ip any object mtrestexpapis-test01.nlh.org.external 
access-list outside_access_in extended permit tcp any4 object DIRECT.NLH.ORG object-group PaceGlobalgrp 
access-list outside_access_in extended permit tcp any4 object DIRECT.NLH.ORG eq 15031 
access-list outside_access_in extended permit udp any4 object DIRECT.NLH.ORG eq 15032 
access-list outside_access_in extended permit tcp any4 object DIRECT.NLH.ORG eq 15002 
access-list outside_access_in extended permit tcp any4 object DIRECT.NLH.ORG eq 15331 
access-list outside_access_in extended permit ip any4 object DIRECT.NLH.ORG 
access-list outside_access_in extended permit ip any4 object-group NLI-BG-GROUP 
access-list outside_access_in extended permit ip any object NETSCALER.WEB 
access-list outside_access_in extended permit ip any object DUOTEST.NLH.ORG 
access-list outside_access_in extended permit ip any object DUOTEST.NLH.ORG.DMZ 
access-list outside_access_in extended permit tcp any4 object NLH-ISWEB.DMZ eq https 
access-list outside_access_in extended permit ip any object NLH-ISWEB.DMZVR 
access-list outside_access_in extended permit ip any4 object NLH-WEB01-WS01 
access-list outside_access_in extended permit ip object-group ProviderOrg.External object DIRECT.NLH.ORG 
access-list outside_access_in extended permit ip any object MAGICA 
access-list outside_access_in extended permit ip object BRIAN_DHCP object NLHTESTMOBILE 
access-list outside_access_in remark Rule for Castle DR Implementation
access-list DMZ_access_in_V1 extended permit ip ************* ************* object NLHPRTG01 
access-list DMZ_access_in_V1 extended permit ip object-group CITRIXGATEWAY.DMZ object-group IT_DEPT 
access-list DMZ_access_in_V1 extended permit ip object DMZ_TEST.NLH.ORG object-group IT_DEPT 
access-list DMZ_access_in_V1 extended permit ip object-group CITRIXGATEWAY.DMZ object-group CITRIX_INTERNAL_TO_DMZ 
access-list DMZ_access_in_V1 extended deny ip ************* ************* object LAN 
access-list DMZ_access_in_V1 extended deny ip ************* ************* object-group EXPANSE_VLANS 
access-list inside_nat0_outbound extended permit ip object MAGICA object questlab 
access-list inside_nat0_outbound extended permit ip any4 *************** *************** 
access-list inside_nat0_outbound extended permit ip object LAN *************** *************** 
access-list inside_nat0_outbound extended permit ip object pacs.net_1 object PhilipsSupport 
access-list inside_nat0_outbound extended permit ip object-group MVO_Allow_OUTBOUND_Group object MVOrtho.net 
access-list inside_nat0_outbound extended permit ip object LAN_1 object LabCorp3 
access-list inside_nat0_outbound extended permit ip object LAN_1 object LabCorpDev 
access-list inside_nat0_outbound extended permit ip object LAN_1 object LabCorpProd 
access-list inside_nat0_outbound extended permit ip object MilleniumPACSnat object-group MilleniumPACS 
access-list inside_nat0_outbound extended permit ip object-group RAD.PACS.READ object-group SMHA.RAD 
access-list inside_nat0_outbound extended permit ip object pacs.net_1 object MVOatJSC.net 
access-list inside_nat0_outbound extended permit ip object-group MVO_Allow_OUTBOUND_Group object pacs.net_1 
access-list inside_nat0_outbound extended permit ip object-group FoodSVC object-group Healthtouch.out 
access-list inside_nat0_outbound extended permit ip object LAN object MVOrtho.net 
access-list inside_nat0_outbound extended permit ip object MDILIVE.NLH.ORG object-group Schumacher.Inside 
access-list inside_nat0_outbound extended permit ip object MDITEST object-group Schumacher.Inside 
access-list inside_nat0_outbound extended permit ip object PACS_NEW object-group AAI.NYOH.PACS 
access-list inside_nat0_outbound extended permit ip object-group CITRIX_INTERNAL_TO_DMZ object-group CITRIXGATEWAY.DMZ 
access-list inside_nat0_outbound extended permit ip object INTERLACE object-group SMHA.RAD 
access-list VPN2.nlh.org_splitTunnelAcl standard permit any4 
access-list outside_cryptomap_6 extended permit ip object-group QUEST.VPN.INTERNAL.GROUP.2 object QUEST.VPN.EXTERNAL.2 
access-list VPN.nlh.org_splitTunnelAcl standard permit ************* ************* 
access-list VPN.nlh.org_splitTunnelAcl standard permit *************** *************** 
access-list VPN.nlh.org_splitTunnelAcl standard permit ************* ************* 
access-list outside_cryptomap_3 extended permit ip object-group NUVODIA.INTERNAL.PEER.NET1 object AMC.PACS.NEW 
access-list outside_cryptomap_9 extended permit ip object LAN object-group STRATEGICSOLUTIONS.EXTERNAL.GROUP 
access-list outside_cryptomap_10 extended permit ip object-group DM_INLINE_NETWORK_3 object CHC.OPTUM.NAT.INTERNAL.SUB 
access-list outside_cryptomap_11 extended permit ip object-group MDI.GROUP object-group HIXNY.INTERNAL.GROUP 
access-list outside_cryptomap_1 extended permit ip object-group NUVODIA_VPN_NLH_PEER object Ellis.inside 
access-list outside_cryptomap_12 extended permit ip object-group MDI.GROUP object HIXNY.MBMS.MILLENIUMBILLING.INTERNAL1 
access-list outside_cryptomap extended permit ip object-group MDI.GROUP object HIXNY.MBMS.MILLENIUMBILLING.INTERNAL1 
access-list outside_cryptomap_14 extended permit ip object-group NUVODIA_VPN_NLH_PEER object-group SMHA.RAD.NEW 
access-list outside_cryptomap_15 extended permit ip object-group NUVODIA.VPN.SENDPOINT.MASTER object-group NYOH.INTERNAL.NET 
access-list outside_cryptomap_2 extended permit ip object-group NLH.DI.GEDEVICES object GESUPPORT.INTERNAL.NET 
access-list outside_cryptomap_7 extended permit ip object-group MDI.GROUP object-group PATIENT.CONNECT.ARTERA.INTERNAL.PEER.GROUP 
access-list outside_cryptomap_880 extended permit ip object PACS_VCE object-group AAI.NYOH.PACS 
access-list outside_pnat_inbound extended permit ip object LAN object TheOutsourceGroup inactive 
access-list inside_pnat_outbound_V2 extended permit ip object LAN object TheOutsourceGroup inactive 
access-list outside_cryptomap_1000_1 extended permit ip object pacs.net_1 object MVOatJSC.net inactive 
access-list outside_cryptomap_13 extended permit ip object NLH.Firewall.Range.Internal object Clearwater.Internal.Peer.Range 
access-list outside_cryptomap_16 extended permit ip object-group MDI.GROUP object MMI.BILLING.INTERNAL.PEER 
access-list outside_cryptomap_1120 extended permit ip object SENTRYDS.NET object-group DM_INLINE_NETWORK_9 
access-list inside_pnat_outbound_V3 extended permit ip object MAGICA object SENTRYDS.NET 
access-list inside_pnat_outbound_V3 extended permit ip object MAGICA object SENTRYDS 
access-list inside_pnat_outbound_V3 extended permit ip object MDILIVE.NLH.ORG object SENTRYDS.NET 
access-list inside_pnat_outbound_V4 extended permit ip object MDILIVE.NLH.ORG object SENTRYDS.NET 
access-list inside_pnat_outbound_V4 extended permit ip object MDILIVE.NLH.ORG object SENTRYDS 
access-list inside_pnat_outbound_V4 extended permit ip object NLHCITRIXGATEWAY object SENTRYDS 
access-list outside_cryptomap_17 extended permit ip object NUVODIA.INTERNAL.NEW.1 object Nuvodia.OneOncology.Cloud.Internal.Peer 
access-list inside_pnat_outbound_V14 extended permit ip object MDILIVE.NLH.ORG object SENTRYDS 
access-list inside_pnat_outbound_V15 extended permit ip object MAGICA object SENTRYDS 
access-list outside_cryptomap_5 extended permit ip object-group MDI.GROUP object-group Schumacher.Inside 
access-list outside_cryptomap_1300 extended permit ip object-group Quest.NLH2Quest.Internal object questlab 
access-list sfr_redirect extended permit ip any any 
access-list Guest_access_in extended deny ip object GUEST_NETWORK ************* ************* 
access-list Guest_access_in extended deny ip object GUEST_NETWORK ************* ************* 
access-list Guest_access_in extended permit object-group DM_INLINE_SERVICE_1 object GUEST_NETWORK any 
access-list Vendor_access_in extended deny ip ********** ************* ************* ************* 
access-list Vendor_access_in extended permit ip ********** ************* object NETSCALER.WEB 
access-list Vendor_access_in remark For blocking VENDOR -> DMZ
access-list Vendor_access_in extended deny ip ********** ************* ************* ************* 
access-list Vendor_access_in extended permit ip ********** ************* any 
access-list AnyConnect_Client_Local_Print extended deny ip any4 any4 
access-list AnyConnect_Client_Local_Print extended permit tcp any4 any4 eq lpd 
access-list AnyConnect_Client_Local_Print remark IPP: Internet Printing Protocol
access-list AnyConnect_Client_Local_Print extended permit tcp any4 any4 eq 631 
access-list AnyConnect_Client_Local_Print remark Windows' printing port
access-list AnyConnect_Client_Local_Print extended permit tcp any4 any4 eq 9100 
access-list AnyConnect_Client_Local_Print remark mDNS: multicast DNS protocol
access-list AnyConnect_Client_Local_Print extended permit udp any4 host *********** eq 5353 
access-list AnyConnect_Client_Local_Print remark LLMNR: Link Local Multicast Name Resolution protocol
access-list AnyConnect_Client_Local_Print extended permit udp any4 host *********** eq 5355 
access-list AnyConnect_Client_Local_Print remark TCP/NetBIOS protocol
access-list AnyConnect_Client_Local_Print extended permit tcp any4 any4 eq 137 
access-list AnyConnect_Client_Local_Print extended permit udp any4 any4 eq netbios-ns 
access-list outside_cryptomap_4 extended permit ip object-group BACKLINE.LDAP.NLH.INTERNAL object BACKLINE.LDAP.INTERNAL 
access-list outside_cryptomap_8 extended permit ip object-group BACKLINE.LDAP.NLH.INTERNAL object BACKLINE.LDAP.INTERNAL2 
pager lines 50
logging enable
logging timestamp
logging list Warning_Messages level warnings
logging buffer-size 52428800
logging monitor debugging
logging buffered informational
logging trap debugging
logging asdm debugging
logging host inside ************** format emblem
logging host outside ************** 6/1514
mtu outside 1500
mtu inside 1500
mtu DMZ 1500
mtu Guest 1500
mtu Vendor 1500
no failover
no failover wait-disable
no monitor-interface service-module 
icmp unreachable rate-limit 1 burst-size 1
icmp permit any outside
icmp permit any inside
asdm image disk0:/asdm-openjre-7131-101.bin
asdm history enable
arp timeout 14400
no arp permit-nonconnected
arp rate-limit 16384
nat (inside,any) source static MAGICA MAGICA destination static questlab questlab no-proxy-arp route-lookup
nat (inside,any) source static DM_INLINE_NETWORK_7 DM_INLINE_NETWORK_7 destination static MVOrtho.net MVOrtho.net no-proxy-arp route-lookup
nat (inside,any) source static MDILIVE.NLH.ORG MDILIVE.NLH.ORG destination static Medent.Interface Medent.Interface no-proxy-arp route-lookup
nat (inside,any) source static MilleniumPACSnat MilleniumPACSnat destination static MilleniumPACS MilleniumPACS no-proxy-arp route-lookup
nat (inside,any) source static RAD.PACS.READ RAD.PACS.READ destination static SMHA.RAD SMHA.RAD no-proxy-arp route-lookup
nat (inside,any) source static LAN LAN destination static MVOrtho.net MVOrtho.net no-proxy-arp route-lookup
nat (inside,any) source static MDITEST MDITEST destination static Medent.Interface Medent.Interface no-proxy-arp route-lookup
nat (inside,any) source static MDILIVE.NLH.ORG MDILIVE.NLH.ORG destination static Schumacher.Inside Schumacher.Inside no-proxy-arp route-lookup
nat (inside,any) source static MDITEST MDITEST destination static Schumacher.Inside Schumacher.Inside no-proxy-arp route-lookup
nat (inside,any) source static CITRIX_INTERNAL_TO_DMZ CITRIX_INTERNAL_TO_DMZ destination static CITRIXGATEWAY.DMZ CITRIXGATEWAY.DMZ no-proxy-arp route-lookup
nat (inside,any) source static IT_DEPT IT_DEPT destination static CITRIXGATEWAY.DMZ CITRIXGATEWAY.DMZ no-proxy-arp route-lookup
nat (inside,any) source static LAN-01 LAN-01 destination static LabCorp3 LabCorp3 no-proxy-arp route-lookup
nat (inside,any) source static LAN-01 LAN-01 destination static LabCorpDev LabCorpDev no-proxy-arp route-lookup
nat (inside,any) source static LAN-01 LAN-01 destination static LabCorpProd LabCorpProd no-proxy-arp route-lookup
nat (inside,any) source static MDILIVE.NLH.ORG MDILIVE.NLH.ORG destination static obj-************ obj-************ no-proxy-arp route-lookup
nat (inside,outside) source static NLHEXCHANGE.NLH.ORG mail.nlh.org service obj-tcp-source-eq-25 obj-tcp-source-eq-25
nat (inside,outside) source static NLHTEST01 interface service obj-tcp-source-eq-3389 obj-tcp-source-eq-3389
nat (inside,outside) source static NLHTESTMOBILE NOVA.NLH.ORG.EXTERNAL service obj-tcp-source-eq-3389 obj-tcp-source-eq-3389
nat (inside,any) source static NOVA-QIE.INTERNAL NOVA.NLH.ORG.EXTERNAL service NOVA-8070-TCP NOVA-8070-TCP inactive
nat (inside,any) source static NOVA-QIE.INTERNAL NOVA.NLH.ORG.EXTERNAL service obj-tcp-source-eq-80 obj-tcp-source-eq-80
nat (inside,any) source static NOVA-QIE.INTERNAL NOVA.NLH.ORG.EXTERNAL service obj-tcp-source-eq-443 obj-tcp-source-eq-443
nat (inside,outside) source static NLI-T-BG01 DIRECT.NLH.ORG service DIRECT.MESSAGE.TEST.NLI DIRECT.MESSAGE.TEST.NLI
nat (inside,any) source static NLI-BG04 DIRECT.NLH.ORG service DIRECT.MESSAGE.LIVE.NLI DIRECT.MESSAGE.LIVE.NLI
nat (inside,outside) source static NLHEXCHANGE.NLH.ORG mail.nlh.org service obj-tcp-source-eq-443 obj-tcp-source-eq-443
nat (DMZ,outside) source static PATIENTPORTAL.DMZ PATIENTPORTAL.EXTERNAL service obj-tcp-source-eq-443 obj-tcp-source-eq-443
nat (DMZ,outside) source static PATIENTPORTAL.DMZ PATIENTPORTAL.EXTERNAL service PATIENTPORTALTEST PATIENTPORTALTEST
nat (DMZ,outside) source static mtrestexpapis-live01.nlh.org.DMZ mtrestexpapis-live01.nlh.org.external service obj-tcp-source-eq-443 obj-tcp-source-eq-443
nat (DMZ,outside) source static mtrestexpapis-test01.nlh.org.DMZ mtrestexpapis-test01.nlh.org.external service obj-tcp-source-eq-443 obj-tcp-source-eq-443
nat (inside,outside) source static MDILIVE.NLH.ORG obj-*********** destination static SENTRYDS SENTRYDS
nat (inside,outside) source static MDITEST MDITEST_SENDTRYDS destination static SENTRYDS SENTRYDS
nat (inside,outside) source static NLI-BG04 NLI-BG04.CHC.NAT destination static CHC.OPTUM.NAT.INTERNAL.SUB CHC.OPTUM.NAT.INTERNAL.SUB
nat (inside,outside) source static MDITEST MDITEST.CHC.NAT destination static CHC.OPTUM.NAT.INTERNAL.SUB CHC.OPTUM.NAT.INTERNAL.SUB inactive
nat (inside,outside) source static NLI.T.BG01 NLI-T-BG01.CHC.NAT destination static CHC.OPTUM.NAT.INTERNAL.SUB CHC.OPTUM.NAT.INTERNAL.SUB
nat (DMZ,outside) source static NLH-ISWEB.DMZVR DIRECT.NLH.ORG service obj-tcp-source-eq-443 obj-tcp-source-eq-443
nat (inside,outside) source dynamic LAN obj-************ destination static TheOutsourceGroup TheOutsourceGroup
nat (inside,outside) source static MAGICA MAGICA destination static questlab questlab no-proxy-arp route-lookup
nat (inside,outside) source static MDILIVE.NLH.ORG MDILIVE.NLH.ORG destination static Schumacher.Inside Schumacher.Inside no-proxy-arp route-lookup
nat (inside,outside) source static MAGICA MAGICA destination static SENTRYDS.NET SENTRYDS.NET no-proxy-arp route-lookup
nat (inside,outside) source static Group_************ Group_************ destination static Schumacher.Inside Schumacher.Inside no-proxy-arp route-lookup
nat (inside,outside) source static Group_************* Group_************* destination static MVOatJSC.net MVOatJSC.net no-proxy-arp route-lookup
nat (inside,outside) source static Group_************* Group_************* destination static Medent.Interface Medent.Interface no-proxy-arp route-lookup inactive
nat (inside,outside) source static MDI_Group MDI_Group destination static HIXNY.MBMS.INTERNAL HIXNY.MBMS.INTERNAL no-proxy-arp route-lookup
nat (any,any) source static NETSCALER.WEB remote.nlh.org service obj-tcp-source-eq-443 obj-tcp-source-eq-443
nat (any,any) source static DUOTEST.NLH.ORG.DMZ DUOTEST.NLH.ORG service obj-tcp-source-eq-443 obj-tcp-source-eq-443
nat (inside,outside) source static NUVODIA_VPN_NLH_PEER NUVODIA_VPN_NLH_PEER destination static MVOatJSC.net MVOatJSC.net no-proxy-arp route-lookup
nat (inside,outside) source static NUVODIA_VPN_NLH_PEER NUVODIA_VPN_NLH_PEER destination static ALBANYMED.IN ALBANYMED.IN no-proxy-arp route-lookup
nat (inside,outside) source static NLHDC01.NLH.ORG NLHDC01.NLH.ORG destination static NETWORK_OBJ_************** NETWORK_OBJ_************** no-proxy-arp route-lookup
nat (inside,outside) source static NLHDC01.NLH.ORG NLHDC01.NLH.ORG destination static BACKLINE.LDAP.INTERNAL2 BACKLINE.LDAP.INTERNAL2 no-proxy-arp route-lookup
nat (inside,outside) source static Quest.NLH2Quest.Internal Quest.NLH2Quest.Internal destination static questlab questlab no-proxy-arp route-lookup
nat (any,outside) source static CHANGE.HEALTHCARE.NLH.INTERNAL.GROUP CHANGE.HEALTHCARE.NLH.INTERNAL.GROUP destination static CHANGE.HEALTHCARE.EXTERNAL.GROUP CHANGE.HEALTHCARE.EXTERNAL.GROUP no-proxy-arp route-lookup inactive
nat (DMZ,outside) source static NLI.INTERNAL.NAT.CHC NLI.INTERNAL.NAT.CHC destination static CHANGE.HEALTHCARE.EXTERNAL.GROUP CHANGE.HEALTHCARE.EXTERNAL.GROUP no-proxy-arp route-lookup inactive
nat (inside,outside) source static NLI-BG-GROUP NLI-BG-GROUP destination static HIXNY.INTERNAL.GROUP HIXNY.INTERNAL.GROUP no-proxy-arp route-lookup
nat (inside,outside) source static QUEST.VPN.INTERNAL.GROUP.2 QUEST.VPN.INTERNAL.GROUP.2 destination static QUEST.VPN.EXTERNAL.2 QUEST.VPN.EXTERNAL.2 no-proxy-arp route-lookup
nat (inside,outside) source static DM_INLINE_NETWORK_11 DM_INLINE_NETWORK_11 destination static NETWORK_OBJ_*************** NETWORK_OBJ_*************** no-proxy-arp route-lookup
nat (inside,outside) source static DM_INLINE_NETWORK_11 DM_INLINE_NETWORK_11 destination static NETWORK_OBJ_************ NETWORK_OBJ_************ no-proxy-arp route-lookup
nat (inside,outside) source static HEALTHTOUCH.NLH.INTERNAL HEALTHTOUCH.NLH.INTERNAL destination static HEALTHTOUCH.PEER.INTERNAL.1 HEALTHTOUCH.PEER.INTERNAL.1 no-proxy-arp route-lookup
nat (any,outside) source static DM_INLINE_NETWORK_4 DM_INLINE_NETWORK_4 destination static AMC.PACS.NEW AMC.PACS.NEW no-proxy-arp route-lookup
nat (DMZ,outside) source static NUVODIA.INTERNAL.PEER.NET1 NUVODIA.INTERNAL.PEER.NET1 destination static AMC.PACS.NEW AMC.PACS.NEW no-proxy-arp route-lookup
nat (any,outside) source static LAN LAN destination static DM_INLINE_NETWORK_6 DM_INLINE_NETWORK_6 no-proxy-arp route-lookup
nat (DMZ,outside) source static LAN LAN destination static STRATEGICSOLUTIONS.EXTERNAL.GROUP STRATEGICSOLUTIONS.EXTERNAL.GROUP no-proxy-arp route-lookup
nat (inside,outside) source static MDI.GROUP MDI.GROUP destination static HIXNY.INTERNAL.GROUP HIXNY.INTERNAL.GROUP no-proxy-arp route-lookup
nat (inside,outside) source static NUVODIA_VPN_NLH_PEER NUVODIA_VPN_NLH_PEER destination static Ellis.inside Ellis.inside no-proxy-arp route-lookup
nat (inside,outside) source static NUVODIA_VPN_NLH_PEER NUVODIA_VPN_NLH_PEER destination static SMHA.RAD.NEW SMHA.RAD.NEW no-proxy-arp route-lookup
nat (inside,outside) source static NUVODIA_VPN_NLH_PEER2 NUVODIA_VPN_NLH_PEER2 destination static SMHA.RAD.NEW SMHA.RAD.NEW no-proxy-arp route-lookup
nat (inside,outside) source static NUVODIA.VPN.SENDPOINT.MASTER NUVODIA.VPN.SENDPOINT.MASTER destination static NYOH.INTERNAL.NET NYOH.INTERNAL.NET no-proxy-arp route-lookup
nat (inside,outside) source static NLI.INTERNAL.NAT.CHC NLI.INTERNAL.NAT.CHC destination static CHC.OPTUM.NAT.INTERNAL.SUB CHC.OPTUM.NAT.INTERNAL.SUB no-proxy-arp route-lookup
nat (inside,outside) source static DM_INLINE_NETWORK_3 DM_INLINE_NETWORK_3 destination static CHC.OPTUM.NAT.INTERNAL.SUB CHC.OPTUM.NAT.INTERNAL.SUB no-proxy-arp route-lookup inactive
nat (inside,outside) source static NLH.DI.GEDEVICES NLH.DI.GEDEVICES destination static GESUPPORT.INTERNAL.NET GESUPPORT.INTERNAL.NET no-proxy-arp route-lookup
nat (any,outside) source static MDI.GROUP MDI.GROUP destination static HIXNY.MBMS.MILLENIUMBILLING.INTERNAL1 HIXNY.MBMS.MILLENIUMBILLING.INTERNAL1 no-proxy-arp route-lookup
nat (inside,outside) source static PATIENT.CONNECT.ARTERA.INTERNAL.PEER.GROUP PATIENT.CONNECT.ARTERA.INTERNAL.PEER.GROUP destination static MDI.GROUP MDI.GROUP no-proxy-arp route-lookup
nat (inside,outside) source static MDI.GROUP MDI.GROUP destination static PATIENT.CONNECT.ARTERA.INTERNAL.PEER.GROUP PATIENT.CONNECT.ARTERA.INTERNAL.PEER.GROUP no-proxy-arp route-lookup
nat (inside,outside) source static NLH.Firewall.Range.Internal NLH.Firewall.Range.Internal destination static Clearwater.Internal.Peer.Range Clearwater.Internal.Peer.Range no-proxy-arp route-lookup
nat (inside,outside) source static NETWORK_OBJ_192.168.178.2 NETWORK_OBJ_192.168.178.2 destination static Clearwater.Internal.Peer.Range Clearwater.Internal.Peer.Range no-proxy-arp route-lookup
nat (inside,outside) source static NETWORK_OBJ_192.168.178.2 NETWORK_OBJ_192.168.178.2 destination static CLEARWATER1 CLEARWATER1 no-proxy-arp route-lookup
nat (inside,outside) source static MDI.GROUP MDI.GROUP destination static MMI.BILLING.INTERNAL.PEER MMI.BILLING.INTERNAL.PEER no-proxy-arp route-lookup
nat (inside,outside) source static NETWORK_OBJ_162.245.33.10 NETWORK_OBJ_162.245.33.10 destination static Nuvodia.OneOncology.Cloud.Internal.Peer Nuvodia.OneOncology.Cloud.Internal.Peer no-proxy-arp route-lookup
nat (inside,outside) source static NUVODIA.INTERNAL.NEW.1 NUVODIA.INTERNAL.NEW.1 destination static Nuvodia.OneOncology.Cloud.Internal.Peer Nuvodia.OneOncology.Cloud.Internal.Peer no-proxy-arp route-lookup
!
object network obj_any
 nat (inside,outside) dynamic interface
object network GUEST_WLAN_NAT
 nat (Guest,outside) dynamic GUEST_INTERFACE_EXTERNAL
object network VENDOR_WLAN_NAT
 nat (Vendor,outside) dynamic VENDOR_EXTERNAL_INTERFACE
access-group outside_access_in in interface outside
access-group inside_access_in in interface inside
access-group DMZ_access_in_V1 in interface DMZ
access-group Guest_access_in in interface Guest
access-group Vendor_access_in in interface Vendor
route outside 0.0.0.0 0.0.0.0 208.125.81.161 1
route inside Guest_Wireless ************* ************** 1
route inside 10.10.21.0 ************* ************** 1
route inside ********** ************* ************** 1
route inside ********** ************* ************** 1
route inside ********** ************* ************** 1
route inside STUDENT_VLAN ************* ************** 1
route DMZ ************ *************55 Ivans.Gateway 1
route DMZ ************* *************55 Ivans.Gateway 1
route DMZ ************* *************55 Ivans.Gateway 1
route DMZ *********** *************55 Ivans.Gateway 1
route DMZ ************ *************55 Ivans.Gateway 1
route DMZ ATT_VPN_HOST *************55 Ivans.Gateway 1
route DMZ ************* *************55 Ivans.Gateway 1
route DMZ ATT_CERT *************55 Ivans.Gateway 1
route DMZ ************* *************55 Ivans.Gateway 1
route DMZ ************* *************55 Ivans.Gateway 1
route DMZ ************* *************55 Ivans.Gateway 1
route DMZ ************** *************55 Ivans.Gateway 1
route inside DI.NETold ************* ************** 1
route DMZ *********** *************55 Ivans.Gateway 1
route DMZ *********** *************55 Ivans.Gateway 1
route DMZ ************ *************55 Ivans.Gateway 1
route DMZ ************ *************55 Ivans.Gateway 1
route DMZ ************* *************55 Ivans.Gateway 1
route DMZ ************* *************55 Ivans.Gateway 1
route DMZ ************* *************55 Ivans.Gateway 1
route DMZ ************ *************55 Ivans.Gateway 1
route DMZ ************* *************55 Ivans.Gateway 1
route inside ************ *************55 ************** 1
route inside ************ *************55 ************** 1
route DMZ 165.87.194.243 *************55 Ivans.Gateway 1
route DMZ 165.87.194.246 *************55 Ivans.Gateway 1
route inside GEInsight ************* ************** 1
route DMZ 192.5.41.209 *************55 Ivans.Gateway 1
route DMZ 192.43.244.18 *************55 Ivans.Gateway 1
route inside NUVODIA_PACS_**************-28 *************40 ************** 1
route inside NUVODIA_PACS_************-23 ************* ************** 1
route inside ************* *************48 ************** 1
route inside PACS_VCE ************* ************** 1
route inside DI.NET *************** ************** 1
route inside pacs.net *************** ************** 1
route inside 192.168.252.0 ************* ************** 1
route inside 192.168.254.0 ************* ************** 1
route DMZ 204.146.166.105 *************55 Ivans.Gateway 1
route DMZ 204.146.172.225 *************55 Ivans.Gateway 1
route DMZ *************** *************55 Ivans.Gateway 1
route DMZ ************* *************55 Ivans.Gateway 1
route DMZ *************** *************55 Ivans.Gateway 1
route DMZ ************ *************55 Ivans.Gateway 1
route DMZ ************ *************55 Ivans.Gateway 1
timeout xlate 3:00:00
timeout pat-xlate 0:00:30
timeout conn 1:00:00 half-closed 0:10:00 udp 0:02:00 sctp 0:02:00 icmp 0:00:02
timeout sunrpc 0:10:00 h323 0:05:00 h225 1:00:00 mgcp 0:05:00 mgcp-pat 0:05:00
timeout sip 0:30:00 sip_media 0:02:00 sip-invite 0:03:00 sip-disconnect 0:02:00
timeout sip-provisional-media 0:02:00 uauth 0:05:00 absolute
timeout tcp-proxy-reassembly 0:01:00
timeout floating-conn 0:00:00
timeout conn-holddown 0:00:15
timeout igp stale-route 0:01:10
user-identity default-domain LOCAL
aaa authentication ssh console LOCAL 
aaa authentication login-history
http server enable
http 0.0.0.0 0.0.0.0 inside
http ************* *************55 outside
http ************* *************55 inside
http 6************* *************55 outside
snmp-server host inside ************** community nlhpublic version 2c
no snmp-server location
no snmp-server contact
snmp-server community nlhpublic
crypto ipsec ikev1 transform-set ESP-3DES-SHA esp-3des esp-sha-hmac 
crypto ipsec ikev1 transform-set ESP-3DES-MD5 esp-3des esp-md5-hmac 
crypto ipsec ikev1 transform-set ESP-DES-SHA esp-des esp-sha-hmac 
crypto ipsec ikev1 transform-set ESP-AES-256-SHA esp-aes-256 esp-sha-hmac 
crypto ipsec ikev1 transform-set ESP-AES-128-SHA esp-aes esp-sha-hmac 
crypto ipsec ikev1 transform-set ESP-AES-128-MD5 esp-aes esp-md5-hmac 
crypto ipsec ikev1 transform-set ESP-AES-192-SHA esp-aes-192 esp-sha-hmac 
crypto ipsec ikev1 transform-set ESP-AES-192-MD5 esp-aes-192 esp-md5-hmac 
crypto ipsec ikev1 transform-set ESP-AES-256-MD5 esp-aes-256 esp-md5-hmac 
crypto ipsec ikev1 transform-set ESP-AES-128-SHA-TRANS esp-aes esp-sha-hmac 
crypto ipsec ikev1 transform-set ESP-AES-128-SHA-TRANS mode transport
crypto ipsec ikev1 transform-set ESP-AES-128-MD5-TRANS esp-aes esp-md5-hmac 
crypto ipsec ikev1 transform-set ESP-AES-128-MD5-TRANS mode transport
crypto ipsec ikev1 transform-set ESP-AES-192-SHA-TRANS esp-aes-192 esp-sha-hmac 
crypto ipsec ikev1 transform-set ESP-AES-192-SHA-TRANS mode transport
crypto ipsec ikev1 transform-set ESP-AES-192-MD5-TRANS esp-aes-192 esp-md5-hmac 
crypto ipsec ikev1 transform-set ESP-AES-192-MD5-TRANS mode transport
crypto ipsec ikev1 transform-set ESP-AES-256-SHA-TRANS esp-aes-256 esp-sha-hmac 
crypto ipsec ikev1 transform-set ESP-AES-256-SHA-TRANS mode transport
crypto ipsec ikev1 transform-set ESP-AES-256-MD5-TRANS esp-aes-256 esp-md5-hmac 
crypto ipsec ikev1 transform-set ESP-AES-256-MD5-TRANS mode transport
crypto ipsec ikev1 transform-set ESP-3DES-SHA-TRANS esp-3des esp-sha-hmac 
crypto ipsec ikev1 transform-set ESP-3DES-SHA-TRANS mode transport
crypto ipsec ikev1 transform-set ESP-3DES-MD5-TRANS esp-3des esp-md5-hmac 
crypto ipsec ikev1 transform-set ESP-3DES-MD5-TRANS mode transport
crypto ipsec ikev1 transform-set ESP-DES-MD5 esp-des esp-md5-hmac 
crypto ipsec ikev1 transform-set ESP-DES-SHA-TRANS esp-des esp-sha-hmac 
crypto ipsec ikev1 transform-set ESP-DES-SHA-TRANS mode transport
crypto ipsec ikev1 transform-set ESP-DES-MD5-TRANS esp-des esp-md5-hmac 
crypto ipsec ikev1 transform-set ESP-DES-MD5-TRANS mode transport
crypto ipsec ikev1 transform-set CHC esp-aes-256 esp-sha-hmac 
crypto ipsec ikev1 transform-set MBMS esp-aes-256 esp-sha-hmac 
crypto ipsec ikev2 ipsec-proposal DES
 protocol esp encryption des
 protocol esp integrity sha-1 md5
crypto ipsec ikev2 ipsec-proposal 3DES
 protocol esp encryption 3des
 protocol esp integrity sha-1 md5
crypto ipsec ikev2 ipsec-proposal AES
 protocol esp encryption aes
 protocol esp integrity sha-1 md5
crypto ipsec ikev2 ipsec-proposal AES192
 protocol esp encryption aes-192
 protocol esp integrity sha-1 md5
crypto ipsec ikev2 ipsec-proposal AES256
 protocol esp encryption aes-256
 protocol esp integrity sha-1 md5
crypto ipsec ikev2 ipsec-proposal SCHUMACHER
 protocol esp encryption aes-256
 protocol esp integrity sha-256
crypto ipsec ikev2 ipsec-proposal CHC
 protocol esp encryption aes-256
 protocol esp integrity sha-256
crypto ipsec ikev2 ipsec-proposal AMC
 protocol esp encryption aes-256
 protocol esp integrity sha-1
crypto ipsec ikev2 ipsec-proposal HIXNY
 protocol esp encryption aes-256
 protocol esp integrity sha-512
crypto ipsec ikev2 ipsec-proposal SMHA
 protocol esp encryption aes-256
 protocol esp integrity sha-256
crypto ipsec ikev2 ipsec-proposal BACKLINEDR1
 protocol esp encryption aes-256
 protocol esp integrity sha-256
crypto ipsec ikev2 ipsec-proposal STRATEGICSOL
 protocol esp encryption aes-256
 protocol esp integrity sha-256
crypto ipsec ikev2 ipsec-proposal QUEST
 protocol esp encryption aes-256
 protocol esp integrity sha-256
crypto ipsec ikev2 ipsec-proposal SENTRYDS
 protocol esp encryption aes-256
 protocol esp integrity sha-256
crypto ipsec ikev2 ipsec-proposal NYOH
 protocol esp encryption aes-256
 protocol esp integrity sha-256
crypto ipsec ikev2 ipsec-proposal GESUPPORT
 protocol esp encryption aes-256
 protocol esp integrity sha-256
crypto ipsec ikev2 ipsec-proposal MBMS
 protocol esp encryption aes-gcm-256
 protocol esp integrity sha-256
crypto ipsec ikev2 ipsec-proposal PC.ARTERA
 protocol esp encryption aes-256
 protocol esp integrity sha-256
crypto ipsec ikev2 ipsec-proposal Clearwater
 protocol esp encryption aes-gcm-256
 protocol esp integrity sha-256
crypto ipsec ikev2 ipsec-proposal MMI.BILLING
 protocol esp encryption aes-256
 protocol esp integrity sha-256
crypto ipsec security-association pmtu-aging infinite
crypto map outside_map 1 match address outside_cryptomap_12
crypto map outside_map 1 set pfs group19
crypto map outside_map 1 set peer MBMS.MILLENIUMBILLING.EXTERNAL.PEER 
crypto map outside_map 1 set ikev2 ipsec-proposal MBMS
crypto map outside_map 1 set ikev2 pre-shared-key 2m9Ji46*ufKLotcK
crypto map outside_map 2 match address outside_cryptomap_11
crypto map outside_map 2 set peer HIXNY.VPN.EXTERNAL.PEER 
crypto map outside_map 2 set ikev2 ipsec-proposal HIXNY
crypto map outside_map 3 match address outside_cryptomap_15
crypto map outside_map 3 set pfs group19
crypto map outside_map 3 set peer ************** 
crypto map outside_map 3 set ikev2 ipsec-proposal NYOH
crypto map outside_map 3 set ikev2 pre-shared-key nlHtoAiiwlUDLO$5@90Ns1G
crypto map outside_map 4 match address outside_cryptomap_3
crypto map outside_map 4 set peer *************** 
crypto map outside_map 4 set ikev2 ipsec-proposal AMC
crypto map outside_map 5 match address outside_cryptomap_4
crypto map outside_map 5 set pfs group14
crypto map outside_map 5 set peer BACKLINE.VPN.EXTERNAL.PEER 
crypto map outside_map 5 set ikev2 ipsec-proposal BACKLINEDR1
crypto map outside_map 5 set ikev2 pre-shared-key emhi@ZSi2&hfxeG#j
crypto map outside_map 6 match address outside_cryptomap_5
crypto map outside_map 6 set pfs group20
crypto map outside_map 6 set peer SCHUMACHER.VPN.EXTERNAL.PEER 
crypto map outside_map 6 set ikev2 ipsec-proposal SCHUMACHER
crypto map outside_map 6 set ikev2 pre-shared-key on768tp75
crypto map outside_map 7 match address outside_cryptomap_6
crypto map outside_map 7 set pfs group14
crypto map outside_map 7 set peer QUEST.VPN.EXTERNAL.PEER.2 
crypto map outside_map 7 set ikev2 ipsec-proposal QUEST
crypto map outside_map 7 set ikev2 pre-shared-key #?!NLH2QU35T02!?#
crypto map outside_map 8 match address outside_cryptomap_10
crypto map outside_map 8 set pfs group19
crypto map outside_map 8 set peer ************** 
crypto map outside_map 8 set ikev2 ipsec-proposal CHC
crypto map outside_map 8 set ikev2 pre-shared-key w+zgSV+nut3rbOhWNJ07vfIflStsTazw
crypto map outside_map 8 set security-association lifetime seconds 28800
crypto map outside_map 9 match address outside_cryptomap_8
crypto map outside_map 9 set pfs group14
crypto map outside_map 9 set peer BACKLINE.VPN.EXTERNAL.PEER.LDAP 
crypto map outside_map 9 set ikev2 ipsec-proposal BACKLINEDR1
crypto map outside_map 9 set ikev2 pre-shared-key emhi@ZSi2&hfxeG#j
crypto map outside_map 9 set nat-t-disable
crypto map outside_map 10 match address outside_cryptomap_9
crypto map outside_map 10 set pfs 
crypto map outside_map 10 set peer STRATEGIC.VPN.EXTERNAL.PEER 
crypto map outside_map 10 set ikev2 ipsec-proposal STRATEGICSOL
crypto map outside_map 10 set ikev2 pre-shared-key !5M3LL!5!
crypto map outside_map 11 match address outside_cryptomap_14
crypto map outside_map 11 set pfs group14
crypto map outside_map 11 set peer SMHA.RAD.EXTERNAL.PEER 
crypto map outside_map 11 set ikev2 ipsec-proposal SMHA
crypto map outside_map 11 set ikev2 pre-shared-key Nlittauer091127#(%@(FpdbR
crypto map outside_map 12 match address outside_cryptomap_1
crypto map outside_map 12 set pfs group19
crypto map outside_map 12 set peer ********** 
crypto map outside_map 12 set ikev2 ipsec-proposal SCHUMACHER
crypto map outside_map 13 match address outside_cryptomap_2
crypto map outside_map 13 set pfs group19
crypto map outside_map 13 set peer GE.VPN 
crypto map outside_map 13 set ikev2 ipsec-proposal GESUPPORT
crypto map outside_map 14 match address outside_cryptomap_7
crypto map outside_map 14 set peer ************** 
crypto map outside_map 14 set ikev2 ipsec-proposal PC.ARTERA
crypto map outside_map 15 match address outside_cryptomap
crypto map outside_map 15 set peer MBMS.MILLENIUMBILLING.EXTERNAL.PEER 
crypto map outside_map 15 set ikev1 transform-set ESP-AES-128-SHA ESP-AES-128-MD5 ESP-AES-192-SHA ESP-AES-192-MD5 ESP-AES-256-SHA ESP-AES-256-MD5 ESP-3DES-SHA ESP-3DES-MD5 ESP-DES-SHA ESP-DES-MD5
crypto map outside_map 15 set ikev2 ipsec-proposal DES 3DES AES AES192 AES256 SCHUMACHER CHC AMC HIXNY
crypto map outside_map 16 match address outside_cryptomap_13
crypto map outside_map 16 set pfs group19
crypto map outside_map 16 set peer ************ 
crypto map outside_map 16 set ikev2 ipsec-proposal Clearwater
crypto map outside_map 16 set ikev2 pre-shared-key qu!etCoal71
crypto map outside_map 17 match address outside_cryptomap_16
crypto map outside_map 17 set peer ************ 
crypto map outside_map 17 set ikev2 ipsec-proposal MMI.BILLING
crypto map outside_map 18 match address outside_cryptomap_17
crypto map outside_map 18 set pfs group14
crypto map outside_map 18 set peer ************ 
crypto map outside_map 18 set ikev2 ipsec-proposal NYOH
crypto map outside_map 1120 match address outside_cryptomap_1120
crypto map outside_map 1120 set pfs group19
crypto map outside_map 1120 set peer SENTRYDS.VPN.EXTERNAL.PEER 
crypto map outside_map 1120 set ikev2 ipsec-proposal SENTRYDS
crypto map outside_map 1120 set ikev2 pre-shared-key Ltno34UQed29SVac
crypto map outside_map 1300 match address outside_cryptomap_1300
crypto map outside_map 1300 set pfs group14
crypto map outside_map 1300 set peer QUEST.VPN.EXTERNAL.PEER.1 
crypto map outside_map 1300 set ikev2 ipsec-proposal QUEST
crypto map outside_map 1300 set ikev2 pre-shared-key jdnlh@12022004dgx
crypto map outside_map interface outside
crypto ca trustpool policy
crypto ikev2 policy 1
 encryption aes-256
 integrity sha
 group 5 2
 prf sha
 lifetime seconds 86400
crypto ikev2 policy 2
 encryption aes-256
 integrity sha256
 group 5 2
 prf sha512 sha256 sha
 lifetime seconds 28800
crypto ikev2 policy 3
 encryption aes-256
 integrity sha256
 group 14
 prf sha256
 lifetime seconds 86400
crypto ikev2 policy 4
 encryption aes-256
 integrity sha256
 group 14
 prf sha256
 lifetime seconds 28800
crypto ikev2 policy 5
 encryption aes-256
 integrity sha256
 group 2
 prf sha256
 lifetime seconds 86400
crypto ikev2 policy 6
 encryption aes-256
 integrity sha256
 group 19
 prf sha256
 lifetime seconds 86400
crypto ikev2 policy 7
 encryption aes-256
 integrity sha256
 group 14
 prf sha256
 lifetime seconds 86400
crypto ikev2 policy 10
 encryption aes-192
 integrity sha
 group 5 2
 prf sha
 lifetime seconds 86400
crypto ikev2 policy 20
 encryption aes
 integrity sha
 group 5 2
 prf sha
 lifetime seconds 86400
crypto ikev2 policy 30
 encryption 3des
 integrity sha
 group 5 2
 prf sha
 lifetime seconds 86400
crypto ikev2 policy 40
 encryption des
 integrity sha
 group 5 2
 prf sha
 lifetime seconds 86400
crypto ikev2 policy 50
 encryption aes-256
 integrity sha256
 group 24
 prf sha256
 lifetime seconds 86400
crypto ikev2 policy 60
 encryption aes-gcm-256
 integrity null
 group 19
 prf sha256
 lifetime seconds 86400
crypto ikev2 enable outside
crypto ikev1 enable outside
crypto ikev1 policy 30
 authentication pre-share
 encryption 3des
 hash sha
 group 2
 lifetime 86400
crypto ikev1 policy 50
 authentication pre-share
 encryption aes-256
 hash sha
 group 2
 lifetime none
crypto ikev1 policy 60
 authentication pre-share
 encryption aes-256
 hash sha
 group 5
 lifetime none
telnet 0.0.0.0 0.0.0.0 inside
telnet timeout 60
ssh stricthostkeycheck
ssh timeout 60
ssh version 2
ssh key-exchange group dh-group1-sha1
ssh ************* *************55 outside
ssh 6************* *************55 outside
ssh 0.0.0.0 0.0.0.0 inside
ssh ************* *************55 inside
console timeout 0
no vpn-addr-assign aaa
no vpn-addr-assign dhcp
vpn load-balancing 
dhcpd dns NLHDC01 NLHDC02
dhcpd lease 86000
dhcpd domain nlh.org
dhcpd option 3 ip **************
dhcpd option 6 ip NLHDC01 **************
dhcpd option 15 ascii nlh.org
!
dhcpd address **********00-**********30 Guest
dhcpd dns ******* ******* interface Guest
dhcpd lease 1800 interface Guest
dhcpd option 3 ip ********** interface Guest
dhcpd enable Guest
!
dhcpd address **********0-************ Vendor
dhcpd dns ******* ******* interface Vendor
dhcpd lease 1800 interface Vendor
dhcpd option 3 ip ********** interface Vendor
dhcpd enable Vendor
!
threat-detection basic-threat
threat-detection statistics
threat-detection statistics tcp-intercept rate-interval 30 burst-rate 400 average-rate 200
ntp server NLHDC01 source inside prefer
webvpn
 hsts
  enable
  max-age 31536000
  include-sub-domains
  no preload
 anyconnect-essentials
 anyconnect image disk0:/anyconnect-win-3.1.14018-k9.pkg 1
 anyconnect image disk0:/anyconnect-win-2.5.2014-k9.pkg 2
 tunnel-group-list enable
 keepout "Service out temporarily."
 cache
  disable
 error-recovery disable
group-policy SCHUMACHER.SITE2SITE internal
group-policy SCHUMACHER.SITE2SITE attributes
 vpn-tunnel-protocol ikev2 
group-policy DfltGrpPolicy attributes
 vpn-simultaneous-logins 99
 vpn-idle-timeout none
 vpn-tunnel-protocol ikev1 
 split-tunnel-policy tunnelspecified
 split-tunnel-network-list value VPN.nlh.org_splitTunnelAcl
 user-authentication-idle-timeout none
group-policy BACKLINEDR1 internal
group-policy BACKLINEDR1 attributes
 vpn-tunnel-protocol ikev2 
group-policy PC.ARTERA internal
group-policy PC.ARTERA attributes
 vpn-tunnel-protocol ikev2 
group-policy QUEST internal
group-policy QUEST attributes
 vpn-tunnel-protocol ikev2 
group-policy GroupPolicy_NLH_VPN internal
group-policy GroupPolicy_NLH_VPN attributes
 wins-server none
 dns-server value **************
 vpn-tunnel-protocol ssl-client 
 default-domain value nlh.org
group-policy GroupPolicy_************ internal
group-policy GroupPolicy_************ attributes
 vpn-tunnel-protocol ikev2 
group-policy GroupPolicy_************ internal
group-policy GroupPolicy_************ attributes
 vpn-tunnel-protocol ikev1 
group-policy GroupPolicy_*************** internal
group-policy GroupPolicy_*************** attributes
 vpn-tunnel-protocol ikev2 
group-policy GroupPolicy_************** internal
group-policy GroupPolicy_************** attributes
 vpn-tunnel-protocol ikev2 
group-policy GroupPolicy_*********** internal
group-policy GroupPolicy_*********** attributes
 vpn-tunnel-protocol ikev1 
group-policy GroupPolicy_************** internal
group-policy GroupPolicy_************** attributes
 vpn-tunnel-protocol ikev1 
group-policy GroupPolicy_************ internal
group-policy GroupPolicy_************ attributes
 vpn-tunnel-protocol ikev1 
group-policy GroupPolicy_********** internal
group-policy GroupPolicy_********** attributes
 vpn-tunnel-protocol ikev2 
group-policy GroupPolicy_************* internal
group-policy GroupPolicy_************* attributes
 vpn-tunnel-protocol ikev2 
group-policy GroupPolicy_************** internal
group-policy GroupPolicy_************** attributes
 vpn-tunnel-protocol ikev2 
group-policy GroupPolicy_************** internal
group-policy GroupPolicy_************** attributes
 vpn-tunnel-protocol ikev1 ikev2 
group-policy GroupPolicy2 internal
group-policy GroupPolicy2 attributes
 vpn-tunnel-protocol ikev1 
group-policy GroupPolicy_************ internal
group-policy GroupPolicy_************ attributes
 vpn-tunnel-protocol ikev2 
group-policy GroupPolicy1 internal
group-policy GroupPolicy1 attributes
 vpn-tunnel-protocol ikev1 
group-policy GroupPolicy_************* internal
group-policy GroupPolicy_************* attributes
 vpn-tunnel-protocol ikev2 
group-policy GroupPolicy_************ internal
group-policy GroupPolicy_************ attributes
 vpn-tunnel-protocol ikev1 
group-policy GroupPolicy_************** internal
group-policy GroupPolicy_************** attributes
 vpn-tunnel-protocol ikev2 
group-policy GroupPolicy_*************** internal
group-policy GroupPolicy_*************** attributes
 vpn-tunnel-protocol ikev2 
group-policy GroupPolicy_************ internal
group-policy GroupPolicy_************ attributes
 vpn-tunnel-protocol ikev2 
group-policy TRANS.NLH.ORG internal
group-policy TRANS.NLH.ORG attributes
 dns-server value ************** ***************
 vpn-idle-timeout 30
 vpn-filter none
 split-tunnel-policy tunnelspecified
 split-tunnel-network-list value VPN.nlh.org_splitTunnelAcl
 default-domain value nlh.org
group-policy VPN2.nlh.org internal
group-policy VPN2.nlh.org attributes
 wins-server value **************
 dns-server value ************** ***************
 vpn-idle-timeout 30
 vpn-filter none
 split-tunnel-policy tunnelspecified
 split-tunnel-network-list value VPN.nlh.org_splitTunnelAcl
 default-domain value nlh.org
 client-access-rule none
group-policy VPN.nlh.org internal
group-policy VPN.nlh.org attributes
 wins-server value **************
 dns-server value ************** ***************
 vpn-idle-timeout 30
 vpn-filter none
 split-tunnel-policy tunnelspecified
 split-tunnel-network-list value VPN.nlh.org_splitTunnelAcl
 default-domain value nlh.org
group-policy SENTRYDS internal
group-policy SENTRYDS attributes
 vpn-tunnel-protocol ikev2 
group-policy VPN.nlh.org_1 internal
group-policy VPN.nlh.org_1 attributes
 wins-server value **************
 dns-server value ************** ***************
 dhcp-network-scope none
 vpn-filter none
 split-tunnel-policy tunnelspecified
 split-tunnel-network-list value VPN.nlh.org_splitTunnelAcl
 default-domain value nlh.org
group-policy STRATEGICSOL internal
group-policy STRATEGICSOL attributes
 vpn-tunnel-protocol ikev2 
group-policy formsfast.nlh.org internal
group-policy formsfast.nlh.org attributes
 wins-server none
 dns-server value ************** ***************
 vpn-idle-timeout 30
 vpn-filter none
 split-tunnel-policy tunnelspecified
 split-tunnel-network-list value VPN.nlh.org_splitTunnelAcl
 default-domain value nlh.org
group-policy MBMS internal
group-policy MBMS attributes
 vpn-tunnel-protocol ikev2 
dynamic-access-policy-record DfltAccessPolicy
username brubscha password $sha512$5000$4C6uPuSWnppBuOOUFoOMrQ==$Dv2yRKmlKLqXZ8p/kDJhdg== pbkdf2 privilege 15
username cwsupport password $sha512$5000$2BKnby4+R3IZWjBhmTL87w==$knzv4aGizcg84ZMFEH2CaQ== pbkdf2 privilege 15
username ePlus password $sha512$5000$HZUm+0mmXP+narTkNtyyYw==$SU8okrR3YV9+HHVFPmM2Qg== pbkdf2 privilege 15
username wlooman password $sha512$5000$ULVDlhubWiTVt8poYNwwHg==$Hk+L514xyKxv3YjWB7rqog== pbkdf2 privilege 15
username mromell password $sha512$5000$z+Y+RuBzptBCuOGFqN9jzQ==$/IjMxd9LFYGYW25R8YJSZw== pbkdf2 privilege 15
tunnel-group VPN.nlh.org type remote-access
tunnel-group VPN.nlh.org general-attributes
 address-pool VPN2.Pool
 default-group-policy VPN.nlh.org_1
tunnel-group VPN.nlh.org ipsec-attributes
 ikev1 pre-shared-key Nlittauer
 peer-id-validate nocheck
 ikev1 user-authentication none
tunnel-group ************ type ipsec-l2l
tunnel-group ************ general-attributes
 default-group-policy QUEST
tunnel-group ************ ipsec-attributes
 ikev1 pre-shared-key #?!NLH2QU35T02!?#
 ikev2 remote-authentication pre-shared-key #?!NLH2QU35T02!?#
 ikev2 local-authentication pre-shared-key #?!NLH2QU35T02!?#
tunnel-group *************** type ipsec-l2l
tunnel-group *************** general-attributes
 default-group-policy GroupPolicy_***************
tunnel-group *************** ipsec-attributes
 ikev1 pre-shared-key rd62kRs5TP21vbf4382HRs
 ikev2 remote-authentication pre-shared-key rd62kRs5TP21vbf4382HRs
 ikev2 local-authentication pre-shared-key rd62kRs5TP21vbf4382HRs
tunnel-group ************ type ipsec-l2l
tunnel-group ************ general-attributes
 default-group-policy STRATEGICSOL
tunnel-group ************ ipsec-attributes
 ikev1 pre-shared-key !5M3LL!5!
 ikev2 remote-authentication pre-shared-key !5M3LL!5!
 ikev2 local-authentication pre-shared-key !5M3LL!5!
tunnel-group ************** type ipsec-l2l
tunnel-group ************** general-attributes
 default-group-policy GroupPolicy_**************
tunnel-group ************** ipsec-attributes
 ikev2 remote-authentication pre-shared-key w+zgSV+nut3rbOhWNJ07vfIflStsTazw
 ikev2 local-authentication pre-shared-key w+zgSV+nut3rbOhWNJ07vfIflStsTazw
tunnel-group ************** type ipsec-l2l
tunnel-group ************** general-attributes
 default-group-policy GroupPolicy_**************
tunnel-group ************** ipsec-attributes
 ikev1 pre-shared-key y7OBR78017GjQxCu0J3FS1n0Krq8Ax4u
 ikev2 remote-authentication pre-shared-key y7OBR78017GjQxCu0J3FS1n0Krq8Ax4u
 ikev2 local-authentication pre-shared-key y7OBR78017GjQxCu0J3FS1n0Krq8Ax4u
tunnel-group ************* type ipsec-l2l
tunnel-group ************* ipsec-attributes
 ikev1 pre-shared-key mEd7!L$JcHKd
tunnel-group ********** type ipsec-l2l
tunnel-group ********** general-attributes
 default-group-policy GroupPolicy_**********
tunnel-group ********** ipsec-attributes
 ikev1 pre-shared-key cC830>!S&XNa<lRX#Fg
 ikev2 remote-authentication pre-shared-key cC830>!S&XNa<lRX#Fg
 ikev2 local-authentication pre-shared-key cC830>!S&XNa<lRX#Fg
tunnel-group *********** type ipsec-l2l
tunnel-group *********** general-attributes
 default-group-policy MBMS
tunnel-group *********** ipsec-attributes
 ikev1 pre-shared-key 2m9Ji46*ufKLotcK
 ikev2 remote-authentication pre-shared-key 2m9Ji46*ufKLotcK
 ikev2 local-authentication pre-shared-key 2m9Ji46*ufKLotcK
tunnel-group ************** type ipsec-l2l
tunnel-group ************** general-attributes
 default-group-policy GroupPolicy_**************
tunnel-group ************** ipsec-attributes
 ikev2 remote-authentication pre-shared-key nlHtoAiiwlUDLO$5@90Ns1G
 ikev2 local-authentication pre-shared-key nlHtoAiiwlUDLO$5@90Ns1G
tunnel-group ************** type ipsec-l2l
tunnel-group ************** general-attributes
 default-group-policy PC.ARTERA
tunnel-group ************** ipsec-attributes
 ikev1 pre-shared-key 12345678901234567890
 ikev2 remote-authentication pre-shared-key 1e50f26aa0be1792a126c524abd69f7a7ad0eb8eca0c9491edac8c21
 ikev2 local-authentication pre-shared-key 1e50f26aa0be1792a126c524abd69f7a7ad0eb8eca0c9491edac8c21
tunnel-group ************* type ipsec-l2l
tunnel-group ************* general-attributes
 default-group-policy SENTRYDS
tunnel-group ************* ipsec-attributes
 ikev1 pre-shared-key Ltno34UQed29SVac
 ikev2 remote-authentication pre-shared-key Ltno34UQed29SVac
 ikev2 local-authentication pre-shared-key Ltno34UQed29SVac
tunnel-group ************** type ipsec-l2l
tunnel-group ************** general-attributes
 default-group-policy SCHUMACHER.SITE2SITE
tunnel-group ************** ipsec-attributes
 ikev2 remote-authentication pre-shared-key on768tp75
 ikev2 local-authentication pre-shared-key on768tp75
tunnel-group formsfastsup.nlh.org type remote-access
tunnel-group formsfastsup.nlh.org general-attributes
 address-pool VPN2.Pool
 default-group-policy formsfast.nlh.org
tunnel-group formsfastsup.nlh.org ipsec-attributes
 ikev1 pre-shared-key !NLHFormsFast!
 peer-id-validate nocheck
 ikev1 user-authentication none
tunnel-group ************** type ipsec-l2l
tunnel-group ************** general-attributes
 default-group-policy QUEST
tunnel-group ************** ipsec-attributes
 ikev1 pre-shared-key jdnlh@12022004dgx
 ikev2 remote-authentication pre-shared-key jdnlh@12022004dgx
 ikev2 local-authentication pre-shared-key jdnlh@12022004dgx
tunnel-group SGRUBB type remote-access
tunnel-group SGRUBB general-attributes
 address-pool VPN2.Pool
tunnel-group SGRUBB ipsec-attributes
 ikev1 pre-shared-key !NLH_GRUBB01!
 peer-id-validate nocheck
 ikev1 user-authentication none
tunnel-group NLH_VPN type remote-access
tunnel-group NLH_VPN general-attributes
 address-pool VPN2.Pool
 default-group-policy GroupPolicy_NLH_VPN
tunnel-group NLH_VPN webvpn-attributes
 group-alias NLH_VPN enable
tunnel-group *************** type ipsec-l2l
tunnel-group *************** general-attributes
 default-group-policy GroupPolicy_***************
tunnel-group *************** ipsec-attributes
 ikev1 pre-shared-key Nlittauer091127#(%@(FpdbR
 ikev2 remote-authentication pre-shared-key Nlittauer091127#(%@(FpdbR
 ikev2 local-authentication pre-shared-key Nlittauer091127#(%@(FpdbR
tunnel-group ************ type ipsec-l2l
tunnel-group ************ general-attributes
 default-group-policy GroupPolicy_************
tunnel-group ************ ipsec-attributes
 ikev1 pre-shared-key qu!etCoal71
 ikev2 remote-authentication pre-shared-key qu!etCoal71
 ikev2 local-authentication pre-shared-key qu!etCoal71
tunnel-group ************ type ipsec-l2l
tunnel-group ************ general-attributes
 default-group-policy GroupPolicy_************
tunnel-group ************ ipsec-attributes
 ikev1 pre-shared-key hJ@B7e#my$Hw%PtKf3#NN&UTm5
 ikev2 remote-authentication pre-shared-key hJ@B7e#my$Hw%PtKf3#NN&UTm5
 ikev2 local-authentication pre-shared-key hJ@B7e#my$Hw%PtKf3#NN&UTm5
tunnel-group ************* type ipsec-l2l
tunnel-group ************* general-attributes
 default-group-policy GroupPolicy_*************
tunnel-group ************* ipsec-attributes
 ikev2 remote-authentication pre-shared-key rm$Z23jkT6xv2$
 ikev2 local-authentication pre-shared-key rm$Z23jkT6xv2$
tunnel-group ************ type ipsec-l2l
tunnel-group ************ general-attributes
 default-group-policy GroupPolicy_************
tunnel-group ************ ipsec-attributes
 ikev1 pre-shared-key 123456789
 ikev2 remote-authentication pre-shared-key Nt$rFQdXfu7y5N4pNW&N
 ikev2 local-authentication pre-shared-key Nt$rFQdXfu7y5N4pNW&N
tunnel-group ************** type ipsec-l2l
tunnel-group ************** general-attributes
 default-group-policy BACKLINEDR1
tunnel-group ************** ipsec-attributes
 ikev1 pre-shared-key dRupruCHoprOq72
 ikev2 remote-authentication pre-shared-key emhi@ZSi2&hfxeG#j
 ikev2 local-authentication pre-shared-key emhi@ZSi2&hfxeG#j
tunnel-group ************ type ipsec-l2l
tunnel-group ************ general-attributes
 default-group-policy BACKLINEDR1
tunnel-group ************ ipsec-attributes
 ikev1 pre-shared-key dRupruCHoprOq72
 ikev2 remote-authentication pre-shared-key emhi@ZSi2&hfxeG#j
 ikev2 local-authentication pre-shared-key emhi@ZSi2&hfxeG#j
!
class-map sfr
 match access-list sfr_redirect
class-map inspection_default
 match default-inspection-traffic
!
!
policy-map type inspect dns preset_dns_map
 parameters
  message-length maximum client auto
  message-length maximum 512
  no tcp-inspection
policy-map global_policy
 class inspection_default
  inspect dns preset_dns_map 
  inspect ftp 
  inspect h323 h225 
  inspect h323 ras 
  inspect ip-options 
  inspect netbios 
  inspect rsh 
  inspect rtsp 
  inspect skinny  
  inspect sqlnet 
  inspect sunrpc 
  inspect tftp 
  inspect sip  
  inspect xdmcp 
 class sfr
  sfr fail-open monitor-only
 class class-default
  user-statistics accounting
!
service-policy global_policy global
prompt hostname context 
no call-home reporting anonymous
call-home
 profile CiscoTAC-1
  no active
  destination address http https://tools.cisco.com/its/service/oddce/services/DDCEService
  destination <NAME_EMAIL>
  destination transport-method http
  subscribe-to-alert-group diagnostic
  subscribe-to-alert-group environment
  subscribe-to-alert-group inventory periodic monthly 17
  subscribe-to-alert-group configuration periodic monthly 17
  subscribe-to-alert-group telemetry periodic daily
hpm topN enable
Cryptochecksum:61e5c0f16fff85c90c2b883432d656e3
: end
