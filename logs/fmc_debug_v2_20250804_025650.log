2025-08-04 02:56:50,583 | INFO | ================================================================================
2025-08-04 02:56:50,583 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 02:56:50,583 | INFO | Session ID: migration_1754301410
2025-08-04 02:56:50,583 | INFO | Connection Type: fmcapi
2025-08-04 02:56:50,583 | INFO | 🔍 Connection Diagnostic:
2025-08-04 02:56:50,583 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 02:56:50,583 | INFO |    • fmcapi Available: True
2025-08-04 02:56:50,583 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 02:56:55,589 | WARNING |    • fmcapi object creation failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x105ea41a0>, 'Connection to ************* timed out. (connect timeout=5)'))
2025-08-04 02:56:55,589 | INFO | 🔍 Connection diagnostic complete
2025-08-04 02:56:55,589 | INFO | ================================================================================
2025-08-04 02:56:55,589 | INFO | [LOAD] Loading migration configuration: fmc_migration_config.json
2025-08-04 02:56:55,596 | INFO | [DOC] Detected v1.0 config format (api_calls structure)
2025-08-04 02:56:55,596 | INFO | [INFO] Found 629 host objects in v1.0 format
2025-08-04 02:56:55,596 | INFO | [INFO] Found 63 network objects in v1.0 format
2025-08-04 02:56:55,596 | INFO | [INFO] Found 29 service objects in v1.0 format
2025-08-04 02:56:55,596 | INFO | [START] Starting Host Objects migration...
2025-08-04 02:56:55,596 | INFO | [INFO] Processing 629 hosts objects...
2025-08-04 02:56:55,596 | DEBUG | Processing hosts 1: RadSaratoga
2025-08-04 02:56:55,596 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 02:56:55,596 | DEBUG | Object data: {'name': 'RadSaratoga', 'type': 'Host', 'value': '***********', 'description': 'Migrated from ASA - ', 'overridable': False}
2025-08-04 02:57:00,599 | DEBUG | GET result for RadSaratoga: success=False, message='fmcapi get() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x105caf110>, 'Connection to ************* timed out. (connect timeout=5)'))'
2025-08-04 02:57:05,601 | DEBUG | POST result for RadSaratoga: success=False, message='fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x105cafd90>, 'Connection to ************* timed out. (connect timeout=5)'))'
2025-08-04 02:57:05,602 | ERROR | Creation failed for RadSaratoga: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x105cafd90>, 'Connection to ************* timed out. (connect timeout=5)'))
2025-08-04 02:57:05,602 | DEBUG | Processing hosts 2: RadAmsMem
2025-08-04 02:57:05,602 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 02:57:05,602 | DEBUG | Object data: {'name': 'RadAmsMem', 'type': 'Host', 'value': '***********', 'description': 'Migrated from ASA - ', 'overridable': False}
2025-08-04 02:57:10,605 | DEBUG | GET result for RadAmsMem: success=False, message='fmcapi get() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x10c21c410>, 'Connection to ************* timed out. (connect timeout=5)'))'
2025-08-04 02:57:15,609 | DEBUG | POST result for RadAmsMem: success=False, message='fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x10c21c550>, 'Connection to ************* timed out. (connect timeout=5)'))'
2025-08-04 02:57:15,609 | ERROR | Creation failed for RadAmsMem: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x10c21c550>, 'Connection to ************* timed out. (connect timeout=5)'))
2025-08-04 02:57:15,610 | DEBUG | Processing hosts 3: RadStMarys
2025-08-04 02:57:15,610 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 02:57:15,610 | DEBUG | Object data: {'name': 'RadStMarys', 'type': 'Host', 'value': '***********', 'description': 'Migrated from ASA - ', 'overridable': False}
2025-08-04 02:57:20,613 | DEBUG | GET result for RadStMarys: success=False, message='fmcapi get() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x10c21ccd0>, 'Connection to ************* timed out. (connect timeout=5)'))'
2025-08-04 02:57:25,615 | DEBUG | POST result for RadStMarys: success=False, message='fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x10c21c910>, 'Connection to ************* timed out. (connect timeout=5)'))'
2025-08-04 02:57:25,616 | ERROR | Creation failed for RadStMarys: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x10c21c910>, 'Connection to ************* timed out. (connect timeout=5)'))
2025-08-04 02:57:30,619 | DEBUG | GET result for RadSeton: success=False, message='fmcapi get() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x10c21d310>, 'Connection to ************* timed out. (connect timeout=5)'))'
