2025-08-04 03:10:07,367 | INFO | ================================================================================
2025-08-04 03:10:07,367 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 03:10:07,367 | INFO | Session ID: migration_1754302207
2025-08-04 03:10:07,367 | INFO | Connection Type: fmcapi
2025-08-04 03:10:07,367 | INFO | 🔍 Connection Diagnostic:
2025-08-04 03:10:07,367 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 03:10:07,367 | INFO |    • fmcapi Available: True
2025-08-04 03:10:07,367 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 03:10:12,391 | WARNING |    • fmcapi object creation failed: HTTPSConnectionPool(host='test.example.com', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x1029d3620>, 'Connection to test.example.com timed out. (connect timeout=5)'))
2025-08-04 03:10:12,391 | INFO | 🔍 Connection diagnostic complete
2025-08-04 03:10:12,391 | INFO | ================================================================================
