2025-08-04 06:26:00,224 | INFO | ================================================================================
2025-08-04 06:26:00,226 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 06:26:00,228 | INFO | Session ID: migration_1754303160
2025-08-04 06:26:00,230 | INFO | Connection Type: fmcapi
2025-08-04 06:26:00,232 | INFO | 🔍 Connection Diagnostic:
2025-08-04 06:26:00,233 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 06:26:00,235 | INFO |    • fmcapi Available: True
2025-08-04 06:26:00,237 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 06:26:02,187 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 06:26:02,192 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 06:26:02,195 | INFO | 🔍 Connection diagnostic complete
2025-08-04 06:26:02,197 | INFO | ================================================================================
2025-08-04 06:39:41,583 | ERROR | Creation failed for Hixny.com: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:41,649 | ERROR | Creation failed for statrad.hl7.test: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:41,766 | ERROR | Creation failed for P_PAT_FIN: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:41,871 | ERROR | Creation failed for NLHENDO01: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:41,932 | ERROR | Creation failed for NLHENDO01.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:42,023 | ERROR | Creation failed for ENDOWORKS02: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:42,108 | ERROR | Creation failed for ENDOWORKS03: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:42,175 | ERROR | Creation failed for P_IS_PACS: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:42,282 | ERROR | Creation failed for retsolinc2.com: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:42,393 | ERROR | Creation failed for retsolinc3.com: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:42,489 | ERROR | Creation failed for SophosMailExt: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:42,628 | ERROR | Creation failed for IRIS: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:42,670 | ERROR | Creation failed for smtp.biz.rr.com: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:42,733 | ERROR | Creation failed for Hypertype: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:42,804 | ERROR | Creation failed for MVP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:42,884 | ERROR | Creation failed for LeaderHFTPsite: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:42,960 | ERROR | Creation failed for LeaderHFTPsite2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:43,068 | ERROR | Creation failed for stentor.com: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:43,149 | ERROR | Creation failed for TOGARM: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:43,191 | ERROR | Creation failed for Infotrak: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:43,300 | ERROR | Creation failed for sftp.lifethc.org: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:43,341 | ERROR | Creation failed for TeleVideo1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:43,404 | ERROR | Creation failed for Televid2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:43,464 | ERROR | Creation failed for CONNECTPLUS01: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:43,501 | ERROR | Creation failed for VeriquestPC: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:43,584 | ERROR | Creation failed for VeriquestSite: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:43,693 | ERROR | Creation failed for PATIENT_PORTAL_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:43,738 | ERROR | Creation failed for HYPER-_REPLICA_BROKER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:43,800 | ERROR | Creation failed for Sodexho: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:43,846 | ERROR | Creation failed for Provation-out: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:43,891 | ERROR | Creation failed for VeriquestServer: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:43,906 | ERROR | Creation failed for Harland: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:43,951 | ERROR | Creation failed for IMO_2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:44,043 | ERROR | Creation failed for WWW.UPTODATE.COM: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:44,144 | ERROR | Creation failed for XENAPP22: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:44,191 | ERROR | Creation failed for HYPER-V_CLUSTER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:44,305 | ERROR | Creation failed for PATIENTPORTAL.EXTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:44,414 | ERROR | Creation failed for remote.nlh.org: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:44,488 | ERROR | Creation failed for mail.nlh.org: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:44,627 | ERROR | Creation failed for DIRECT.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:44,776 | ERROR | Creation failed for TeleMed_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:44,900 | ERROR | Creation failed for MDI.dmz: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:44,942 | ERROR | Creation failed for NLHCISCO: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:45,012 | ERROR | Creation failed for LabCorp3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:45,102 | ERROR | Creation failed for LabCorpDev: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:45,149 | ERROR | Creation failed for LabCorpProd: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:45,223 | ERROR | Creation failed for TheOutsourceGroup: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:45,258 | ERROR | Creation failed for TeleradIT_Millenium1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:45,418 | ERROR | Creation failed for TeleradIT_Millenium2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:45,527 | ERROR | Creation failed for FastChart.Inside: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:45,630 | ERROR | Creation failed for Ellis.inside: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:45,680 | ERROR | Creation failed for STATRAD.DR.SVR: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:45,742 | ERROR | Creation failed for NLHTEST01: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:45,817 | ERROR | Creation failed for NLH.ORG.EXTERNAL.FORMS: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:45,918 | ERROR | Creation failed for obj-************: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:46,041 | ERROR | Creation failed for obj-***********: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:46,092 | ERROR | Creation failed for obj-***********: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:46,167 | ERROR | Creation failed for obj-************: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:46,276 | ERROR | Creation failed for obj-***********: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:46,406 | ERROR | Creation failed for obj-************: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:46,524 | ERROR | Creation failed for obj-************: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:46,578 | ERROR | Creation failed for Ellis.Peer.New: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:46,625 | ERROR | Creation failed for HEALTHTOUCH.PEER.INTERNAL.1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:46,669 | ERROR | Creation failed for Medent.Peer.New.: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:46,748 | ERROR | Creation failed for HIXNY.MBMS.MILLENIUMBILLING.PEER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:46,856 | ERROR | Creation failed for HIXNY.MBMS.MILLENIUMBILLING.INTERNAL1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:46,941 | ERROR | Creation failed for MCKESSON.MC.PHARM: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:47,072 | ERROR | Creation failed for newsync3.mkesson.com: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:47,168 | ERROR | Creation failed for obj-0.0.0.0: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:47,202 | ERROR | Creation failed for SMHA.pacs1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:47,247 | ERROR | Creation failed for SMHA.pacs2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:47,311 | ERROR | Creation failed for SMHA.pacs3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:47,350 | ERROR | Creation failed for PACS.VCE1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:47,388 | ERROR | Creation failed for PACS.VCE2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:47,437 | ERROR | Creation failed for PACS.VCE3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:47,516 | ERROR | Creation failed for PACS.VCE4: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:47,660 | ERROR | Creation failed for MEDENT_NAS_INTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:47,721 | ERROR | Creation failed for HEALTHTOUCH01: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:47,798 | ERROR | Creation failed for HEALTHTOUCH02: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:47,857 | ERROR | Creation failed for PDX.Internal: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:47,907 | ERROR | Creation failed for PDX.External: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:47,951 | ERROR | Creation failed for HIXNY.PEER.NEW: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:47,982 | ERROR | Creation failed for HIXNY.INTERNAL1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:48,039 | ERROR | Creation failed for XCHANGEWORX.PEER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:48,091 | ERROR | Creation failed for NETSCALER.NLHRESTAPI: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:48,179 | ERROR | Creation failed for NUVODIA_VPN_NLH_PEER1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:48,216 | ERROR | Creation failed for NUVODIA_VPN_NLH_PEER2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:48,261 | ERROR | Creation failed for FIREPOWER_VM_ESXI: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:48,310 | ERROR | Creation failed for SMHA.READ.10: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:48,439 | ERROR | Creation failed for ESRS_EMC_VIRTUAL_APPLIANCE: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:48,485 | ERROR | Creation failed for NLH-ISWEB.INTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:48,528 | ERROR | Creation failed for NLH-ISWEB.DMZ: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:48,590 | ERROR | Creation failed for RESTFULAPI.DMZ: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:48,621 | ERROR | Creation failed for NYOH.INTERNAL.1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:48,683 | ERROR | Creation failed for NYOH.INTERNAL.2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:48,715 | ERROR | Creation failed for NYOH.EXTERNAL.PEER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:48,750 | ERROR | Creation failed for NUVODIA.INTERNAL.1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:48,791 | ERROR | Creation failed for NUVODIA.INTERNAL.2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:48,828 | ERROR | Creation failed for P_MIS52_DMZ: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:48,867 | ERROR | Creation failed for MDITEST_SENDTRYDS: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:48,898 | ERROR | Creation failed for XENAPP25: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:48,952 | ERROR | Creation failed for skype.nlh.org_external: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:48,994 | ERROR | Creation failed for st_netadmin: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,027 | ERROR | Creation failed for SMHA.RAD.EXTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,072 | ERROR | Creation failed for MVO_AMST_PEER_NEW: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,102 | ERROR | Creation failed for GUEST_INTERFACE_EXTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,134 | ERROR | Creation failed for VENDOR_EXTERNAL_INTERFACE: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,153 | ERROR | Creation failed for p_mis_netadmin.dmz: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,196 | ERROR | Creation failed for NLH-ISWEB.DMZVR: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,227 | ERROR | Creation failed for AMC.PACS.NEW: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,259 | ERROR | Creation failed for BRIAN_DHCP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,289 | ERROR | Creation failed for BPC.External: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,321 | ERROR | Creation failed for P_MIS_CISCOMON: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,353 | ERROR | Creation failed for XENAPP17: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,383 | ERROR | Creation failed for XENAPP18: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,415 | ERROR | Creation failed for XENAPP19: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,448 | ERROR | Creation failed for P_MIS52.WAYNE: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,519 | ERROR | Creation failed for NETADMIN.DMZ.TEST: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,558 | ERROR | Creation failed for EUGENE10: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,611 | ERROR | Creation failed for MEDITECHAPIVIP1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,665 | ERROR | Creation failed for MEDITECHAPIVIP2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,778 | ERROR | Creation failed for StratSolution.Peer: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,831 | ERROR | Creation failed for P_PHA_PDX1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:49,871 | ERROR | Creation failed for NLHPRTG01: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:50,001 | ERROR | Creation failed for RCARE-SERVER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:50,056 | ERROR | Creation failed for NLHDMZ01_SWITCH: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:50,166 | ERROR | Creation failed for XENAPP01: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:50,226 | ERROR | Creation failed for PRTG.NLH.ORG.EXTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:50,291 | ERROR | Creation failed for BACKLINE.VPN.PEER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:50,360 | ERROR | Creation failed for UNITEDLABNETWORK.VPN.PEER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:50,468 | ERROR | Creation failed for NETWORK_OBJ_18.204.173.205: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:50,576 | ERROR | Creation failed for BACKLINE.LDAP.INTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:50,644 | ERROR | Creation failed for MEDENT.NIMBLE.INSIDE.1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:50,700 | ERROR | Creation failed for MEDENT.NIMBLE.OPENVPN.OUTSIDE.1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:50,769 | ERROR | Creation failed for MEDENT.NIMBLE.OPENVPN.OUTSIDE.2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:50,823 | ERROR | Creation failed for ROBOT_GE_VOT_TRAIN: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:50,870 | ERROR | Creation failed for BILL_BAIRD: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:50,985 | ERROR | Creation failed for LUCIUS29-iDRAC: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:51,058 | ERROR | Creation failed for DOLBEY: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:51,133 | ERROR | Creation failed for DOLBEYTEST: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:51,171 | ERROR | Creation failed for NLH_DCDS_9300s: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:51,231 | ERROR | Creation failed for Schumacher.Inside1.new.ADTPROD: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:51,287 | ERROR | Creation failed for Schumacher.Inside2.new.ADTTEST: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:51,322 | ERROR | Creation failed for Schumacher.VPN.Peer.New: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:51,419 | ERROR | Creation failed for MEDENT-EXPORT: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:51,496 | ERROR | Creation failed for QUEST.VPN.PEER.2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:51,594 | ERROR | Creation failed for QUEST.VPN.INTERNAL.2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:51,638 | ERROR | Creation failed for Wayne: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:51,694 | ERROR | Creation failed for HIXNY.PEER.INTERNAL.TEST: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:51,727 | ERROR | Creation failed for HIXNY.PEER.INTERNAL.PROD: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:51,759 | ERROR | Creation failed for NLHSP19OFCWEB.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:51,818 | ERROR | Creation failed for PATIENTPORTAL.DMZ: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:51,867 | ERROR | Creation failed for mtrestexpapis-live01.nlh.org.external: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:51,943 | ERROR | Creation failed for mtrestexpapis-test01.nlh.org.external: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:51,974 | ERROR | Creation failed for mtrestexpapis-test01.nlh.org.DMZ: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:52,023 | ERROR | Creation failed for mtrestexpapis-live01.nlh.org.DMZ: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:52,107 | ERROR | Creation failed for CHANGE.HEALTHCARE.EXTERNAL.PEER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:52,170 | ERROR | Creation failed for CHANGE.HEALTHCARE.EXTERNAL.IP1.PROD: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:52,194 | ERROR | Creation failed for CHANGE.HEALTHCARE.EXTERNAL.IP2.TEST: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:52,230 | ERROR | Creation failed for NLI.T.BG01: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:52,255 | ERROR | Creation failed for CHC.EXTERNAL.1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:52,278 | ERROR | Creation failed for CHC.EXTERNAL.2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:52,363 | ERROR | Creation failed for NLI-T-BG01.CHC.NAT: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:52,412 | ERROR | Creation failed for MDILIVE.CHC.NAT: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:52,441 | ERROR | Creation failed for MDITEST.CHC.NAT: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:52,474 | ERROR | Creation failed for NLI-T-BG01: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:52,535 | ERROR | Creation failed for NLHFTP01.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:52,587 | ERROR | Creation failed for SR_STACK_01: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:52,697 | ERROR | Creation failed for NLI-BG01.nlh.org: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:52,776 | ERROR | Creation failed for NLI-BG04.CHC.NAT: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:52,836 | ERROR | Creation failed for NLI-BG04: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:52,898 | ERROR | Creation failed for CHC.EXTERNAL.3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:52,945 | ERROR | Creation failed for NYOH.INTERNAL.3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:52,990 | ERROR | Creation failed for WEBSSO.MEDITECH.COM: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:53,046 | ERROR | Creation failed for WEBSSO2FA.MEDITECH.COM: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:53,084 | ERROR | Creation failed for HIXNY.INTERNAL.PUSH_SERVER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:53,132 | ERROR | Creation failed for HIXNY.INTERNAL.TESTHUB: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:53,176 | ERROR | Creation failed for FIRECALL_JSC: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:53,208 | ERROR | Creation failed for FIRECALLSYSTEM_ENDPOINTS1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:53,255 | ERROR | Creation failed for FIRECALLSYSTEM_ENDPOINTS2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:53,334 | ERROR | Creation failed for BACKLINE.VPN.PEER2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:53,367 | ERROR | Creation failed for BACKLINE.LDAP.INTERNAL2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:53,398 | ERROR | Creation failed for NETWORK_OBJ_35.155.201.32: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:53,460 | ERROR | Creation failed for BANDWIDTH_TEST: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:53,573 | ERROR | Creation failed for P_IS_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:53,683 | ERROR | Creation failed for P_IS_3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:53,725 | ERROR | Creation failed for P_IT_COOR: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:53,791 | ERROR | Creation failed for P_IT_TECH1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:53,863 | ERROR | Creation failed for HIRAM: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:53,919 | ERROR | Creation failed for RYAN: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:53,988 | ERROR | Creation failed for NICK: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:54,027 | ERROR | Creation failed for IT_TEST: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:54,092 | ERROR | Creation failed for P-BOARDROOM1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:54,154 | ERROR | Creation failed for LUCIUS08: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:54,198 | ERROR | Creation failed for LUCIUS21-iLO: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:54,308 | ERROR | Creation failed for NLHADMINCENTER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:54,355 | ERROR | Creation failed for XENAPP24: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:54,459 | ERROR | Creation failed for SQL01.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:54,527 | ERROR | Creation failed for FAXSERVER.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:54,573 | ERROR | Creation failed for NLHFUSION: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:54,640 | ERROR | Creation failed for BACKUPEXEC01: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:54,781 | ERROR | Creation failed for ARCHIVE.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:54,854 | ERROR | Creation failed for PRINT: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:54,926 | ERROR | Creation failed for NLHBACKUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:54,979 | ERROR | Creation failed for INTERLACETEST: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:55,045 | ERROR | Creation failed for NLHMONITOR01: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:55,122 | ERROR | Creation failed for SANPHNHM: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:55,181 | ERROR | Creation failed for CENTRALINK_BCR: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:55,301 | ERROR | Creation failed for CENTRALINK_VISTA2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:55,404 | ERROR | Creation failed for CENTRALINK_VISTA1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:55,472 | ERROR | Creation failed for CENTRALINK_LCM: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:55,509 | ERROR | Creation failed for CENTRALINK: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:55,571 | ERROR | Creation failed for LUCIUS31-iDRAC: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:55,604 | ERROR | Creation failed for XENAPP21: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:55,649 | ERROR | Creation failed for NLHCITRIXGATEWAY: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:55,680 | ERROR | Creation failed for ST_NETADMIN2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:55,758 | ERROR | Creation failed for DR_CECIL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:55,803 | ERROR | Creation failed for P_IS_RAMANI: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:55,835 | ERROR | Creation failed for US_LOGU_E9_2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:55,882 | ERROR | Creation failed for NYOH.INTERNAL.4: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:55,929 | ERROR | Creation failed for NLI-BG13: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:55,997 | ERROR | Creation failed for NLHTESTMOBILE: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,053 | ERROR | Creation failed for NOVA.NLH.ORG.EXTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,101 | ERROR | Creation failed for BANDWIDTH_TEST_2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,162 | ERROR | Creation failed for NETWORK_OBJ_192.168.253.161: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,224 | ERROR | Creation failed for NETWORK_OBJ_172.16.41.10: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,292 | ERROR | Creation failed for Barracuda.Web.NLH.Internal: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,335 | ERROR | Creation failed for Barracuda.Email.NLH.Internal: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,381 | ERROR | Creation failed for HEALTHTOUCH.EXTERNAL.PEER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,428 | ERROR | Creation failed for HEALTHTOUCH.PEER.INTERNAL.2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,459 | ERROR | Creation failed for NETWORK_OBJ_216.41.86.228: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,491 | ERROR | Creation failed for DMZ_TEST.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,537 | ERROR | Creation failed for DUOTEST.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,569 | ERROR | Creation failed for DUOTEST.NLH.ORG.DMZ: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,600 | ERROR | Creation failed for BARRACUDA.EMAIL.INSIDE: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,632 | ERROR | Creation failed for NLH.CORE.INTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,696 | ERROR | Creation failed for DCDS.CORE.INTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,742 | ERROR | Creation failed for GPC_STACK: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,803 | ERROR | Creation failed for NLHSSI: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,849 | ERROR | Creation failed for NLHBRAUNPUMPS.INTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,886 | ERROR | Creation failed for NLHBRAUNPUMPS.EXTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,933 | ERROR | Creation failed for P-ITMGR: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:56,975 | ERROR | Creation failed for MEDIVATOR66838147: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:57,027 | ERROR | Creation failed for MEDIVATOR66838143: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:57,134 | ERROR | Creation failed for AMC.VPN.PEER.NEW: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:57,260 | ERROR | Creation failed for NUVODIA.INTERNAL.NEW.1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:57,287 | ERROR | Creation failed for NUVODIA.INTERNAL.NEW.2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:57,346 | ERROR | Creation failed for ULN.VPN.INTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:57,381 | ERROR | Creation failed for CISCOPRIME.INTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:57,496 | ERROR | Creation failed for CISCOPRIMEINF: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:57,571 | ERROR | Creation failed for MIS_TEST2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:57,636 | ERROR | Creation failed for CISCONMON: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:57,710 | ERROR | Creation failed for SYSLOGSERVER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:57,757 | ERROR | Creation failed for NOVA-QIE.INTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:57,821 | ERROR | Creation failed for NOVA.INTERLACE.PEER.EXTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:57,879 | ERROR | Creation failed for WLC1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:57,927 | ERROR | Creation failed for sendgrid.net.virus: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:57,995 | ERROR | Creation failed for NOVA.INTERLACE.PEER.EXTERNAL2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:58,052 | ERROR | Creation failed for love.explorethebest.com.spam.2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:58,154 | ERROR | Creation failed for love.explorethebest.com.spam.1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:58,308 | ERROR | Creation failed for love.explorethebest.com.spam.3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:58,341 | ERROR | Creation failed for CISCO.WSA.INTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:58,415 | ERROR | Creation failed for HARRIET.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:58,463 | ERROR | Creation failed for LUCIUS32.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:58,514 | ERROR | Creation failed for LUCIUS10A.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:58,622 | ERROR | Creation failed for LUCIUS19B.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:58,739 | ERROR | Creation failed for WILLYWONKA.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:58,789 | ERROR | Creation failed for NLHSYN01.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:58,852 | ERROR | Creation failed for NLHSYN02.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:58,901 | ERROR | Creation failed for NLHSYN03.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:58,948 | ERROR | Creation failed for NLHSYN04.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:59,031 | ERROR | Creation failed for NLHSP19APP.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:59,090 | ERROR | Creation failed for LUCIUS18C.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:59,138 | ERROR | Creation failed for LUCIUS19C.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:59,181 | ERROR | Creation failed for LUCIUS14.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:59,241 | ERROR | Creation failed for LUCIUS26D.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:59,292 | ERROR | Creation failed for DDPC.FIREALARM: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:59,335 | ERROR | Creation failed for LUCUIS16B.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:59,380 | ERROR | Creation failed for LUCIUS17B.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:59,416 | ERROR | Creation failed for LUCIUS19A.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:59,459 | ERROR | Creation failed for SUMMIT.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:59,533 | ERROR | Creation failed for LUCIUS25A.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:59,597 | ERROR | Creation failed for ONEVIEW.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:59,691 | ERROR | Creation failed for DR1.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:59,765 | ERROR | Creation failed for LUCIUS26B.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:59,817 | ERROR | Creation failed for NLHBACKUP02.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:39:59,914 | ERROR | Creation failed for KRONOSNEW.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,005 | ERROR | Creation failed for SMHA.RAD.EXTERNAL.NEW: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,056 | ERROR | Creation failed for BARRACUDA.LDAP.EXTERNAL.PEER.1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,117 | ERROR | Creation failed for BARRACUDA.LDAP.EXTERNAL.PEER.2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,194 | ERROR | Creation failed for BARRACUDA.LDAP.EXTERNAL.PEER.3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,250 | ERROR | Creation failed for REYHEALTH.EXTERNAL.EXTERNAL.1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,287 | ERROR | Creation failed for REYHEALTH.EXTERNAL.EXTERNAL.2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,335 | ERROR | Creation failed for CHC.OPTUM.EXTERNAL.VPN.PEER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,371 | ERROR | Creation failed for LUCIUS18D.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,413 | ERROR | Creation failed for STREAMTASK.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,443 | ERROR | Creation failed for GPSUPPORT.VPN.EXTERNAL.PEER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,475 | ERROR | Creation failed for DI.AWSERVER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,520 | ERROR | Creation failed for DI.AWSERVER.ILO: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,552 | ERROR | Creation failed for DI.CTSCANNER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,583 | ERROR | Creation failed for DI.CT.ADV.WS: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,615 | ERROR | Creation failed for DI.GE.MAMMO.INTERFACE: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,647 | ERROR | Creation failed for DI.MAMMO.SHUTTLE: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,678 | ERROR | Creation failed for DI.MRI.ALLIANCE: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,708 | ERROR | Creation failed for DI.MUSE01: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,740 | ERROR | Creation failed for DI.MUSE02: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,770 | ERROR | Creation failed for DI.MUSE03: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,801 | ERROR | Creation failed for DI.MAMMO: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,833 | ERROR | Creation failed for DI.NUCMEDCAMERA: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,865 | ERROR | Creation failed for DI.PETCTVIEWER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,896 | ERROR | Creation failed for DI.PERTH.XRAY: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,927 | ERROR | Creation failed for DI.R.AND.F: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:00,974 | ERROR | Creation failed for DI.ROOMA: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:01,037 | ERROR | Creation failed for DI.XELERIS.NM: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:01,082 | ERROR | Creation failed for NYOH.INTERNAL.5: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:01,128 | ERROR | Creation failed for CLEARWATER1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:01,175 | ERROR | Creation failed for CLEARWATER2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:01,208 | ERROR | Creation failed for JELMENDORFSPAM: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:01,254 | ERROR | Creation failed for LUCIUS25C.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:01,286 | ERROR | Creation failed for PROVMDAPP.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:01,333 | ERROR | Creation failed for NLHPROVMDORACLE.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:01,364 | ERROR | Creation failed for NLHMUSE01.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:01,403 | ERROR | Creation failed for NLHMUSE02.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:01,448 | ERROR | Creation failed for LUCIUS16A.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:01,514 | ERROR | Creation failed for DESIGO.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:01,552 | ERROR | Creation failed for PATIENT.CONNECT.ARTERA.EXTERNAL.PEER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:01,635 | ERROR | Creation failed for PATIENT.CONNECT.ARTERA.INTERNAL.PEER.1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:01,746 | ERROR | Creation failed for PATIENT.CONNECT.ARTERA.INTERNAL.PEER.2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:01,900 | ERROR | Creation failed for LUCIOUS01: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:02,007 | ERROR | Creation failed for NLHPRTGPROBE04: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:02,093 | ERROR | Creation failed for LUCIUS10C: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:02,132 | ERROR | Creation failed for LUCIUS28: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:02,211 | ERROR | Creation failed for PATIENT.CONNECT.ARTERA.INTERNAL.PEER.3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:02,317 | ERROR | Creation failed for PATIENT.CONNECT.ARTERA.INTERNAL.PEER.4: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:02,384 | ERROR | Creation failed for LUCIUS07.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:02,446 | ERROR | Creation failed for NURSECALLAPP.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:02,488 | ERROR | Creation failed for NLHBRAUNPUMPS.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:02,520 | ERROR | Creation failed for BRAUNWEB: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:02,597 | ERROR | Creation failed for LUCIUS09A.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:02,638 | ERROR | Creation failed for LUCIUS09B.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:02,704 | ERROR | Creation failed for LUCIUS09C.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:02,800 | ERROR | Creation failed for NLHCISCO.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:02,847 | ERROR | Creation failed for LUCIUS13.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:02,949 | ERROR | Creation failed for SQLTEST.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:02,994 | ERROR | Creation failed for NLHMONITOR.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:03,053 | ERROR | Creation failed for NLHPRTG01.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:03,101 | ERROR | Creation failed for NLHKIWISYSLOG01.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:03,166 | ERROR | Creation failed for LUCIUS17A.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:03,250 | ERROR | Creation failed for XENAPP01.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:03,297 | ERROR | Creation failed for CITRIXSF.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:03,342 | ERROR | Creation failed for NLHWEB01..NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:03,411 | ERROR | Creation failed for AVAYACALLACCT.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:03,467 | ERROR | Creation failed for NLHSSI.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:03,528 | ERROR | Creation failed for TEMPTRAK.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:03,587 | ERROR | Creation failed for PRINT.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:03,652 | ERROR | Creation failed for QUICKCHARGE.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:03,797 | ERROR | Creation failed for NLH3M.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:03,918 | ERROR | Creation failed for LUCIUS19D.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:03,978 | ERROR | Creation failed for NLHAV01.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:04,071 | ERROR | Creation failed for LUCIUS23A.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:04,134 | ERROR | Creation failed for LUCIUS23B.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:04,182 | ERROR | Creation failed for LUCIUS23C.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:04,288 | ERROR | Creation failed for LUCIUS23D.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:04,367 | ERROR | Creation failed for NLHDHCP01.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:04,437 | ERROR | Creation failed for LUCIUS25B.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:04,491 | ERROR | Creation failed for LUCIUS21.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:04,560 | ERROR | Creation failed for CENTRALINK.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:04,617 | ERROR | Creation failed for LUCIUS27.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:04,665 | ERROR | Creation failed for HEALTHTOUCH02.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:04,755 | ERROR | Creation failed for MUSE03.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:04,804 | ERROR | Creation failed for KRONOSTEST.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:04,852 | ERROR | Creation failed for MUSE-TEST.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:04,907 | ERROR | Creation failed for INTERLACETEST.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:04,928 | ERROR | Creation failed for NLHINT-TEST.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:05,029 | ERROR | Creation failed for LUCIUS29.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:05,096 | ERROR | Creation failed for NLHFUSION.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:05,153 | ERROR | Creation failed for MUSE-CCGHL7.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:05,229 | ERROR | Creation failed for LUCIUS31.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:05,312 | ERROR | Creation failed for LUCIUS10B.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:05,378 | ERROR | Creation failed for LUCIUS10C.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:05,496 | ERROR | Creation failed for NLHCA.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:05,558 | ERROR | Creation failed for NLHPRTGPROBE3.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:05,604 | ERROR | Creation failed for LUCIUS10D.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:05,639 | ERROR | Creation failed for CODONICS.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:05,708 | ERROR | Creation failed for MDITEST.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:05,760 | ERROR | Creation failed for CITRIXFS02.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:05,845 | ERROR | Creation failed for XENAPP02.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:05,891 | ERROR | Creation failed for MEDENTPRINT01.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:05,947 | ERROR | Creation failed for HEALTHTOUCH01.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:06,081 | ERROR | Creation failed for LUCIUS18A.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:06,163 | ERROR | Creation failed for INTERLACE.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:06,226 | ERROR | Creation failed for NOVA-QIE.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:06,322 | ERROR | Creation failed for LUCIUS18B.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:06,421 | ERROR | Creation failed for NLHUTILITY.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:06,526 | ERROR | Creation failed for NLHCODONICS.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:06,555 | ERROR | Creation failed for NLHLICENSE.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:06,588 | ERROR | Creation failed for HPDMAN.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:06,653 | ERROR | Creation failed for SCVMM.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:06,715 | ERROR | Creation failed for LUCIUS24A.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:06,777 | ERROR | Creation failed for LUCIUS24B.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:06,880 | ERROR | Creation failed for LUCIUS24C.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:06,965 | ERROR | Creation failed for LUCIUS24D.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:07,050 | ERROR | Creation failed for LUCIUS26A.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:07,123 | ERROR | Creation failed for ESICALLACCT26A.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:07,257 | ERROR | Creation failed for ESRS.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:07,340 | ERROR | Creation failed for NLHDRFIRST.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:07,404 | ERROR | Creation failed for NLHELOCK.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:07,465 | ERROR | Creation failed for LUCIUS26C.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:07,528 | ERROR | Creation failed for COBAS.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:07,593 | ERROR | Creation failed for PRADEV.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:07,674 | ERROR | Creation failed for NLHADMINCENTER.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:07,731 | ERROR | Creation failed for LUCIUS28.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:07,761 | ERROR | Creation failed for NURSECALLHD.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:07,808 | ERROR | Creation failed for NLH-iUV.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:07,915 | ERROR | Creation failed for LUCIUS30.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:08,011 | ERROR | Creation failed for MUSE-APP.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:08,096 | ERROR | Creation failed for MUSE-NXWEB.NLH.ORG: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:08,179 | ERROR | Creation failed for Clearwater.External.Peer: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:08,253 | ERROR | Creation failed for ASA01: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:08,406 | ERROR | Creation failed for ASA02: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:08,478 | ERROR | Creation failed for NETWORK_OBJ_172.16.201.35: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:08,543 | ERROR | Creation failed for NETWORK_OBJ_192.168.178.2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:08,655 | ERROR | Creation failed for QUICKCHARGE.EXTERNAL.WHITELIST.PEER.1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:08,740 | ERROR | Creation failed for QUICKCHARGE.EXTERNAL.WHITELIST.PEER.2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:08,788 | ERROR | Creation failed for MMI.BILLING.EXTERNAL.PEER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:08,896 | ERROR | Creation failed for MMI.BILLING.INTERNAL.PEER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:08,999 | ERROR | Creation failed for NYOH.INTERNAL.MEDICOM: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:09,109 | ERROR | Creation failed for NYOH.INTERNAL.AMBRA: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:09,142 | ERROR | Creation failed for NYOH.INTERNAL.POWERSHARE: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:09,282 | ERROR | Creation failed for NYOH.INTERNAL.CLOUD: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:09,403 | ERROR | Creation failed for Nuvodia.OneOncology.Cloud.External.Peer: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:09,545 | ERROR | Creation failed for Nuvodia.OneOncology.Cloud.Internal.Peer: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:09,624 | ERROR | Creation failed for NETWORK_OBJ_162.245.33.10: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:09,663 | ERROR | Creation failed for NUVODIA.INTERNAL.NEW.3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:09,795 | ERROR | Creation failed for FRESHWORKS.EXCLUSIONS.1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:09,851 | ERROR | Creation failed for FRESHWORKS.EXCLUSIONS.2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:09,914 | ERROR | Creation failed for FRESHWORKS.EXCLUSIONS.3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:09,960 | ERROR | Creation failed for FRESHWORKS.EXCLUSIONS.5: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:10,007 | ERROR | Creation failed for FRESHWORKS.EXCLUSIONS.6: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:10,039 | ERROR | Creation failed for FRESHWORKS.EXCLUSIONS.7: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:10,077 | INFO | [FILE] Checkpoint saved: migration_checkpoints\migration_1754303160_phase1_hosts.json
2025-08-04 06:40:10,117 | ERROR | Creation failed for TeleMedVT3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:10,166 | ERROR | Creation failed for TelemedVT4: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:10,245 | ERROR | Creation failed for TelemedVT5: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:10,319 | ERROR | Creation failed for TeleMedVT1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:10,371 | ERROR | Creation failed for Medent.VPN.net: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:10,472 | ERROR | Creation failed for SMHApacsSUBNET: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:10,526 | ERROR | Creation failed for pacs.net: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:10,588 | ERROR | Creation failed for PACS_VCE: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:10,695 | ERROR | Creation failed for pacs.net_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:10,729 | ERROR | Creation failed for Olympus.Inside.New: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:10,792 | ERROR | Creation failed for speculator: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:10,851 | ERROR | Creation failed for GEserviceNET: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:10,935 | ERROR | Creation failed for Mill.PACS.NET: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:10,997 | ERROR | Creation failed for DI.NET: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:11,065 | ERROR | Creation failed for STUDENT_VLAN: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:11,164 | ERROR | Creation failed for questlab: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:11,311 | ERROR | Creation failed for iPEOPLEremote: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:11,469 | ERROR | Creation failed for LAN: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:11,610 | ERROR | Creation failed for RALSplusLAN: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:11,675 | ERROR | Creation failed for PhilipsSupport: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:11,738 | ERROR | Creation failed for STRAT_SOL.NET.INTERNAL1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:11,849 | ERROR | Creation failed for MVOrtho.net: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:11,888 | ERROR | Creation failed for LAN_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:11,932 | ERROR | Creation failed for MilleniumPACSnat: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:12,032 | ERROR | Creation failed for MVOatJSC.net: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:12,073 | ERROR | Creation failed for SENTRYDS.NET: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:12,135 | ERROR | Creation failed for SENTRYDS: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:12,183 | ERROR | Creation failed for pacs.net-01: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:12,315 | ERROR | Creation failed for LAN-01: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:12,395 | ERROR | Creation failed for MilleniumPACSnat-*************: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:12,482 | ERROR | Creation failed for obj-*************-*************: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:12,620 | ERROR | Creation failed for obj_any: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:12,678 | ERROR | Creation failed for obj_any-03: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:12,753 | ERROR | Creation failed for NUVODIA_NETWORK_3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:12,853 | ERROR | Creation failed for GUEST_WLAN_NAT: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:12,891 | ERROR | Creation failed for GUEST_NETWORK: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:12,994 | ERROR | Creation failed for VENDOR_WLAN_NAT: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:13,026 | ERROR | Creation failed for CREDITCARD_CAFE_EXTERNAL1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:13,072 | ERROR | Creation failed for CREDITCARD_CAFE2_EXTERNAL2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:13,135 | ERROR | Creation failed for CREDITCARD_CAFE_EXTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:13,169 | ERROR | Creation failed for STRAT_SOL.NET.INTERNAL2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:13,213 | ERROR | Creation failed for EXPANSE_VLAN1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:13,253 | ERROR | Creation failed for EXPANSE_VLAN2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:13,292 | ERROR | Creation failed for EXPANSE_VLAN3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:13,394 | ERROR | Creation failed for EXPANSE_VLAN4: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:13,466 | ERROR | Creation failed for QUEST.VPN.EXTERNAL.2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:13,514 | ERROR | Creation failed for CHC.OPTUM.NAT.INTERNAL.SUB: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:13,622 | ERROR | Creation failed for ACRONIS.EXTERNAL.RANGE1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:13,780 | ERROR | Creation failed for ACRONIS.EXTERNAL.RANGE2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:13,854 | ERROR | Creation failed for BARRACUDA.CLOUD.EXTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:13,948 | ERROR | Creation failed for ACRONIS.EXTERNAL.RANGE3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:14,046 | ERROR | Creation failed for ACRONIS.EXTERNAL.RANGE4: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:14,115 | ERROR | Creation failed for GESUPPORT.INTERNAL.NET: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:14,198 | ERROR | Creation failed for CLEARWATER3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:14,272 | ERROR | Creation failed for CLEARWATER4: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:14,352 | ERROR | Creation failed for backblazeb2.com: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:14,454 | ERROR | Creation failed for HANYS.EXTERNAL.1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:14,609 | ERROR | Creation failed for HANYS.INTERNAL.2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:14,737 | ERROR | Creation failed for HANYS.EXTERNAL.3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:14,839 | ERROR | Creation failed for Clearwater.Internal.Peer.Range: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:14,985 | ERROR | Creation failed for NLH.Firewall.Range.Internal: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:15,112 | ERROR | Creation failed for SSI.EXTERNAL.PEER.1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:15,153 | ERROR | Creation failed for MICROSOFTSTREAM.COM: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:15,174 | INFO | [FILE] Checkpoint saved: migration_checkpoints\migration_1754303160_phase1_networks.json
2025-08-04 06:40:15,248 | ERROR | Creation failed for obj-tcp-eq-80: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:15,299 | ERROR | Creation failed for obj-tcp-eq-15002: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:15,425 | ERROR | Creation failed for obj-tcp-eq-15331: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:15,579 | ERROR | Creation failed for obj-tcp-eq-3389: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:15,733 | ERROR | Creation failed for obj-tcp-eq-2222: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:15,792 | ERROR | Creation failed for obj-tcp-eq-6544: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:15,953 | ERROR | Creation failed for obj-tcp-eq-2020: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:16,073 | ERROR | Creation failed for obj-tcp-eq-23: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:16,150 | ERROR | Creation failed for obj-tcp-eq-15031: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:16,311 | ERROR | Creation failed for obj-tcp-eq-5631: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:16,431 | ERROR | Creation failed for obj-udp-eq-15032: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:16,478 | ERROR | Creation failed for obj-udp-eq-5632: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:16,549 | ERROR | Creation failed for obj-tcp-eq-25: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:16,634 | ERROR | Creation failed for obj-tcp-eq-443: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:16,735 | ERROR | Creation failed for obj-tcp-eq-55443: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:16,892 | ERROR | Creation failed for obj-tcp-eq-3401: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:16,994 | ERROR | Creation failed for obj-tcp-eq-53048: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:17,102 | ERROR | Creation failed for obj-tcp-eq-53372: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:17,149 | ERROR | Creation failed for obj-tcp-eq-53050: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:17,238 | ERROR | Creation failed for obj-tcp-eq-53374: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:17,325 | ERROR | Creation failed for obj-tcp-eq-21: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:17,384 | ERROR | Creation failed for NLI-BG13-FTP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:17,449 | ERROR | Creation failed for W32.MYDOOM.OLD: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:17,549 | ERROR | Creation failed for GREYCASTLE_VPN: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:17,586 | ERROR | Creation failed for IMO_CLOUD: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:17,648 | ERROR | Creation failed for NOVA-8070-TCP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:17,696 | ERROR | Creation failed for REYHEALTH.EXTERNAL.PORT1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:17,741 | ERROR | Creation failed for REYHEALTH.EXTERNAL.PORT2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:17,773 | ERROR | Creation failed for NOVA.TOPAZ: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:17,793 | INFO | [FILE] Checkpoint saved: migration_checkpoints\migration_1754303160_phase1_services.json
2025-08-04 06:40:17,902 | ERROR | Creation failed for Medivators: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:18,061 | ERROR | Creation failed for NUVODIA.INTERNAL.GROUP.NEW: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:18,170 | ERROR | Creation failed for NUVODIA.INTERNAL.PEER.NET1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:18,326 | ERROR | Creation failed for DM_INLINE_NETWORK_4: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:18,444 | ERROR | Creation failed for DM_INLINE_NETWORK_6: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:18,560 | ERROR | Creation failed for FoodService: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:18,711 | ERROR | Creation failed for DI.Net.Group: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:18,849 | ERROR | Creation failed for STRATEGICSOLUTIONS.EXTERNAL.GROUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:18,922 | ERROR | Creation failed for Cardinal: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:18,959 | ERROR | Creation failed for medinotes: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:18,992 | ERROR | Creation failed for CitrixServers.dmz: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:19,053 | ERROR | Creation failed for MilleniumPACS: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:19,103 | ERROR | Creation failed for ExchangeServers: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:19,152 | ERROR | Creation failed for TeleMedVT: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:19,204 | ERROR | Creation failed for PacsServers: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:19,261 | ERROR | Creation failed for MDI.OUT.Allow: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:19,413 | ERROR | Creation failed for eRXdataCenters: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:19,524 | ERROR | Creation failed for Medent.Interface: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:19,652 | ERROR | Creation failed for SMHA.RAD: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:19,693 | ERROR | Creation failed for RAD.PACS.READ: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:19,762 | ERROR | Creation failed for SOPHOS: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:19,824 | ERROR | Creation failed for CitrixServers1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:19,865 | ERROR | Creation failed for CitrixServers1_ref: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:19,899 | ERROR | Creation failed for MVO_Allow_OUTBOUND_Group: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:19,944 | ERROR | Creation failed for ProvationServers: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:19,990 | ERROR | Creation failed for AAI.NYOH.PACS: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:20,038 | ERROR | Creation failed for Dolby_OUT: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:20,092 | ERROR | Creation failed for Dolby_Servers: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:20,131 | ERROR | Creation failed for Healthtouch.out: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:20,185 | ERROR | Creation failed for FoodSVC: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:20,242 | ERROR | Creation failed for ALBANYPACS.GROUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:20,302 | ERROR | Creation failed for HIXNY: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:20,354 | ERROR | Creation failed for Olympus.inside.group: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:20,480 | ERROR | Creation failed for MDI_Group: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:20,582 | ERROR | Creation failed for Brian_DHCP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:20,681 | ERROR | Creation failed for Schumacher.Inside: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:20,833 | ERROR | Creation failed for MEDENTHQ: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:20,949 | ERROR | Creation failed for MEDENT_GROUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:21,042 | ERROR | Creation failed for WINDOWS_XP_DENY: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:21,084 | ERROR | Creation failed for APPLE.OUT: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:21,142 | ERROR | Creation failed for ProviderOrg.External: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:21,258 | ERROR | Creation failed for CITRIX_EXTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:21,352 | ERROR | Creation failed for CITRIXGATEWAY.DMZ: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:21,395 | ERROR | Creation failed for CITRIX_INTERNAL_TO_DMZ: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:21,444 | ERROR | Creation failed for MIS_TEST: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:21,580 | ERROR | Creation failed for MARKETO_SPAMMER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:21,715 | ERROR | Creation failed for ENDOWORKS: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:21,857 | ERROR | Creation failed for CE2000.INTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:21,991 | ERROR | Creation failed for CE2000.EXTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:22,056 | ERROR | Creation failed for Group_24.97.36.3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:22,100 | ERROR | Creation failed for Group_65.114.41.136: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:22,153 | ERROR | Creation failed for Group_12.39.198.49: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:22,306 | ERROR | Creation failed for Group_173.84.224.94: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:22,464 | ERROR | Creation failed for Group_12.152.123.2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:22,607 | ERROR | Creation failed for HIXNY.MBMS.INTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:22,701 | ERROR | Creation failed for NUVODIA_VPN_NLH_PEER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:22,794 | ERROR | Creation failed for CLEARWATERTEST: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:22,855 | ERROR | Creation failed for HIXNY.INTERNAL.GROUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:22,900 | ERROR | Creation failed for MDI.GROUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:23,027 | ERROR | Creation failed for NYOH.INTERNAL.NET: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:23,180 | ERROR | Creation failed for NUVODIA.VPN.SENDPOINT.MASTER: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:23,338 | ERROR | Creation failed for DM_INLINE_NETWORK_7: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:23,462 | ERROR | Creation failed for DM_INLINE_NETWORK_8: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:23,505 | ERROR | Creation failed for DM_INLINE_NETWORK_9: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:23,566 | ERROR | Creation failed for SMHA.RAD.NEW: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:23,683 | ERROR | Creation failed for DM_INLINE_NETWORK_10: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:23,784 | ERROR | Creation failed for MEDENT.NIMBLE.OPENVPN: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:23,907 | ERROR | Creation failed for MEDENT.NIMBLE.OPENVPN.OUTSIDE.GROUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:23,992 | ERROR | Creation failed for MEDENT.NIMBLE.OPENVPN.INSIDE.GROUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:24,105 | ERROR | Creation failed for TCPUDP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:24,207 | ERROR | Creation failed for EXPANSE_VLANS: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:24,250 | ERROR | Creation failed for SmartNet_Devices: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:24,304 | ERROR | Creation failed for Domain.Controllers.Group: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:24,427 | ERROR | Creation failed for Quest.NLH2Quest.Internal: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:24,571 | ERROR | Creation failed for CHANGE.HEALTHCARE.EXTERNAL.GROUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:24,690 | ERROR | Creation failed for CHANGE.HEALTHCARE.NLH.INTERNAL.GROUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:24,808 | ERROR | Creation failed for CHC.EXTERNAL.NETWORK: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:24,939 | ERROR | Creation failed for PHINMS.GROUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:25,093 | ERROR | Creation failed for NLI.INTERNAL.NAT.CHC: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:25,248 | ERROR | Creation failed for NLI-BG-GROUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:25,330 | ERROR | Creation failed for DM_INLINE_NETWORK_11: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:25,483 | ERROR | Creation failed for QUEST.VPN.INTERNAL.GROUP.2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:25,603 | ERROR | Creation failed for WEBSSO.MEDITECH.COM.EXTERNAL.GROUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:25,703 | ERROR | Creation failed for BACKLINE.LDAP.NLH.INTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:25,761 | ERROR | Creation failed for FIRECALLSYSTEM: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:25,837 | ERROR | Creation failed for FIRECALLSYSTEM__ENDPOINTS: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:25,879 | ERROR | Creation failed for IT_DEPT: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:25,927 | ERROR | Creation failed for FULL_PORT_ACCESS: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:26,029 | ERROR | Creation failed for CISCO_INTERNAL_2_EXTERNAL_ACL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:26,154 | ERROR | Creation failed for HEALTHTOUCH.NLH.INTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:26,310 | ERROR | Creation failed for HEALTHTOUCH.PEER.INTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:26,402 | ERROR | Creation failed for Greycastle_Testing_External: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:26,558 | ERROR | Creation failed for LINKBG.SPAM.GROUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:26,618 | ERROR | Creation failed for love.explorethebest.com.spam.group: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:26,668 | ERROR | Creation failed for NLH.ACRONIS.GROUP.INSIDE: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:26,744 | ERROR | Creation failed for NLH.ACRONIS.GROUP.EXTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:26,792 | ERROR | Creation failed for BARRACUDA.LDAP.EXTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:26,892 | ERROR | Creation failed for REYHEALTH.EXTERNAL.GROUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:26,944 | ERROR | Creation failed for EMAIL.BLACKLIST.EXTERNAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:27,009 | ERROR | Creation failed for NLH.DI.GEDEVICES: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:27,099 | ERROR | Creation failed for DM_INLINE_NETWORK_3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:27,156 | ERROR | Creation failed for blackblazeb2.goup: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:27,232 | ERROR | Creation failed for HAYNS.EXTERNAL.GROUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:27,266 | ERROR | Creation failed for PATIENT.CONNECT.ARTERA.INTERNAL.PEER.GROUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:27,321 | ERROR | Creation failed for Incident.External: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:27,384 | ERROR | Creation failed for NLH.Firewall.Internal.Group: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:27,490 | ERROR | Creation failed for Clearwater.Internal.Group: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:27,520 | ERROR | Creation failed for QUICKCHARGE.EXTERNAL.WHITELIST.PEER.GROUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:27,606 | ERROR | Creation failed for SSI.EXTERNAL.PEER.GROUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:27,671 | ERROR | Creation failed for BANDWIDTH.TEST.GROUP.OUTSIDE: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:27,809 | ERROR | Creation failed for FRESHWORKS.EXCLUSIONS.GROUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:27,841 | INFO | [FILE] Checkpoint saved: migration_checkpoints\migration_1754303160_phase1_object_groups.json
2025-08-04 06:40:27,949 | ERROR | Creation failed for PaceGlobalgrp: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:28,105 | ERROR | Creation failed for timeservice: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:28,258 | ERROR | Creation failed for timeserviceUDP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:28,353 | ERROR | Creation failed for QUEST: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:28,430 | ERROR | Creation failed for citrixXML: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:28,572 | ERROR | Creation failed for GatewayDMZ: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:28,655 | ERROR | Creation failed for RSA: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:28,716 | ERROR | Creation failed for HFMBoces: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:28,756 | ERROR | Creation failed for GEinbound: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:28,850 | ERROR | Creation failed for GEoutbound: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:28,981 | ERROR | Creation failed for PetLinks: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:29,100 | ERROR | Creation failed for TeleVideoTcpUdp: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:29,178 | ERROR | Creation failed for GEPACS: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:29,224 | ERROR | Creation failed for ExchangePorts: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:29,277 | ERROR | Creation failed for PrintPorts: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:29,326 | ERROR | Creation failed for PrinterPorts: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:29,396 | ERROR | Creation failed for IPSEC_ISAKMP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:29,463 | ERROR | Creation failed for EmdeonPorts: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:29,566 | ERROR | Creation failed for in_any_to_out_any_tcp: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:29,688 | ERROR | Creation failed for RAMSOFTports: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:29,764 | ERROR | Creation failed for CoreFTP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:29,829 | ERROR | Creation failed for PhilipsPacs: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:29,957 | ERROR | Creation failed for Pacs: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:30,011 | ERROR | Creation failed for NexTalk1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:30,061 | ERROR | Creation failed for NexTalkTcpUdp: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:30,173 | ERROR | Creation failed for CastleSys: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:30,237 | ERROR | Creation failed for FTPpsv5500: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:30,306 | ERROR | Creation failed for Labcorp: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:30,401 | ERROR | Creation failed for Labcorptcp: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:30,462 | ERROR | Creation failed for IVANStcp: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:30,530 | ERROR | Creation failed for IVANSudp: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:30,589 | ERROR | Creation failed for Sophos: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:30,706 | ERROR | Creation failed for any_in_udp_to_any_out: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:30,762 | ERROR | Creation failed for SophosMail: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:30,867 | ERROR | Creation failed for BobSFTP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:30,996 | ERROR | Creation failed for Impulse.UDP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:31,089 | ERROR | Creation failed for ImpulseTCP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:31,188 | ERROR | Creation failed for TEMP_TRACK1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:31,313 | ERROR | Creation failed for PatPortal: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:31,440 | ERROR | Creation failed for ALLSCRIPT_PORTAL: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:31,543 | ERROR | Creation failed for testgroup: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:31,654 | ERROR | Creation failed for ALBANYMEDPACS: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:31,731 | ERROR | Creation failed for Guest_Wireless: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:31,794 | ERROR | Creation failed for SOPHOSFTP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:31,862 | ERROR | Creation failed for BOCES_IPADS: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:31,914 | ERROR | Creation failed for TEST: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:31,978 | ERROR | Creation failed for IMO_Ports: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:32,039 | ERROR | Creation failed for TeamViewer: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:32,091 | ERROR | Creation failed for CCD_MESSAGING: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:32,154 | ERROR | Creation failed for Apple_Services: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:32,216 | ERROR | Creation failed for ProviderOrg: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:32,286 | ERROR | Creation failed for MAIL_VIRUS: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:32,395 | ERROR | Creation failed for STAT_RAD: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:32,444 | ERROR | Creation failed for StatRadService: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:32,505 | ERROR | Creation failed for PAT_ACCTS_FTP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:32,644 | ERROR | Creation failed for UDP_TEST: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:32,700 | ERROR | Creation failed for CE000SVC: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:32,779 | ERROR | Creation failed for CE2000: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:32,832 | ERROR | Creation failed for mckesson: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:32,890 | ERROR | Creation failed for DM_INLINE_SERVICE_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:32,950 | ERROR | Creation failed for MEDENT_TELEMED: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:33,017 | ERROR | Creation failed for PHINMS: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:33,076 | ERROR | Creation failed for SALUCRO_FTP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:33,173 | ERROR | Creation failed for QUEST_SFTP_NEW: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:33,214 | ERROR | Creation failed for REYHEALTH.EXTERNAL.PORT.GROUP: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:33,280 | ERROR | Creation failed for DM_INLINE_SERVICE_2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:33,303 | INFO | [FILE] Checkpoint saved: migration_checkpoints\migration_1754303160_phase1_service_groups.json
2025-08-04 06:40:33,355 | ERROR | Creation failed for inside_access_in_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:33,450 | ERROR | Creation failed for inside_access_in_rule_2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:33,540 | ERROR | Creation failed for inside_access_in_rule_3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:33,703 | ERROR | Creation failed for inside_access_in_rule_4: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:33,810 | ERROR | Creation failed for inside_access_in_rule_5: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:33,948 | ERROR | Creation failed for inside_access_in_rule_6: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:34,077 | ERROR | Creation failed for inside_access_in_rule_7: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:34,170 | ERROR | Creation failed for inside_access_in_rule_8: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:34,303 | ERROR | Creation failed for inside_access_in_rule_9: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:34,405 | ERROR | Creation failed for inside_access_in_rule_10: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:34,512 | ERROR | Creation failed for inside_access_in_rule_11: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:34,626 | ERROR | Creation failed for inside_access_in_rule_12: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:34,698 | ERROR | Creation failed for inside_access_in_rule_13: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:34,793 | ERROR | Creation failed for inside_access_in_rule_14: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:34,856 | ERROR | Creation failed for inside_access_in_rule_15: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:35,007 | ERROR | Creation failed for inside_access_in_rule_16: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:35,083 | ERROR | Creation failed for inside_access_in_rule_17: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:35,195 | ERROR | Creation failed for inside_access_in_rule_18: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:35,305 | ERROR | Creation failed for inside_access_in_rule_19: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:35,445 | ERROR | Creation failed for inside_access_in_rule_20: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:35,544 | ERROR | Creation failed for inside_access_in_rule_21: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:35,664 | ERROR | Creation failed for inside_access_in_rule_22: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:35,749 | ERROR | Creation failed for inside_access_in_rule_23: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:35,808 | ERROR | Creation failed for inside_access_in_rule_24: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:35,902 | ERROR | Creation failed for inside_access_in_rule_25: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:35,968 | ERROR | Creation failed for inside_access_in_rule_26: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:35,994 | ERROR | Creation failed for inside_access_in_rule_27: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:36,122 | ERROR | Creation failed for inside_access_in_rule_28: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:36,233 | ERROR | Creation failed for inside_access_in_rule_29: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:36,345 | ERROR | Creation failed for inside_access_in_rule_30: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:36,406 | ERROR | Creation failed for inside_access_in_rule_31: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:36,519 | ERROR | Creation failed for inside_access_in_rule_32: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:36,614 | ERROR | Creation failed for inside_access_in_rule_33: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:36,646 | ERROR | Creation failed for inside_access_in_rule_34: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:36,691 | ERROR | Creation failed for inside_access_in_rule_35: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:36,741 | ERROR | Creation failed for inside_access_in_rule_36: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:36,829 | ERROR | Creation failed for inside_access_in_rule_37: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:36,917 | ERROR | Creation failed for inside_access_in_rule_38: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:37,017 | ERROR | Creation failed for inside_access_in_rule_39: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:37,098 | ERROR | Creation failed for inside_access_in_rule_40: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:37,175 | ERROR | Creation failed for inside_access_in_rule_41: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:37,233 | ERROR | Creation failed for inside_access_in_rule_42: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:37,331 | ERROR | Creation failed for inside_access_in_rule_43: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:37,471 | ERROR | Creation failed for inside_access_in_rule_44: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:37,629 | ERROR | Creation failed for inside_access_in_rule_45: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:37,765 | ERROR | Creation failed for inside_access_in_rule_46: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:37,922 | ERROR | Creation failed for inside_access_in_rule_47: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:38,023 | ERROR | Creation failed for inside_access_in_rule_48: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:38,123 | ERROR | Creation failed for inside_access_in_rule_49: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:38,268 | ERROR | Creation failed for inside_access_in_rule_50: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:38,419 | ERROR | Creation failed for inside_access_in_rule_51: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:38,481 | ERROR | Creation failed for inside_access_in_rule_52: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:38,590 | ERROR | Creation failed for inside_access_in_rule_53: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:38,637 | ERROR | Creation failed for inside_access_in_rule_54: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:38,725 | ERROR | Creation failed for inside_access_in_rule_55: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:38,829 | ERROR | Creation failed for inside_access_in_rule_56: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:38,885 | ERROR | Creation failed for inside_access_in_rule_57: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:38,923 | ERROR | Creation failed for inside_access_in_rule_58: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:38,986 | ERROR | Creation failed for inside_access_in_rule_59: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:39,124 | ERROR | Creation failed for inside_access_in_rule_60: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:39,239 | ERROR | Creation failed for inside_access_in_rule_61: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:39,309 | ERROR | Creation failed for inside_access_in_rule_62: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:39,452 | ERROR | Creation failed for inside_access_in_rule_63: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:39,586 | ERROR | Creation failed for inside_access_in_rule_64: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:39,708 | ERROR | Creation failed for inside_access_in_rule_65: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:39,809 | ERROR | Creation failed for inside_access_in_rule_66: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:39,932 | ERROR | Creation failed for inside_access_in_rule_67: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:39,990 | ERROR | Creation failed for inside_access_in_rule_68: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:40,059 | ERROR | Creation failed for inside_access_in_rule_69: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:40,120 | ERROR | Creation failed for inside_access_in_rule_70: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:40,163 | ERROR | Creation failed for inside_access_in_rule_71: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:40,209 | ERROR | Creation failed for inside_access_in_rule_72: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:40,343 | ERROR | Creation failed for inside_access_in_rule_73: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:40,399 | ERROR | Creation failed for inside_access_in_rule_74: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:40,463 | ERROR | Creation failed for inside_access_in_rule_75: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:40,557 | ERROR | Creation failed for inside_access_in_rule_76: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:40,654 | ERROR | Creation failed for inside_access_in_rule_77: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:40,695 | ERROR | Creation failed for inside_access_in_rule_78: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:40,744 | ERROR | Creation failed for inside_access_in_rule_79: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:40,830 | ERROR | Creation failed for inside_access_in_rule_80: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:40,869 | ERROR | Creation failed for inside_access_in_rule_81: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:40,997 | ERROR | Creation failed for inside_access_in_rule_82: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:41,048 | ERROR | Creation failed for inside_access_in_rule_83: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:41,155 | ERROR | Creation failed for inside_access_in_rule_84: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:41,268 | ERROR | Creation failed for inside_access_in_rule_85: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:41,388 | ERROR | Creation failed for inside_access_in_rule_86: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:41,476 | ERROR | Creation failed for inside_access_in_rule_87: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:41,556 | ERROR | Creation failed for inside_access_in_rule_88: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:41,668 | ERROR | Creation failed for inside_access_in_rule_89: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:41,780 | ERROR | Creation failed for inside_access_in_rule_90: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:41,893 | ERROR | Creation failed for outside_access_in_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:42,039 | ERROR | Creation failed for outside_access_in_rule_2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:42,150 | ERROR | Creation failed for outside_access_in_rule_3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:42,262 | ERROR | Creation failed for outside_access_in_rule_4: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:42,321 | ERROR | Creation failed for outside_access_in_rule_5: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:42,455 | ERROR | Creation failed for outside_access_in_rule_6: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:42,565 | ERROR | Creation failed for outside_access_in_rule_7: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:42,625 | ERROR | Creation failed for outside_access_in_rule_8: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:42,688 | ERROR | Creation failed for outside_access_in_rule_9: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:42,739 | ERROR | Creation failed for outside_access_in_rule_10: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:42,872 | ERROR | Creation failed for outside_access_in_rule_11: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:42,970 | ERROR | Creation failed for outside_access_in_rule_12: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:43,031 | ERROR | Creation failed for outside_access_in_rule_13: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:43,094 | ERROR | Creation failed for outside_access_in_rule_14: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:43,176 | ERROR | Creation failed for outside_access_in_rule_15: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:43,209 | ERROR | Creation failed for outside_access_in_rule_16: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:43,283 | ERROR | Creation failed for outside_access_in_rule_17: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:43,341 | ERROR | Creation failed for outside_access_in_rule_18: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:43,418 | ERROR | Creation failed for outside_access_in_rule_19: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:43,450 | ERROR | Creation failed for outside_access_in_rule_20: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:43,498 | ERROR | Creation failed for outside_access_in_rule_21: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:43,528 | ERROR | Creation failed for outside_access_in_rule_22: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:43,572 | ERROR | Creation failed for outside_access_in_rule_23: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:43,627 | ERROR | Creation failed for outside_access_in_rule_24: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:43,707 | ERROR | Creation failed for outside_access_in_rule_25: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:43,828 | ERROR | Creation failed for outside_access_in_rule_26: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:43,877 | ERROR | Creation failed for outside_access_in_rule_27: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:43,947 | ERROR | Creation failed for outside_access_in_rule_28: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:44,048 | ERROR | Creation failed for outside_access_in_rule_29: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:44,154 | ERROR | Creation failed for outside_access_in_rule_30: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:44,268 | ERROR | Creation failed for outside_access_in_rule_31: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:44,347 | ERROR | Creation failed for outside_access_in_rule_32: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:44,407 | ERROR | Creation failed for outside_access_in_rule_33: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:44,484 | ERROR | Creation failed for outside_access_in_rule_34: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:44,593 | ERROR | Creation failed for outside_access_in_rule_35: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:44,645 | ERROR | Creation failed for outside_access_in_rule_36: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:44,686 | ERROR | Creation failed for outside_access_in_rule_37: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:44,743 | ERROR | Creation failed for outside_access_in_rule_38: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:44,795 | ERROR | Creation failed for outside_access_in_rule_39: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:44,852 | ERROR | Creation failed for outside_access_in_rule_40: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:44,916 | ERROR | Creation failed for outside_access_in_rule_41: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:44,996 | ERROR | Creation failed for outside_access_in_rule_42: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:45,098 | ERROR | Creation failed for outside_access_in_rule_43: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:45,154 | ERROR | Creation failed for outside_access_in_rule_44: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:45,211 | ERROR | Creation failed for outside_access_in_rule_45: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:45,325 | ERROR | Creation failed for outside_access_in_rule_46: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:45,417 | ERROR | Creation failed for outside_access_in_rule_47: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:45,505 | ERROR | Creation failed for outside_access_in_rule_48: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:45,574 | ERROR | Creation failed for outside_access_in_rule_49: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:45,665 | ERROR | Creation failed for outside_access_in_rule_50: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:45,735 | ERROR | Creation failed for outside_access_in_rule_51: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:45,799 | ERROR | Creation failed for DMZ_access_in_V1_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:45,853 | ERROR | Creation failed for DMZ_access_in_V1_rule_2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:45,939 | ERROR | Creation failed for DMZ_access_in_V1_rule_3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:46,022 | ERROR | Creation failed for DMZ_access_in_V1_rule_4: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:46,059 | ERROR | Creation failed for DMZ_access_in_V1_rule_5: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:46,133 | ERROR | Creation failed for DMZ_access_in_V1_rule_6: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:46,167 | ERROR | Creation failed for inside_nat0_outbound_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:46,216 | ERROR | Creation failed for inside_nat0_outbound_rule_2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:46,285 | ERROR | Creation failed for inside_nat0_outbound_rule_3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:46,346 | ERROR | Creation failed for inside_nat0_outbound_rule_4: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:46,452 | ERROR | Creation failed for inside_nat0_outbound_rule_5: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:46,542 | ERROR | Creation failed for inside_nat0_outbound_rule_6: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:46,578 | ERROR | Creation failed for inside_nat0_outbound_rule_7: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:46,636 | ERROR | Creation failed for inside_nat0_outbound_rule_8: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:46,670 | ERROR | Creation failed for inside_nat0_outbound_rule_9: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:46,715 | ERROR | Creation failed for inside_nat0_outbound_rule_10: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:46,762 | ERROR | Creation failed for inside_nat0_outbound_rule_11: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:46,832 | ERROR | Creation failed for inside_nat0_outbound_rule_12: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:46,892 | ERROR | Creation failed for inside_nat0_outbound_rule_13: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:46,958 | ERROR | Creation failed for inside_nat0_outbound_rule_14: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:47,072 | ERROR | Creation failed for inside_nat0_outbound_rule_15: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:47,178 | ERROR | Creation failed for inside_nat0_outbound_rule_16: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:47,230 | ERROR | Creation failed for inside_nat0_outbound_rule_17: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:47,318 | ERROR | Creation failed for inside_nat0_outbound_rule_18: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:47,368 | ERROR | Creation failed for inside_nat0_outbound_rule_19: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:47,420 | ERROR | Creation failed for VPN2.nlh.org_splitTunnelAcl_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:47,468 | ERROR | Creation failed for outside_cryptomap_6_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:47,528 | ERROR | Creation failed for VPN.nlh.org_splitTunnelAcl_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:47,588 | ERROR | Creation failed for VPN.nlh.org_splitTunnelAcl_rule_2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:47,638 | ERROR | Creation failed for VPN.nlh.org_splitTunnelAcl_rule_3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:47,699 | ERROR | Creation failed for outside_cryptomap_3_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:47,744 | ERROR | Creation failed for outside_cryptomap_9_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:47,840 | ERROR | Creation failed for outside_cryptomap_10_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:47,885 | ERROR | Creation failed for outside_cryptomap_11_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:47,953 | ERROR | Creation failed for outside_cryptomap_1_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:47,995 | ERROR | Creation failed for outside_cryptomap_12_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:48,050 | ERROR | Creation failed for outside_cryptomap_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:48,111 | ERROR | Creation failed for outside_cryptomap_14_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:48,191 | ERROR | Creation failed for outside_cryptomap_15_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:48,262 | ERROR | Creation failed for outside_cryptomap_2_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:48,325 | ERROR | Creation failed for outside_cryptomap_7_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:48,388 | ERROR | Creation failed for outside_cryptomap_880_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:48,449 | ERROR | Creation failed for outside_pnat_inbound_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:48,480 | ERROR | Creation failed for inside_pnat_outbound_V2_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:48,514 | ERROR | Creation failed for outside_cryptomap_1000_1_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:48,573 | ERROR | Creation failed for outside_cryptomap_13_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:48,636 | ERROR | Creation failed for outside_cryptomap_16_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:48,682 | ERROR | Creation failed for outside_cryptomap_1120_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:48,716 | ERROR | Creation failed for inside_pnat_outbound_V3_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:48,775 | ERROR | Creation failed for inside_pnat_outbound_V3_rule_2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:48,814 | ERROR | Creation failed for inside_pnat_outbound_V3_rule_3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:48,859 | ERROR | Creation failed for inside_pnat_outbound_V4_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:48,919 | ERROR | Creation failed for inside_pnat_outbound_V4_rule_2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:48,980 | ERROR | Creation failed for inside_pnat_outbound_V4_rule_3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:49,028 | ERROR | Creation failed for outside_cryptomap_17_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:49,074 | ERROR | Creation failed for inside_pnat_outbound_V14_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:49,122 | ERROR | Creation failed for inside_pnat_outbound_V15_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:49,166 | ERROR | Creation failed for outside_cryptomap_5_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:49,231 | ERROR | Creation failed for outside_cryptomap_1300_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:49,275 | ERROR | Creation failed for sfr_redirect_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:49,323 | ERROR | Creation failed for Guest_access_in_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:49,370 | ERROR | Creation failed for Guest_access_in_rule_2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:49,458 | ERROR | Creation failed for Guest_access_in_rule_3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:49,585 | ERROR | Creation failed for Vendor_access_in_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:49,636 | ERROR | Creation failed for Vendor_access_in_rule_2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:49,728 | ERROR | Creation failed for Vendor_access_in_rule_3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:49,796 | ERROR | Creation failed for Vendor_access_in_rule_4: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:49,838 | ERROR | Creation failed for Vendor_access_in_rule_5: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:49,896 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:49,951 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_2: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:49,979 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_3: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:50,048 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_4: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:50,095 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_5: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:50,140 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_6: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:50,198 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_7: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:50,260 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_8: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:50,359 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_9: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:50,402 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_10: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:50,437 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_11: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:50,501 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_12: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:50,541 | ERROR | Creation failed for AnyConnect_Client_Local_Print_rule_13: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:50,564 | ERROR | Creation failed for outside_cryptomap_4_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:50,610 | ERROR | Creation failed for outside_cryptomap_8_rule_1: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ProxyError('Unable to connect to proxy', OSError('Tunnel connection failed: 407 Proxy Authentication Required')))
2025-08-04 06:40:50,645 | INFO | [FILE] Checkpoint saved: migration_checkpoints\migration_1754303160_phase1_access_rules.json
2025-08-04 06:40:50,646 | INFO | ================================================================================
2025-08-04 06:40:50,648 | INFO | MIGRATION SUMMARY
2025-08-04 06:40:50,649 | INFO | ================================================================================
2025-08-04 06:40:50,651 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 06:40:50,652 | INFO |    • Total Objects: 1122
2025-08-04 06:40:50,654 | INFO |    • Created: 0
2025-08-04 06:40:50,655 | INFO |    • Updated: 189
2025-08-04 06:40:50,657 | INFO |    • Failed: 933
2025-08-04 06:40:50,658 | INFO |    • Skipped: 0
2025-08-04 06:40:50,660 | INFO |    • Success Rate: 16.8%
2025-08-04 06:40:50,661 | INFO | ================================================================================
2025-08-04 06:40:50,683 | INFO | [FILE] Summary saved: migration_summary_migration_1754303160.json
2025-08-04 06:40:50,687 | WARNING | [WARN]  Migration completed with 933 failures
