# #!/usr/bin/env python3
# """
# FMC API Executor
# This script takes the output from the ASA to FMC translator and executes 
# the API calls against a Firepower Management Center instance.
# """

# import json
# import requests
# import time
# import sys
# import random
# from typing import Dict, List, Optional, Any
# import ipaddress
# import logging
# import os
# from datetime import datetime
# from urllib3.exceptions import InsecureRequestWarning
# from requests.auth import HTTPBasicAuth

# # Disable SSL warnings for demo purposes
# requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)

# class FMCAPIExecutor:
#     def __init__(self, fmc_host: str, username: str, password: str, 
#                  api_delay: float = 0.1, verify_ssl: bool = False, overwrite: bool = False,
#                  skip_existing_check: bool = False):
#         self.fmc_host = fmc_host
#         self.username = username
#         self.password = password
#         self.api_delay = api_delay
#         self.verify_ssl = verify_ssl
#         self.overwrite = overwrite
#         self.skip_existing_check = skip_existing_check
        
#         # Authentication tokens
#         self.access_token = None
#         self.refresh_token = None
#         self.domain_uuid = None
        
#         # Error tracking
#         self.last_api_error = None
        
#         # Session tracking
#         self.session_start_time = time.time()
#         self.migration_checkpoint = {
#             'session_id': f"fmc_migration_{int(self.session_start_time)}",
#             'start_time': self.session_start_time,
#             'created_objects': [],
#             'modified_objects': [],
#             'deleted_objects': [],
#             'failed_operations': [],
#             'completed_phases': {},  # Track completed phases for resumption
#             'current_phase': None,
#             'phase_results': {}      # Store results for each completed phase
#         }
        
#         # Object caching system to reduce excessive API calls
#         self._object_cache = {
#             'existing_objects': None,  # Cache for get_existing_objects()
#             'object_lookups': {},      # Cache for individual object lookups by name
#             'cache_timestamp': None,   # When cache was last updated
#             'cache_ttl': 300,         # Cache TTL in seconds (5 minutes)
#             'lookup_cache': {}        # Cache for live lookups by (type, name)
#         }
        
#         # Track phantom objects to prevent infinite retry loops
#         self._phantom_objects_detected = set()
        
#         # Setup comprehensive logging
#         self.setup_logging()
        
#         # Flag to track if cache has been initialized
#         self._cache_initialized = False
    
#     def _is_cache_valid(self) -> bool:
#         """Check if the object cache is still valid"""
#         if self._object_cache['cache_timestamp'] is None:
#             return False
        
#         age = time.time() - self._object_cache['cache_timestamp']
#         return age < self._object_cache['cache_ttl']
    
#     def _invalidate_cache(self):
#         """Invalidate the object cache (call when objects are created/modified)"""
#         self._object_cache['existing_objects'] = None
#         self._object_cache['object_lookups'] = {}
#         self._object_cache['lookup_cache'] = {}
#         self._object_cache['cache_timestamp'] = None
#         self.log("Object cache invalidated", "DEBUG")
    
#     def _cache_object_lookup(self, obj_type: str, name: str, result: Optional[Dict]):
#         """Cache the result of an object lookup"""
#         cache_key = f"{obj_type.lower()}:{name.lower()}"
#         self._object_cache['lookup_cache'][cache_key] = result
#         self.log(f"Cached lookup result for {cache_key}", "DEBUG")
    
#     def _get_cached_lookup(self, obj_type: str, name: str) -> Optional[Dict]:
#         """Get cached lookup result if available"""
#         if not self._is_cache_valid():
#             return None
            
#         cache_key = f"{obj_type.lower()}:{name.lower()}"
#         result = self._object_cache['lookup_cache'].get(cache_key)
        
#         if result is not None:
#             self.log(f"Cache hit for {cache_key}", "DEBUG")
        
#         return result
    
#     def setup_logging(self):
#         """Setup comprehensive logging with file output and structured formatting"""
#         # Create logs directory if it doesn't exist
#         log_dir = "logs"
#         if not os.path.exists(log_dir):
#             os.makedirs(log_dir)
        
#         # Generate timestamp for this session
#         timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
#         # Setup main logger
#         self.logger = logging.getLogger('FMCMigration')
#         self.logger.setLevel(logging.DEBUG)
        
#         # Clear any existing handlers
#         self.logger.handlers = []
        
#         # Create formatters
#         detailed_formatter = logging.Formatter(
#             '%(asctime)s | %(levelname)-8s | %(funcName)-20s | %(message)s',
#             datefmt='%Y-%m-%d %H:%M:%S'
#         )
        
#         simple_formatter = logging.Formatter(
#             '%(asctime)s | %(levelname)-8s | %(message)s',
#             datefmt='%Y-%m-%d %H:%M:%S'
#         )
        
#         # Main migration log file
#         main_log_file = os.path.join(log_dir, f"fmc_migration_{timestamp}.log")
#         main_handler = logging.FileHandler(main_log_file, encoding='utf-8')
#         main_handler.setLevel(logging.INFO)
#         main_handler.setFormatter(detailed_formatter)
#         self.logger.addHandler(main_handler)
        
#         # Error-only log file
#         error_log_file = os.path.join(log_dir, f"fmc_errors_{timestamp}.log")
#         error_handler = logging.FileHandler(error_log_file, encoding='utf-8')
#         error_handler.setLevel(logging.ERROR)
#         error_handler.setFormatter(detailed_formatter)
#         self.logger.addHandler(error_handler)
        
#         # Format corrections log file
#         corrections_log_file = os.path.join(log_dir, f"fmc_format_corrections_{timestamp}.log")
#         self.corrections_handler = logging.FileHandler(corrections_log_file, encoding='utf-8')
#         self.corrections_handler.setLevel(logging.INFO)
#         self.corrections_handler.setFormatter(simple_formatter)
        
#         # Debug log file (detailed API calls and responses)
#         debug_log_file = os.path.join(log_dir, f"fmc_debug_{timestamp}.log")
#         debug_handler = logging.FileHandler(debug_log_file, encoding='utf-8')
#         debug_handler.setLevel(logging.DEBUG)
#         debug_handler.setFormatter(detailed_formatter)
#         self.logger.addHandler(debug_handler)
        
#         # Console handler for immediate feedback
#         console_handler = logging.StreamHandler(sys.stdout)
#         console_handler.setLevel(logging.INFO)
#         console_handler.setFormatter(simple_formatter)
#         self.logger.addHandler(console_handler)
        
#         # Store log file paths for reference
#         self.log_files = {
#             'main': main_log_file,
#             'errors': error_log_file,
#             'corrections': corrections_log_file,
#             'debug': debug_log_file
#         }
        
#         # Log session start
#         self.logger.info("="*80)
#         self.logger.info(f"FMC Migration Session Started")
#         self.logger.info(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
#         self.logger.info(f"FMC Host: {self.fmc_host}")
#         self.logger.info(f"Overwrite Mode: {self.overwrite}")
#         self.logger.info(f"Log Files Created:")
#         for log_type, log_path in self.log_files.items():
#             self.logger.info(f"  - {log_type.title()}: {log_path}")
#         self.logger.info("="*80)
    
#     def log(self, message: str, level: str = "INFO"):
#         """Enhanced logging method with file output and structured formatting"""
#         # Map string levels to logging constants
#         level_map = {
#             'DEBUG': logging.DEBUG,
#             'INFO': logging.INFO,
#             'WARN': logging.WARNING,
#             'WARNING': logging.WARNING,
#             'ERROR': logging.ERROR,
#             'CRITICAL': logging.CRITICAL
#         }
        
#         log_level = level_map.get(level.upper(), logging.INFO)
#         self.logger.log(log_level, message)
    
#     def log_format_correction(self, object_name: str, corrections: List[Dict]):
#         """Log format corrections to dedicated corrections log file"""
#         corrections_logger = logging.getLogger('FormatCorrections')
#         corrections_logger.addHandler(self.corrections_handler)
#         corrections_logger.setLevel(logging.INFO)
        
#         corrections_logger.info(f"Format corrections applied to '{object_name}':")
#         for correction in corrections:
#             corrections_logger.info(f"  Field: {correction['field']}")
#             corrections_logger.info(f"  Original: {correction['original']}")
#             corrections_logger.info(f"  Corrected: {correction['corrected']}")
#             corrections_logger.info(f"  Reason: {correction['reason']}")
#             corrections_logger.info("  " + "-"*50)
    
#     def log_api_call(self, method: str, url: str, status_code: int, response_data: Optional[Dict] = None, request_data: Optional[Dict] = None):
#         """Log detailed API call information for debugging"""
#         self.logger.debug(f"API Call: {method} {url}")
#         self.logger.debug(f"Status Code: {status_code}")
        
#         if request_data:
#             self.logger.debug(f"Request Data: {json.dumps(request_data, indent=2)}")
        
#         if response_data:
#             # Log response, but truncate if too large
#             response_str = json.dumps(response_data, indent=2)
#             if len(response_str) > 1000:
#                 response_str = response_str[:997] + "..."
#             self.logger.debug(f"Response Data: {response_str}")
    
#     def log_operation_summary(self, operation_type: str, success_count: int, failure_count: int, details: Dict = None):
#         """Log operation summaries for analysis"""
#         self.logger.info(f"Operation Summary: {operation_type}")
#         self.logger.info(f"  Successful: {success_count}")
#         self.logger.info(f"  Failed: {failure_count}")
#         self.logger.info(f"  Success Rate: {success_count/(success_count+failure_count)*100:.1f}%")
        
#         if details:
#             for key, value in details.items():
#                 self.logger.info(f"  {key}: {value}")
    
#     def log_session_summary(self):
#         """Log comprehensive session summary"""
#         self.logger.info("="*80)
#         self.logger.info("MIGRATION SESSION SUMMARY")
#         self.logger.info("="*80)
        
#         # Get operation counts from checkpoint
#         created_count = len(self.migration_checkpoint['created_objects'])
#         modified_count = len(self.migration_checkpoint['modified_objects']) 
#         failed_count = len(self.migration_checkpoint['failed_operations'])
        
#         self.logger.info(f"Objects Created: {created_count}")
#         self.logger.info(f"Objects Modified: {modified_count}")
#         self.logger.info(f"Operations Failed: {failed_count}")
        
#         total_operations = created_count + modified_count + failed_count
#         if total_operations > 0:
#             success_rate = (created_count + modified_count) / total_operations * 100
#             self.logger.info(f"Overall Success Rate: {success_rate:.1f}%")
        
#         # Log checkpoint file location if created
#         checkpoint_file = getattr(self, 'last_checkpoint_file', None)
#         if checkpoint_file:
#             self.logger.info(f"Rollback Checkpoint: {checkpoint_file}")
        
#         # Log file locations
#         self.logger.info("Log Files:")
#         for log_type, log_path in self.log_files.items():
#             if os.path.exists(log_path) and os.path.getsize(log_path) > 0:
#                 size_kb = os.path.getsize(log_path) / 1024
#                 self.logger.info(f"  - {log_type.title()}: {log_path} ({size_kb:.1f} KB)")
        
#         session_end = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
#         self.logger.info(f"Session Ended: {session_end}")
#         self.logger.info("="*80)
    
#     def authenticate(self) -> bool:
#         """Authenticate with FMC and get access tokens"""
#         self.log("Authenticating with FMC...")
        
#         auth_url = f"{self.fmc_host}/api/fmc_platform/v1/auth/generatetoken"
        
#         try:
#             response = requests.post(
#                 auth_url,
#                 auth=HTTPBasicAuth(self.username, self.password),
#                 verify=self.verify_ssl,
#                 timeout=30
#             )
            
#             if response.status_code == 204:
#                 self.access_token = response.headers.get('X-auth-access-token')
#                 self.refresh_token = response.headers.get('X-auth-refresh-token')
#                 self.domain_uuid = response.headers.get('DOMAIN_UUID')
                
#                 self.log("Authentication successful!")
#                 self.log(f"Domain UUID: {self.domain_uuid}")
#                 return True
#             else:
#                 self.log(f"Authentication failed with status code: {response.status_code}", "ERROR")
#                 self.log(f"Response: {response.text}", "ERROR")
#                 return False
                
#         except requests.exceptions.RequestException as e:
#             self.log(f"Authentication error: {e}", "ERROR")
#             return False
    
#     def refresh_authentication(self) -> bool:
#         """Refresh the access token"""
#         self.log("Refreshing authentication token...")
        
#         refresh_url = f"{self.fmc_host}/api/fmc_platform/v1/auth/refreshtoken"
#         headers = {
#             'X-auth-access-token': self.access_token,
#             'X-auth-refresh-token': self.refresh_token
#         }
        
#         try:
#             response = requests.post(
#                 refresh_url,
#                 headers=headers,
#                 verify=self.verify_ssl,
#                 timeout=30
#             )
            
#             if response.status_code == 204:
#                 self.access_token = response.headers.get('X-auth-access-token')
#                 self.log("Token refreshed successfully!")
#                 return True
#             else:
#                 self.log(f"Token refresh failed: {response.status_code}", "ERROR")
#                 return False
                
#         except requests.exceptions.RequestException as e:
#             self.log(f"Token refresh error: {e}", "ERROR")
#             return False
    
#     def make_api_call(self, method: str, endpoint: str, data: Optional[Dict] = None, 
#                      params: Optional[Dict] = None, max_retries: int = 3) -> Optional[Dict]:
#         """Make an API call to FMC with comprehensive retry logic and error handling"""
        
#         # Format endpoint with domain UUID
#         if '{domain_uuid}' in endpoint:
#             endpoint = endpoint.format(domain_uuid=self.domain_uuid)
        
#         url = f"{self.fmc_host}{endpoint}"
#         headers = {
#             'Content-Type': 'application/json',
#             'X-auth-access-token': self.access_token
#         }
        
#         # Log the API call attempt
#         self.log(f"Making {method} request to {endpoint}", "DEBUG")
#         if data:
#             self.log(f"Request payload size: {len(json.dumps(data))} bytes", "DEBUG")
        
#         retry_count = 0
#         while retry_count <= max_retries:
#             try:
#                 # Only log in verbose mode unless it's an error
#                 if not getattr(self, 'skip_existing_check', False) or retry_count > 0:
#                     self.log(f"Making {method} request to {endpoint}")
                
#                 # Rate limiting - only delay on retries, not initial calls
#                 if retry_count > 0:
#                     delay = min(self.api_delay * (2 ** retry_count), 30)  # Exponential backoff, max 30s
#                     self.log(f"Retry {retry_count}/{max_retries} - waiting {delay}s before retry", "WARN")
#                     time.sleep(delay)
#                 # Remove unnecessary delay on successful first attempts
                
#                 # Make the request
#                 if method.upper() == 'GET':
#                     response = requests.get(url, headers=headers, verify=self.verify_ssl, params=params)
#                 elif method.upper() == 'POST':
#                     response = requests.post(url, headers=headers, json=data, verify=self.verify_ssl, params=params)
#                 elif method.upper() == 'PUT':
#                     response = requests.put(url, headers=headers, json=data, verify=self.verify_ssl, params=params)
#                 elif method.upper() == 'DELETE':
#                     response = requests.delete(url, headers=headers, verify=self.verify_ssl, params=params)
#                 else:
#                     self.log(f"Unsupported HTTP method: {method}", "ERROR")
#                     return None
                
#                 # Parse response
#                 try:
#                     response_data = response.json() if response.text else {}
#                 except json.JSONDecodeError:
#                     response_data = {"raw_response": response.text}
                
#                 # Log detailed API call information (only for errors in fast mode)
#                 if response.status_code >= 400 or not getattr(self, 'skip_existing_check', False):
#                     self.log_api_call(method, url, response.status_code, response_data, data)
                
#                 # Handle response based on status code
#                 if response.status_code == 200 or response.status_code == 201:
#                     self.log("API call successful: " + str(response.status_code))
#                     # Clear last error on success
#                     self.last_api_error = None
#                     # Reset rate limit counter on successful calls
#                     if hasattr(self, '_rate_limit_count') and self._rate_limit_count > 0:
#                         self._rate_limit_count = max(0, self._rate_limit_count - 1)
#                     return response_data
                
#                 elif response.status_code == 401:
#                     self.log("Authentication failed (401) - attempting re-authentication", "WARN")
#                     if self.authenticate():
#                         headers['X-auth-access-token'] = self.access_token
#                         retry_count += 1
#                         continue
#                     else:
#                         self.log("Re-authentication failed", "ERROR")
#                         return None
                
#                 elif response.status_code == 429:
#                     # Aggressive rate limiting handling for 100% synchronization guarantee
#                     retry_after = response.headers.get('Retry-After')
#                     if retry_after:
#                         try:
#                             rate_limit_delay = int(retry_after)
#                         except ValueError:
#                             rate_limit_delay = None
#                     else:
#                         rate_limit_delay = None
                    
#                     # If no Retry-After header, use more aggressive backoff for guaranteed success
#                     if rate_limit_delay is None:
#                         # More aggressive base delay to ensure success
#                         base_delay = 5  # Start with 5 seconds instead of 1
#                         rate_limit_delay = min(base_delay * (2 ** retry_count), 300)  # Cap at 5 minutes
#                         # Add jitter (±25%) to prevent thundering herd
#                         jitter = rate_limit_delay * 0.25 * (random.random() - 0.5)
#                         rate_limit_delay = max(3, rate_limit_delay + jitter)  # Minimum 3 seconds
                    
#                     # For 100% synchronization, we need to be more patient with rate limits
#                     self.log(f"🛑 Rate limited (429) - aggressive backoff: waiting {rate_limit_delay:.1f}s for guaranteed success (attempt {retry_count + 1})", "WARN")
                    
#                     # Track rate limiting for circuit breaker pattern
#                     if not hasattr(self, '_rate_limit_count'):
#                         self._rate_limit_count = 0
#                     self._rate_limit_count += 1
                    
#                     # If we're getting heavily rate limited, implement circuit breaker
#                     if self._rate_limit_count > 20:
#                         circuit_breaker_delay = 60  # 1 minute circuit breaker
#                         self.log(f"🛑 CIRCUIT BREAKER: Too many rate limits ({self._rate_limit_count}) - waiting {circuit_breaker_delay}s", "WARN")
#                         time.sleep(circuit_breaker_delay)
#                         self._rate_limit_count = 0  # Reset counter
                    
#                     time.sleep(rate_limit_delay)
#                     retry_count += 1
#                     continue
                
#                 elif response.status_code in [500, 502, 503, 504]:
#                     self.log(f"Server error ({response.status_code}) - will retry", "WARN")
#                     retry_count += 1
#                     if retry_count <= max_retries:
#                         continue
                
#                 # Log detailed error information
#                 self.log(f"API call failed: {response.status_code}", "ERROR")
                
#                 # Capture last error for fallback logic
#                 self.last_api_error = {
#                     'status_code': response.status_code,
#                     'response': response_data,
#                     'response_text': json.dumps(response_data, indent=2) if response_data else response.text
#                 }
                
#                 error_details = {
#                     'status_code': response.status_code,
#                     'url': url,
#                     'method': method,
#                     'response': response_data,
#                     'retry_count': retry_count
#                 }
                
#                 if response_data:
#                     response_text = json.dumps(response_data, indent=2)
#                     self.log(f"Response: {response_text}", "ERROR")
                    
#                     # Provide specific guidance based on error type
#                     if response.status_code == 400:
#                         self._log_400_error_guidance(response_text, data)
#                     elif response.status_code == 404:
#                         self.log("GUIDANCE: Resource not found (404). Consider:", "INFO")
#                         self.log("  - Verify the endpoint URL is correct", "INFO")
#                         self.log("  - Check if the object ID exists", "INFO")
#                         self.log("  - Ensure proper domain access", "INFO")
#                     elif response.status_code == 422:
#                         self.log("GUIDANCE: Unprocessable entity (422). Consider:", "INFO")
#                         self.log("  - Review data validation requirements", "INFO")
#                         self.log("  - Check for missing required fields", "INFO")
#                         self.log("  - Verify data format compliance", "INFO")
                
#                 # Track failed operation
#                 self.migration_checkpoint['failed_operations'].append(error_details)
#                 return None
                
#             except requests.exceptions.ConnectionError as e:
#                 self.log(f"Connection error (attempt {retry_count + 1}): {str(e)}", "ERROR")
#                 retry_count += 1
#                 if retry_count <= max_retries:
#                     delay = min(5 * retry_count, 30)
#                     self.log(f"Retrying in {delay} seconds...", "WARN")
#                     time.sleep(delay)
#                     continue
#                 else:
#                     self.log("Max retries exceeded for connection error", "ERROR")
#                     return None
            
#             except requests.exceptions.RequestException as e:
#                 self.log(f"Request error: {str(e)}", "ERROR")
#                 return None
            
#             except Exception as e:
#                 self.log(f"Unexpected error in API call: {str(e)}", "ERROR")
#                 return None
        
#         self.log(f"API call failed after {max_retries} retries", "ERROR")
#         return None
    
#     def _parse_error_response(self, response) -> Dict:
#         """Parse FMC error response for detailed error information"""
#         try:
#             error_data = response.json()
#             return error_data.get('error', {})
#         except json.JSONDecodeError:
#             return {'messages': [{'description': response.text}]}
    
#     def _log_400_error_guidance(self, response_text: str, request_data: Optional[Dict]):
#         """Provide specific guidance for 400 Bad Request errors"""
#         if 'already exists' in response_text:
#             if self.overwrite:
#                 # Overwrite is enabled but still failing - provide concise guidance
#                 self.log("GUIDANCE: Conflict with --overwrite enabled - attempting targeted lookup and update", "DEBUG")
#             else:
#                 self.log("GUIDANCE: Object name conflict detected. Consider:", "INFO")
#                 self.log("  - Enable overwrite mode with --overwrite flag to replace existing objects", "INFO")
#                 self.log("  - Check for case sensitivity differences in object names", "INFO")
#                 self.log("  - Ensure object type matches (host vs network vs service)", "INFO")
#         elif 'input type mismatch' in response_text.lower() or 'invalid value' in response_text.lower():
#             self.log("GUIDANCE: Data format mismatch detected. Consider:", "INFO")
#             self.log("  - IP ranges should be in CIDR format (e.g., 10.0.0.0/24, not ********-**********)", "INFO")
#             self.log("  - Network objects require CIDR notation", "INFO")
#             self.log("  - Port ranges should be formatted as start-end (e.g., 80-443)", "INFO")
#             self.log("  - Automatic format correction is now enabled for common issues", "INFO")
#             if request_data:
#                 self.log(f"  - Problematic data: {json.dumps(request_data, indent=2)}", "DEBUG")
#         elif 'invalid' in response_text.lower():
#             self.log("GUIDANCE: Invalid data format detected. Consider:", "INFO")
#             self.log("  - Verify all required fields are present", "INFO")
#             self.log("  - Check data types match API requirements", "INFO")
#             if request_data:
#                 self.log(f"  - Request data: {json.dumps(request_data, indent=2)}", "DEBUG")
#         elif 'reference' in response_text.lower():
#             self.log("GUIDANCE: Reference error detected. Consider:", "INFO")
#             self.log("  - Verify referenced objects exist before creating groups", "INFO")
#             self.log("  - Check object UUIDs are correct and accessible", "INFO")
#             self.log("  - Ensure objects are created in proper dependency order", "INFO")
    
#     def validate_data_formats(self, config_file: str) -> Dict[str, Any]:
#         """Validate data formats in migration configuration and suggest corrections"""
#         self.log("Validating data formats in migration configuration...")
        
#         try:
#             with open(config_file, 'r') as f:
#                 config = json.load(f)
#         except FileNotFoundError:
#             return {'error': f"Configuration file '{config_file}' not found"}
#         except json.JSONDecodeError as e:
#             return {'error': f"Invalid JSON in configuration file: {e}"}
        
#         format_results = {
#             'correctable_issues': [],
#             'potential_issues': [],
#             'objects_to_correct': 0,
#             'correction_summary': {}
#         }
        
#         api_calls = config.get('api_calls', {})
        
#         # Check network objects for format issues
#         if 'network_objects' in api_calls:
#             for obj in api_calls['network_objects']['data']:
#                 corrected_obj = self.correct_data_format(obj)
                
#                 # Check if corrections were made by comparing with original
#                 if 'value' in obj and 'value' in corrected_obj:
#                     if obj['value'] != corrected_obj['value']:
#                         format_results['correctable_issues'].append({
#                             'object_name': obj.get('name', 'unknown'),
#                             'object_type': 'network',
#                             'field': 'value',
#                             'original': obj['value'],
#                             'corrected': corrected_obj['value'],
#                             'issue_type': 'IP range format'
#                         })
#                         format_results['objects_to_correct'] += 1
        
#         # Check host objects for format issues
#         if 'host_objects' in api_calls:
#             for obj in api_calls['host_objects']['data']:
#                 corrected_obj = self.correct_data_format(obj)
                
#                 if 'value' in obj and 'value' in corrected_obj:
#                     if obj['value'] != corrected_obj['value']:
#                         format_results['correctable_issues'].append({
#                             'object_name': obj.get('name', 'unknown'),
#                             'object_type': 'host',
#                             'field': 'value',
#                             'original': obj['value'],
#                             'corrected': corrected_obj['value'],
#                             'issue_type': 'IP range in host object'
#                         })
#                         format_results['objects_to_correct'] += 1
        
#         # Check service objects for format issues
#         if 'service_objects' in api_calls:
#             for obj in api_calls['service_objects']['data']:
#                 corrected_obj = self.correct_data_format(obj)
                
#                 if 'port' in obj and 'port' in corrected_obj:
#                     if obj['port'] != corrected_obj['port']:
#                         format_results['correctable_issues'].append({
#                             'object_name': obj.get('name', 'unknown'),
#                             'object_type': 'service',
#                             'field': 'port',
#                             'original': obj['port'],
#                             'corrected': corrected_obj['port'],
#                             'issue_type': 'Port format'
#                         })
#                         format_results['objects_to_correct'] += 1
        
#         # Summarize correction types
#         correction_types = {}
#         for issue in format_results['correctable_issues']:
#             issue_type = issue['issue_type']
#             correction_types[issue_type] = correction_types.get(issue_type, 0) + 1
        
#         format_results['correction_summary'] = correction_types
        
#         return format_results
    
#     def execute_migration_config_improved(self, config_file: str) -> Dict[str, int]:
#         """Execute migration with 100% synchronization guarantee - no operations can fail"""
#         self.log(f"Loading migration configuration from {config_file}...")
#         self.log("🎯 100% SYNCHRONIZATION MODE: All operations must succeed", "INFO")
        
#         try:
#             with open(config_file, 'r') as f:
#                 config = json.load(f)
#         except FileNotFoundError:
#             self.log(f"Configuration file '{config_file}' not found", "ERROR")
#             return {}
#         except json.JSONDecodeError as e:
#             self.log(f"Invalid JSON in configuration file: {e}", "ERROR")
#             return {}
        
#         if not self.authenticate():
#             self.log("Failed to authenticate with FMC", "ERROR")
#             return {}
        
#         # Initialize object cache for optimal performance
#         self.initialize_cache()
        
#         # PHASE 0: Optimized pre-flight validation to ensure 100% success
#         self.log("🔍 PHASE 0: Optimized pre-flight validation for 100% synchronization guarantee...", "INFO")
#         self.log("🎯 Using efficient validation approach to prevent rate limiting during validation", "INFO")
        
#         validation_result = self._comprehensive_pre_flight_validation(config)
#         if not validation_result['can_guarantee_success']:
#             self.log("❌ PRE-FLIGHT VALIDATION FAILED: Cannot guarantee 100% synchronization", "ERROR")
#             self.log("Issues that would prevent 100% success:", "ERROR")
#             for issue in validation_result['blocking_issues']:
#                 self.log(f"  - {issue}", "ERROR")
#             self.log("Migration aborted to prevent partial synchronization", "ERROR")
#             return {'errors': 1, 'validation_failed': True, 'blocking_issues': validation_result['blocking_issues']}
        
#         self.log("✅ Pre-flight validation passed - 100% synchronization can be guaranteed", "INFO")
#         if validation_result.get('warnings'):
#             self.log(f"⚠️ {len(validation_result['warnings'])} warnings will be resolved during migration with aggressive retry", "INFO")
        
#         # Initialize comprehensive results tracking
#         results = {
#             'host_objects_created': 0,
#             'host_objects_updated': 0,
#             'host_objects_skipped': 0,
#             'network_objects_created': 0,
#             'network_objects_updated': 0,
#             'network_objects_skipped': 0,
#             'range_objects_created': 0,
#             'range_objects_updated': 0,
#             'range_objects_skipped': 0,
#             'fqdn_objects_created': 0,
#             'fqdn_objects_updated': 0,
#             'fqdn_objects_skipped': 0,
#             'service_objects_created': 0,
#             'service_objects_overwritten': 0,
#             'service_objects_skipped': 0,
#             'object_groups_created': 0,
#             'object_groups_overwritten': 0,
#             'object_groups_skipped': 0,
#             'service_groups_created': 0,
#             'service_groups_overwritten': 0,
#             'service_groups_skipped': 0,
#             'access_rules_created': 0,
#             'errors': 0,
#             'warnings': 0
#         }
        
#         api_calls = config.get('api_calls', {})
        
#         # Get existing objects for validation and conflict resolution (optional for speed)
#         if getattr(self, 'skip_existing_check', False):
#             self.log("Skipping existing objects check for faster migration (conflicts will be handled during creation)")
#             existing_objects = {}
#         else:
#             self.log("Retrieving existing objects from FMC...")
#             existing_objects = self.get_existing_objects()
        
#         # Phase 1: Individual Objects (no dependencies)
#         # Following Cisco's documented order: Host → Network → Range → FQDN → Service
#         individual_objects = [
#             ('host_objects', 'host', 'Host objects'),
#             ('network_objects', 'network', 'Network objects'), 
#             ('range_objects', 'range', 'Range objects'),
#             ('fqdn_objects', 'fqdn', 'FQDN objects'),
#             ('service_objects', 'service', 'Service objects')
#         ]
        
#         for obj_key, obj_type, description in individual_objects:
#             if obj_key in api_calls and api_calls[obj_key]['data']:
#                 phase_name = f"phase1_{obj_key}"
                
#                 # Check if this phase was already completed
#                 if self.is_phase_completed(phase_name):
#                     self.log(f"⏭️  Phase 1: {description} already completed - skipping")
#                     # Restore results from completed phase
#                     phase_results = self.get_phase_results(phase_name)
#                     type_key = obj_key.replace('_objects', '')
#                     results[f'{type_key}_created'] = phase_results.get('created', 0)
#                     results[f'{type_key}_updated'] = phase_results.get('updated', 0)
#                     results[f'{type_key}_skipped'] = phase_results.get('skipped', 0)
#                     if 'overwritten' in phase_results:
#                         results[f'{type_key}_overwritten'] = phase_results.get('overwritten', 0)
#                     results['errors'] += phase_results.get('errors', 0)
#                     results['warnings'] += phase_results.get('skipped', 0)
#                     continue
                
#                 self.log(f"Phase 1: Creating {description}...")
#                 self.migration_checkpoint['current_phase'] = phase_name
                
#                 # Use bulk operations with guaranteed success for 100% synchronization
#                 bulk_results = self.create_objects_bulk_guaranteed(obj_type, api_calls[obj_key]['data'], existing_objects)
                
#                 # Check for critical failure requiring rollback
#                 if bulk_results.get('requires_rollback'):
#                     self.log(f"❌ CRITICAL FAILURE in Phase 1: {description}", "ERROR")
#                     self.log(f"Failed object: {bulk_results.get('failed_object', 'unknown')} - initiating automatic rollback", "ERROR")
#                     rollback_result = self.rollback_migration(dry_run=False)
#                     results['rollback_executed'] = True
#                     results['rollback_result'] = rollback_result
#                     results['phase_1_failure'] = True
#                     results['failed_object_type'] = obj_key
#                     results['failed_object_name'] = bulk_results.get('failed_object', 'unknown')
#                     return results
                
#                 # Map bulk results to individual result keys
#                 type_key = obj_key.replace('_objects', '')
#                 results[f'{type_key}_created'] = bulk_results.get('created', 0)
#                 results[f'{type_key}_updated'] = bulk_results.get('updated', 0)
#                 results[f'{type_key}_skipped'] = bulk_results.get('skipped', 0)
                
#                 # Handle overwritten objects (mainly for services)
#                 if 'overwritten' in bulk_results:
#                     results[f'{type_key}_overwritten'] = bulk_results.get('overwritten', 0)
                
#                 results['errors'] += bulk_results.get('errors', 0)
#                 results['warnings'] += bulk_results.get('skipped', 0)  # Skipped counts as warnings
                
#                 # Log phase completion and mark as complete
#                 total_processed = bulk_results.get('created', 0) + bulk_results.get('updated', 0) + bulk_results.get('overwritten', 0)
#                 self.log(f"Phase 1 Complete: {description} - {total_processed} objects successfully processed")
                
#                 # Mark this phase as completed
#                 self.mark_phase_complete(phase_name, bulk_results)
        
#         # Phase 2: Object Groups (depend on individual objects)
#         # Use topological sorting for proper dependency order
#         group_objects = [
#             ('object_groups', 'network_group', 'Network object groups'),
#             ('service_groups', 'service_group', 'Service object groups')
#         ]
        
#         for group_key, group_type, description in group_objects:
#             if group_key in api_calls and api_calls[group_key]['data']:
#                 self.log(f"Phase 2: Creating {description}...")
                
#                 # Apply topological sorting for dependency resolution
#                 try:
#                     sorted_groups = self.topological_sort(api_calls[group_key]['data'])
#                     self.log(f"Applied topological sorting to {len(sorted_groups)} {description.lower()}")
#                 except Exception as e:
#                     self.log(f"Topological sort failed for {description}: {e}, using fallback", "WARN")
#                     sorted_groups = api_calls[group_key]['data']
                
#                 # Create groups with retry logic for dependency resolution
#                 groups_created = 0
#                 groups_failed = 0
#                 groups_skipped = 0
#                 max_passes = 5  # Reduced from 10 since we have better sorting now
                
#                 remaining_groups = sorted_groups.copy()
                
#                 for pass_num in range(max_passes):
#                     if not remaining_groups:
#                         break
                    
#                     self.log(f"Phase 2 Pass {pass_num + 1}: Processing {len(remaining_groups)} {description.lower()}")
#                     groups_created_this_pass = []
#                     next_pass_groups = []
                    
#                     for group in remaining_groups:
#                         # Use aggressive retry logic for guaranteed success
#                         result = self.create_object_with_retry(group_type, group, existing_objects, max_retries=10)
                        
#                         if result['success']:
#                             if result['action'] == 'created':
#                                 groups_created += 1
#                             elif result['action'] == 'overwritten':
#                                 groups_created += 1  # Count as success
                            
#                             groups_created_this_pass.append(group['name'])
                            
#                             # Update cache immediately for dependency resolution
#                             if result['object_id']:
#                                 new_obj = {'id': result['object_id'], 'name': group['name'], 'type': 'NetworkGroup' if group_type == 'network_group' else 'ProtocolPortGroup'}
#                                 # Cache update handled elsewhere
                        
#                         elif 'missing dependencies' in result.get('message', '').lower():
#                             # Try again in next pass
#                             next_pass_groups.append(group)
#                         elif result.get('requires_rollback'):
#                             # Critical failure requiring rollback for 100% synchronization
#                             self.log(f"❌ CRITICAL FAILURE: Cannot guarantee 100% synchronization", "ERROR")
#                             self.log(f"Failed object: {group.get('name', 'unknown')} - initiating automatic rollback", "ERROR")
#                             rollback_result = self.rollback_migration(dry_run=False)
#                             results['rollback_executed'] = True
#                             results['rollback_result'] = rollback_result
#                             return results
#                         else:
#                             groups_failed += 1
#                             self.log(f"{description} creation failed for {group.get('name', 'unknown')}: {result.get('message', 'Unknown error')}", "ERROR")
                    
#                     # Update for next pass
#                     remaining_groups = next_pass_groups
                    
#                     if groups_created_this_pass:
#                         self.log(f"Phase 2 Pass {pass_num + 1} Complete: Created {len(groups_created_this_pass)} {description.lower()}")
#                     else:
#                         self.log(f"Phase 2 Pass {pass_num + 1}: No progress made, ending dependency resolution")
#                         break
                
#                 # Handle any remaining unresolved groups
#                 groups_skipped = len(remaining_groups)
#                 if groups_skipped > 0:
#                     self.log(f"Phase 2 Warning: {groups_skipped} {description.lower()} could not be created due to unresolved dependencies", "WARN")
#                     for group in remaining_groups:
#                         self.log(f"  - Unresolved: {group.get('name', 'unknown')}", "WARN")
                
#                 # Update results
#                 group_key_prefix = group_key.replace('_groups', '_group')
#                 results[f'{group_key_prefix}s_created'] = groups_created
#                 results[f'{group_key_prefix}s_skipped'] = groups_skipped
#                 results['errors'] += groups_failed
#                 results['warnings'] += groups_skipped
                
#                 self.log(f"Phase 2 Complete: {description} - {groups_created} created, {groups_skipped} skipped, {groups_failed} failed")
        
#         # Phase 3: Policies and Rules (depend on objects and groups)
#         if 'access_rules' in api_calls and api_calls['access_rules']['data']:
#             self.log("Phase 3: Creating access rules...")
            
#             # Access rules can reference any previously created objects
#             rules_created = 0
#             rules_failed = 0
            
#             for rule in api_calls['access_rules']['data']:
#                 try:
#                     # Apply validation and reference resolution
#                     validation = self.validate_object_dependencies(rule, existing_objects)
#                     if validation['valid']:
#                         # Create rule (implementation depends on your access rule creation logic)
#                         # This is a placeholder - implement based on your access rule structure
#                         self.log(f"Access rule validation passed for: {rule.get('name', 'unknown')}", "DEBUG")
#                         rules_created += 1
#                     else:
#                         self.log(f"Access rule validation failed: {rule.get('name', 'unknown')}", "ERROR")
#                         rules_failed += 1
#                 except Exception as e:
#                     self.log(f"Error processing access rule {rule.get('name', 'unknown')}: {e}", "ERROR")
#                     rules_failed += 1
            
#             results['access_rules_created'] = rules_created
#             results['errors'] += rules_failed
            
#             self.log(f"Phase 3 Complete: Access rules - {rules_created} created, {rules_failed} failed")
        
#         # FINAL VALIDATION: Ensure 100% synchronization was achieved
#         self.log("🔍 FINAL VALIDATION: Verifying 100% synchronization achievement...", "INFO")
        
#         total_attempted = 0
#         total_successful = 0
        
#         for key, value in results.items():
#             if key.endswith('_created') or key.endswith('_updated') or key.endswith('_overwritten'):
#                 total_successful += value
#             elif key.endswith('_skipped') and value > 0:
#                 self.log(f"⚠️ SYNCHRONIZATION WARNING: {value} objects were skipped in {key}", "WARN")
        
#         # Calculate total objects from original config
#         total_objects_in_config = sum(len(api_calls[obj_key].get('data', [])) for obj_key in api_calls if obj_key.endswith('_objects'))
        
#         if total_successful == total_objects_in_config and results.get('errors', 0) == 0:
#             self.log("✅ 100% SYNCHRONIZATION ACHIEVED: All objects successfully migrated", "INFO")
#             results['synchronization_complete'] = True
#             results['synchronization_percentage'] = 100.0
#         else:
#             sync_percentage = (total_successful / total_objects_in_config * 100) if total_objects_in_config > 0 else 0
#             self.log(f"❌ SYNCHRONIZATION INCOMPLETE: {sync_percentage:.1f}% ({total_successful}/{total_objects_in_config})", "ERROR")
#             results['synchronization_complete'] = False
#             results['synchronization_percentage'] = sync_percentage
            
#             if sync_percentage < 100.0:
#                 self.log("🔄 Initiating rollback due to incomplete synchronization", "ERROR")
#                 rollback_result = self.rollback_migration(dry_run=False)
#                 results['rollback_executed'] = True
#                 results['rollback_result'] = rollback_result
#                 results['rollback_reason'] = f"Incomplete synchronization: {sync_percentage:.1f}%"
        
#         # Session completion
#         self.log_session_summary()
        
#         # Create checkpoint for rollback (only if synchronization was successful)
#         if results.get('synchronization_complete', False):
#             checkpoint_file = self.create_migration_checkpoint()
#             if checkpoint_file:
#                 results['checkpoint_file'] = checkpoint_file
        
#         return results

#     def execute_migration_config(self, config_file: str) -> Dict[str, int]:
#         """Legacy method - redirects to improved implementation"""
#         return self.execute_migration_config_improved(config_file)

#     def execute_migration_config_legacy(self, config_file: str) -> Dict[str, int]:
#         """Original implementation preserved for compatibility"""
#         self.log(f"Loading migration configuration from {config_file}...")
        
#         try:
#             with open(config_file, 'r') as f:
#                 config = json.load(f)
#         except FileNotFoundError:
#             self.log(f"Configuration file '{config_file}' not found", "ERROR")
#             return {}
#         except json.JSONDecodeError as e:
#             self.log(f"Invalid JSON in configuration file: {e}", "ERROR")
#             return {}
        
#         if not self.authenticate():
#             self.log("Failed to authenticate with FMC", "ERROR")
#             return {}
        
#         # Initialize object cache for optimal performance
#         self.initialize_cache()
        
#         results = {
#             'host_objects_created': 0,
#             'host_objects_updated': 0,
#             'host_objects_skipped': 0,
#             'network_objects_created': 0,
#             'network_objects_updated': 0,
#             'network_objects_skipped': 0,
#             'service_objects_created': 0,
#             'service_objects_overwritten': 0,
#             'service_objects_skipped': 0,
#             'object_groups_created': 0,
#             'object_groups_overwritten': 0,
#             'object_groups_skipped': 0,
#             'access_rules_created': 0,
#             'errors': 0,
#             'warnings': 0
#         }
        
#         api_calls = config.get('api_calls', {})
        
#         # Get existing objects if overwrite is enabled or for validation
#         self.log("Retrieving existing objects from FMC...")
#         existing_objects = self.get_existing_objects()
        
#         # Track created objects during this session for dependency resolution
#         session_objects = {
#             'hosts': [],
#             'networks': [],
#             'protocol_ports': [],
#             'network_groups': []
#         }
        
#         # Create host objects first
#         if 'host_objects' in api_calls:
#             self.log("Creating/updating host objects...")
#             for obj in api_calls['host_objects']['data']:
#                 result = self.create_object_with_validation('host', obj, existing_objects)
                
#                 if result['success']:
#                     if result['action'] == 'created':
#                         results['host_objects_created'] += 1
#                         # Add to session tracking
#                         new_obj = {'id': result['object_id'], 'name': obj['name'], 'type': 'Host'}
#                         session_objects['hosts'].append(new_obj)
#                         existing_objects['hosts'].append(new_obj)
#                     elif result['action'] == 'updated':
#                         results['host_objects_updated'] += 1
#                         # Also add updated objects to cache for dependency resolution
#                         new_obj = {'id': result['object_id'], 'name': obj['name'], 'type': 'Host'}
#                         session_objects['hosts'].append(new_obj)
#                         existing_objects['hosts'].append(new_obj)
#                 else:
#                     if 'missing dependencies' in result.get('message', '').lower():
#                         results['host_objects_skipped'] += 1
#                         results['warnings'] += 1
#                     else:
#                         results['errors'] += 1
#                         self.log(f"Host object error: {result['message']}", "ERROR")
        
#         # Create network objects
#         if 'network_objects' in api_calls:
#             self.log("Creating/updating network objects...")
#             for obj in api_calls['network_objects']['data']:
#                 result = self.create_object_with_validation('network', obj, existing_objects)
                
#                 if result['success']:
#                     if result['action'] == 'created':
#                         results['network_objects_created'] += 1
#                         # Add to session tracking
#                         new_obj = {'id': result['object_id'], 'name': obj['name'], 'type': 'Network'}
#                         session_objects['networks'].append(new_obj)
#                         existing_objects['networks'].append(new_obj)
#                     elif result['action'] == 'updated':
#                         results['network_objects_updated'] += 1
#                         # Also add updated objects to cache for dependency resolution
#                         new_obj = {'id': result['object_id'], 'name': obj['name'], 'type': 'Network'}
#                         session_objects['networks'].append(new_obj)
#                         existing_objects['networks'].append(new_obj)
#                 else:
#                     if 'missing dependencies' in result.get('message', '').lower():
#                         results['network_objects_skipped'] += 1
#                         results['warnings'] += 1
#                     else:
#                         results['errors'] += 1
#                         self.log(f"Network object error: {result['message']}", "ERROR")
        
#         # Create service objects
#         if 'service_objects' in api_calls:
#             self.log("Creating/overwriting service objects...")
#             for obj in api_calls['service_objects']['data']:
#                 result = self.create_object_with_validation('service', obj, existing_objects)
                
#                 if result['success']:
#                     if result['action'] == 'created':
#                         results['service_objects_created'] += 1
#                         # Add to session tracking
#                         new_obj = {'id': result['object_id'], 'name': obj['name'], 'type': 'Service'}
#                         session_objects['protocol_ports'].append(new_obj)
#                         existing_objects['protocol_ports'].append(new_obj)
#                     elif result['action'] == 'overwritten':
#                         results['service_objects_overwritten'] += 1
#                         # Also add overwritten objects to cache for dependency resolution
#                         new_obj = {'id': result['object_id'], 'name': obj['name'], 'type': 'Service'}
#                         session_objects['protocol_ports'].append(new_obj)
#                         existing_objects['protocol_ports'].append(new_obj)
#                 else:
#                     if 'missing dependencies' in result.get('message', '').lower():
#                         results['service_objects_skipped'] += 1
#                         results['warnings'] += 1
#                     else:
#                         results['errors'] += 1
#                         self.log(f"Service object error: {result['message']}", "ERROR")
        
#         # Create object groups (after individual objects are created)
#         if 'object_groups' in api_calls:
#             self.log("Creating/overwriting network object groups...")
            
#             # Sort groups by dependency depth (groups with fewer dependencies first)
#             groups_to_create = api_calls['object_groups']['data'].copy()
            
#             # Use topological sorting if available
#             try:
#                 groups_to_create = self.topological_sort(groups_to_create)
#                 self.log(f"Applied topological sorting to {len(groups_to_create)} object groups")
#             except Exception as e:
#                 self.log(f"Topological sort failed, using simple sorting: {e}", "WARN")
#                 groups_to_create.sort(key=lambda x: self.count_dependencies(x))
            
#             created_groups = []
#             max_passes = 10  # Increased from 3 to handle complex dependency chains
            
#             for pass_num in range(max_passes):
#                 if not groups_to_create:
#                     break
                    
#                 self.log(f"Object group creation pass {pass_num + 1}")
#                 groups_created_this_pass = []
#                 remaining_groups = []
                
#                 for obj in groups_to_create:
#                     result = self.create_object_with_validation('object_group', obj, existing_objects)
                    
#                     if result['success']:
#                         if result['action'] == 'created':
#                             results['object_groups_created'] += 1
#                         elif result['action'] == 'overwritten':
#                             results['object_groups_overwritten'] += 1
                        
#                         # Add to session tracking
#                         new_obj = {'id': result['object_id'], 'name': obj['name'], 'type': 'NetworkGroup'}
#                         session_objects['network_groups'].append(new_obj)
#                         existing_objects['network_groups'].append(new_obj)
#                         groups_created_this_pass.append(obj['name'])
                        
#                     elif 'missing dependencies' in result.get('message', '').lower():
#                         # Dependencies missing - try again in next pass
#                         remaining_groups.append(obj)
                        
#                     else:
#                         results['errors'] += 1
#                         self.log(f"Object group error: {result['message']}", "ERROR")
                
#                 if groups_created_this_pass:
#                     self.log(f"Created {len(groups_created_this_pass)} groups in pass {pass_num + 1}")
#                     groups_to_create = remaining_groups
#                 else:
#                     # No progress made in this pass
#                     self.log(f"No groups created in pass {pass_num + 1}, stopping dependency resolution")
#                     break
            
#             # Report on any remaining groups that couldn't be created
#             if groups_to_create:
#                 self.log(f"Could not create {len(groups_to_create)} groups due to unresolved dependencies:", "WARN")
#                 for group in groups_to_create:
#                     self.log(f"  - {group.get('name', 'unknown')}", "WARN")
#                     results['object_groups_skipped'] += len(groups_to_create)
#                     results['warnings'] += len(groups_to_create)
        
#         # Create service groups (protocol port groups) 
#         if 'service_groups' in api_calls:
#             self.log("Creating/overwriting service groups...")
            
#             # Sort groups by dependency depth (groups with fewer dependencies first)
#             groups_to_create = api_calls['service_groups']['data'].copy()
            
#             # Use topological sorting if available
#             try:
#                 groups_to_create = self.topological_sort(groups_to_create)
#                 self.log(f"Applied topological sorting to {len(groups_to_create)} service groups")
#             except Exception as e:
#                 self.log(f"Topological sort failed, using simple sorting: {e}", "WARN")
#                 groups_to_create.sort(key=lambda x: self.count_dependencies(x))
            
#             created_groups = []
#             max_passes = 10  # Same as network groups
            
#             for pass_num in range(max_passes):
#                 if not groups_to_create:
#                     break
                    
#                 self.log(f"Service group creation pass {pass_num + 1}")
#                 groups_created_this_pass = []
#                 remaining_groups = []
                
#                 for obj in groups_to_create:
#                     result = self.create_object_with_validation('service_group', obj, existing_objects)
                    
#                     if result['success']:
#                         if result['action'] == 'created':
#                             results['service_groups_created'] = results.get('service_groups_created', 0) + 1
#                         elif result['action'] == 'overwritten':
#                             results['service_groups_overwritten'] = results.get('service_groups_overwritten', 0) + 1
                        
#                         # Add to session tracking (use protocol_port_groups if available)
#                         new_obj = {'id': result['object_id'], 'name': obj['name'], 'type': 'ProtocolPortGroup'}
#                         if 'protocol_port_groups' not in session_objects:
#                             session_objects['protocol_port_groups'] = []
#                         session_objects['protocol_port_groups'].append(new_obj)
                        
#                         if 'protocol_port_groups' not in existing_objects:
#                             existing_objects['protocol_port_groups'] = []
#                         existing_objects['protocol_port_groups'].append(new_obj)
#                         groups_created_this_pass.append(obj['name'])
                        
#                     elif 'missing dependencies' in result.get('message', '').lower():
#                         # Dependencies missing - try again in next pass
#                         remaining_groups.append(obj)
                        
#                     else:
#                         results['errors'] += 1
#                         self.log(f"Service group error: {result['message']}", "ERROR")
                
#                 if groups_created_this_pass:
#                     self.log(f"Created {len(groups_created_this_pass)} service groups in pass {pass_num + 1}")
#                     groups_to_create = remaining_groups
#                 else:
#                     # No progress made in this pass
#                     self.log(f"No service groups created in pass {pass_num + 1}, stopping dependency resolution")
#                     break
            
#             # Report on any remaining groups that couldn't be created
#             if groups_to_create:
#                 self.log(f"Could not create {len(groups_to_create)} service groups due to unresolved dependencies:", "WARN")
#                 for group in groups_to_create:
#                     self.log(f"  - {group.get('name', 'unknown')}", "WARN")
#                     results['service_groups_skipped'] = results.get('service_groups_skipped', 0) + len(groups_to_create)
#                     results['warnings'] += len(groups_to_create)
        
#         # Provide summary of created objects for reference
#         self.log("\nSession Summary:")
#         self.log(f"Created {len(session_objects['hosts'])} new host objects")
#         self.log(f"Created {len(session_objects['networks'])} new network objects")
#         self.log(f"Created {len(session_objects['protocol_ports'])} new service objects")
#         self.log(f"Created {len(session_objects['network_groups'])} new object groups")
        
#         # Log operation summaries for each object type
#         if results.get('host_objects_created', 0) > 0 or results.get('host_objects_failed', 0) > 0:
#             self.log_operation_summary(
#                 "Host Objects", 
#                 results.get('host_objects_created', 0) + results.get('host_objects_updated', 0),
#                 results.get('host_objects_failed', 0),
#                 {
#                     'created': results.get('host_objects_created', 0),
#                     'updated': results.get('host_objects_updated', 0),
#                     'skipped': results.get('host_objects_skipped', 0)
#                 }
#             )
        
#         if results.get('network_objects_created', 0) > 0 or results.get('network_objects_failed', 0) > 0:
#             self.log_operation_summary(
#                 "Network Objects",
#                 results.get('network_objects_created', 0) + results.get('network_objects_updated', 0),
#                 results.get('network_objects_failed', 0),
#                 {
#                     'created': results.get('network_objects_created', 0),
#                     'updated': results.get('network_objects_updated', 0),
#                     'skipped': results.get('network_objects_skipped', 0)
#                 }
#             )
        
#         if results.get('service_objects_created', 0) > 0 or results.get('service_objects_failed', 0) > 0:
#             self.log_operation_summary(
#                 "Service Objects",
#                 results.get('service_objects_created', 0) + results.get('service_objects_overwritten', 0),
#                 results.get('service_objects_failed', 0),
#                 {
#                     'created': results.get('service_objects_created', 0),
#                     'overwritten': results.get('service_objects_overwritten', 0),
#                     'skipped': results.get('service_objects_skipped', 0)
#                 }
#             )
        
#         if results.get('object_groups_created', 0) > 0 or results.get('object_groups_failed', 0) > 0:
#             self.log_operation_summary(
#                 "Object Groups",
#                 results.get('object_groups_created', 0) + results.get('object_groups_overwritten', 0),
#                 results.get('object_groups_failed', 0),
#                 {
#                     'created': results.get('object_groups_created', 0),
#                     'overwritten': results.get('object_groups_overwritten', 0),
#                     'skipped': results.get('object_groups_skipped', 0)
#                 }
#             )
        
#         # Create migration checkpoint for rollback capabilities
#         migration_summary = self.get_migration_summary()
#         if migration_summary['total_operations'] > 0:
#             checkpoint_file = self.create_migration_checkpoint()
#             if checkpoint_file:
#                 results['checkpoint_file'] = checkpoint_file
#                 self.log(f"Migration checkpoint created: {checkpoint_file}")
#                 self.last_checkpoint_file = checkpoint_file
            
#             results['session_summary'] = migration_summary
        
#         # Log caching performance
#         cache_stats = self.get_cache_stats()
#         self.log(f"Caching Performance:", "INFO")
#         self.log(f"  - Lookup cache hits: {cache_stats.get('lookup_cache_size', 0)}", "INFO")
#         if 'total_cached_objects' in cache_stats:
#             self.log(f"  - Total objects cached: {cache_stats['total_cached_objects']}", "INFO")
#         self.log(f"  - Cache age: {cache_stats.get('cache_age_seconds', 0):.1f}s", "INFO")
        
#         # Log comprehensive session summary
#         self.log_session_summary()
        
#         return results
    
#     def get_existing_objects(self) -> Dict[str, List]:
#         """Get existing objects from FMC for validation with proper pagination (cached)"""
#         # Return cached result if valid
#         if self._is_cache_valid() and self._object_cache['existing_objects'] is not None:
#             self.log("Using cached existing objects", "DEBUG")
#             return self._object_cache['existing_objects']
        
#         if not self.authenticate():
#             return {}
        
#         self.log("Fetching existing objects from FMC (cache miss)", "DEBUG")
#         objects = {}
        
#         # Helper function to get all pages of results
#         def get_all_pages(endpoint: str, object_type: str) -> List[Dict]:
#             all_items = []
#             offset = 0
#             limit = 1000  # FMC default page size
            
#             while True:
#                 try:
#                     params = {'offset': offset, 'limit': limit}
#                     result = self.make_api_call('GET', endpoint, params=params)
                    
#                     if not result or 'items' not in result:
#                         break
                        
#                     items = result['items']
#                     all_items.extend(items)
                    
#                     # Check if we've retrieved all items
#                     paging = result.get('paging', {})
#                     total_items = paging.get('count', len(items))
                    
#                     self.log(f"Retrieved {len(items)} {object_type} objects (offset: {offset}, total so far: {len(all_items)})")
                    
#                     if len(items) < limit or len(all_items) >= total_items:
#                         break
                        
#                     offset += limit
                    
#                 except Exception as e:
#                     self.log(f"Error retrieving {object_type} objects at offset {offset}: {e}", "ERROR")
#                     break
                    
#             return all_items
        
#         # Get host objects with pagination
#         try:
#             hosts = get_all_pages('/api/fmc_config/v1/domain/{domain_uuid}/object/hosts', 'host')
#             objects['hosts'] = hosts
#             self.log(f"Found {len(hosts)} existing host objects total")
#         except Exception as e:
#             self.log(f"Failed to retrieve host objects: {e}", "ERROR")
#             objects['hosts'] = []
        
#         # Get network objects with pagination
#         try:
#             networks = get_all_pages('/api/fmc_config/v1/domain/{domain_uuid}/object/networks', 'network')
#             objects['networks'] = networks
#             self.log(f"Found {len(networks)} existing network objects total")
#         except Exception as e:
#             self.log(f"Failed to retrieve network objects: {e}", "ERROR")
#             objects['networks'] = []
        
#         # Get protocol port objects with pagination
#         try:
#             protocols = get_all_pages('/api/fmc_config/v1/domain/{domain_uuid}/object/protocolportobjects', 'protocol port')
#             objects['protocol_ports'] = protocols
#             self.log(f"Found {len(protocols)} existing protocol port objects total")
#         except Exception as e:
#             self.log(f"Failed to retrieve protocol port objects: {e}", "ERROR")
#             objects['protocol_ports'] = []
        
#         # Get network groups with pagination
#         try:
#             groups = get_all_pages('/api/fmc_config/v1/domain/{domain_uuid}/object/networkgroups', 'network group')
#             objects['network_groups'] = groups
#             self.log(f"Found {len(groups)} existing network groups total")
#         except Exception as e:
#             self.log(f"Failed to retrieve network groups: {e}", "ERROR")
#             objects['network_groups'] = []
        
#         # Cache the results
#         self._object_cache['existing_objects'] = objects
#         self._object_cache['cache_timestamp'] = time.time()
#         self.log(f"Cached existing objects: {sum(len(v) for v in objects.values())} total objects", "DEBUG")
        
#         return objects
    
#     def validate_migration(self, config_file: str, offline_mode: bool = False) -> Dict[str, Any]:
#         """Validate the migration configuration against existing FMC objects with comprehensive checks"""
#         self.log("Validating migration configuration...")
        
#         try:
#             with open(config_file, 'r') as f:
#                 config = json.load(f)
#         except FileNotFoundError:
#             return {'error': f"Configuration file '{config_file}' not found"}
#         except json.JSONDecodeError as e:
#             return {'error': f"Invalid JSON in configuration file: {e}"}
        
#         # Get existing objects with pagination (skip if offline mode)
#         if offline_mode:
#             self.log("Running offline validation (skipping FMC connectivity check)...")
#             existing_objects = {}
#         else:
#             self.log("Retrieving existing objects for validation...")
#             existing_objects = self.get_existing_objects()
        
#         validation_results = {
#             'conflicts': [],
#             'safe_to_migrate': [],
#             'dependency_issues': [],
#             'warnings': [],
#             'total_objects_to_create': 0,
#             'pagination_check': {},
#             'summary': {}
#         }
        
#         api_calls = config.get('api_calls', {})
        
#         # Check pagination completeness (only in online mode)
#         if not offline_mode:
#             for obj_type, obj_list in existing_objects.items():
#                 validation_results['pagination_check'][obj_type] = {
#                     'count': len(obj_list),
#                     'appears_complete': True,
#                     'note': f"Retrieved {len(obj_list)} {obj_type} objects"
#                 }
                
#                 # Basic pagination check - if we have exactly 25 or multiples of common page sizes,
#                 # warn that there might be more
#                 count = len(obj_list)
#                 if count > 0 and (count % 25 == 0 or count % 50 == 0 or count % 100 == 0):
#                     validation_results['pagination_check'][obj_type]['appears_complete'] = False
#                     validation_results['pagination_check'][obj_type]['note'] += " (WARNING: Count suggests possible pagination truncation)"
#                     validation_results['warnings'].append(f"Possible pagination issue with {obj_type}: retrieved exactly {count} objects")
#         else:
#             validation_results['pagination_check'] = {'offline_mode': 'Skipped FMC connectivity check in offline validation'}
        
#         # Track objects that will be created for dependency validation
#         objects_to_create = {
#             'hosts': [],
#             'networks': [],
#             'services': [],
#             'groups': []
#         }
        
#         # Validate host objects for conflicts
#         if 'host_objects' in api_calls:
#             existing_names = {obj['name'].lower(): obj for obj in existing_objects.get('hosts', [])}
#             config_names = set()  # Track duplicate names within config
            
#             for obj in api_calls['host_objects']['data']:
#                 obj_name = obj.get('name', '')
#                 validation_results['total_objects_to_create'] += 1
#                 objects_to_create['hosts'].append(obj)
                
#                 # Check for duplicates within the config itself
#                 if obj_name.lower() in config_names:
#                     validation_results['conflicts'].append({
#                         'type': 'host_object',
#                         'name': obj_name,
#                         'reason': 'Duplicate name found within migration config',
#                         'conflict_type': 'internal'
#                     })
#                 else:
#                     config_names.add(obj_name.lower())
                
#                 # Check against existing FMC objects (only in online mode)
#                 if not offline_mode:
#                     # Check for exact name match
#                     if obj_name in existing_names:
#                         validation_results['conflicts'].append({
#                             'type': 'host_object',
#                             'name': obj_name,
#                             'reason': 'Object with exact name already exists',
#                             'existing_id': existing_names[obj_name].get('id', 'unknown'),
#                             'conflict_type': 'fmc_existing'
#                         })
#                     # Check for case-insensitive name match
#                     elif obj_name.lower() in existing_names:
#                         validation_results['conflicts'].append({
#                             'type': 'host_object',
#                             'name': obj_name,
#                             'reason': 'Object with similar name exists (case difference)',
#                             'existing_name': existing_names[obj_name.lower()]['name'],
#                             'existing_id': existing_names[obj_name.lower()].get('id', 'unknown'),
#                             'conflict_type': 'fmc_existing'
#                         })
#                     else:
#                         validation_results['safe_to_migrate'].append({
#                             'type': 'host_object',
#                             'name': obj_name
#                         })
#                 else:
#                     # In offline mode, we can only check basic format
#                     if obj_name.strip() == '':
#                         validation_results['warnings'].append(f"Host object has empty name")
#                     elif obj_name not in config_names:
#                         validation_results['safe_to_migrate'].append({
#                             'type': 'host_object',
#                             'name': obj_name,
#                             'note': 'Offline validation - conflicts with existing FMC objects not checked'
#                         })
        
#         # Validate network objects for conflicts
#         if 'network_objects' in api_calls:
#             existing_names = {obj['name'].lower(): obj for obj in existing_objects.get('networks', [])}
            
#             for obj in api_calls['network_objects']['data']:
#                 obj_name = obj.get('name', '')
#                 validation_results['total_objects_to_create'] += 1
#                 objects_to_create['networks'].append(obj)
                
#                 if obj_name.lower() in existing_names:
#                     validation_results['conflicts'].append({
#                         'type': 'network_object',
#                         'name': obj_name,
#                         'reason': 'Object with same name already exists',
#                         'existing_id': existing_names[obj_name.lower()].get('id', 'unknown')
#                     })
#                 else:
#                     validation_results['safe_to_migrate'].append({
#                         'type': 'network_object',
#                         'name': obj_name
#                     })
        
#         # Validate service objects for conflicts
#         if 'service_objects' in api_calls:
#             existing_names = {obj['name'].lower(): obj for obj in existing_objects.get('protocol_ports', [])}
            
#             for obj in api_calls['service_objects']['data']:
#                 obj_name = obj.get('name', '')
#                 validation_results['total_objects_to_create'] += 1
#                 objects_to_create['services'].append(obj)
                
#                 if obj_name.lower() in existing_names:
#                     validation_results['conflicts'].append({
#                         'type': 'service_object',
#                         'name': obj_name,
#                         'reason': 'Object with same name already exists',
#                         'existing_id': existing_names[obj_name.lower()].get('id', 'unknown')
#                     })
#                 else:
#                     validation_results['safe_to_migrate'].append({
#                         'type': 'service_object',
#                         'name': obj_name
#                     })
        
#         # Validate object groups for conflicts and dependencies
#         if 'object_groups' in api_calls:
#             existing_names = {obj['name'].lower(): obj for obj in existing_objects.get('network_groups', [])}
            
#             for obj in api_calls['object_groups']['data']:
#                 obj_name = obj.get('name', '')
#                 validation_results['total_objects_to_create'] += 1
#                 objects_to_create['groups'].append(obj)
                
#                 # Check for name conflicts
#                 if obj_name.lower() in existing_names:
#                     validation_results['conflicts'].append({
#                         'type': 'object_group',
#                         'name': obj_name,
#                         'reason': 'Object with same name already exists',
#                         'existing_id': existing_names[obj_name.lower()].get('id', 'unknown')
#                     })
#                 else:
#                     validation_results['safe_to_migrate'].append({
#                         'type': 'object_group',
#                         'name': obj_name
#                     })
                
#                 # Validate dependencies - check both existing objects and objects to be created
#                 dependency_validation = self.validate_object_dependencies(obj, existing_objects, objects_to_create)
#                 if not dependency_validation['valid']:
#                     validation_results['dependency_issues'].append({
#                         'group_name': obj_name,
#                         'missing_dependencies': dependency_validation['missing_dependencies'],
#                         'can_resolve_in_session': self._check_if_dependencies_in_session(
#                             dependency_validation['missing_dependencies'], 
#                             objects_to_create
#                         )
#                     })
                
#                 # Add warnings
#                 for warning in dependency_validation.get('warnings', []):
#                     validation_results['warnings'].append(f"Group '{obj_name}': {warning}")
        
#         # Generate summary
#         validation_results['summary'] = {
#             'total_conflicts': len(validation_results['conflicts']),
#             'total_safe': len(validation_results['safe_to_migrate']),
#             'total_dependency_issues': len(validation_results['dependency_issues']),
#             'total_warnings': len(validation_results['warnings']),
#             'overwrite_required': len(validation_results['conflicts']) > 0,
#             'dependency_resolution_needed': len(validation_results['dependency_issues']) > 0,
#             'validation_mode': 'offline' if offline_mode else 'online',
#             'fmc_connectivity_check': 'skipped' if offline_mode else 'performed'
#         }
        
#         return validation_results
    
#     def _find_in_objects_to_create(self, ref_name: str, ref_type: str, objects_to_create: Dict) -> Optional[Dict]:
#         """Find an object reference in the objects that will be created during this migration"""
#         # NetworkObject references could be either Host or Network objects
#         # Search both categories if it's a NetworkObject reference
#         if ref_type.lower() == 'networkobject':
#             search_categories = ['hosts', 'networks']
#         else:
#             # Map other reference types to specific categories
#             type_mapping = {
#                 'host': 'hosts',
#                 'network': 'networks', 
#                 'networkgroupobject': 'groups',
#                 'service': 'services',
#                 'servicegroup': 'groups'
#             }
#             search_categories = [type_mapping.get(ref_type.lower(), 'hosts')]
        
#         # Search in the appropriate categories
#         for category in search_categories:
#             for obj in objects_to_create.get(category, []):
#                 if obj.get('name', '').lower() == ref_name.lower():
#                     # Return object with a placeholder ID since it doesn't exist yet
#                     return {
#                         'id': f"placeholder-{ref_name.lower()}",
#                         'name': obj.get('name'),
#                         'type': obj.get('type', 'Host'),
#                         'in_migration': True  # Flag to indicate this will be created
#                     }
        
#         return None
    
#     def _check_if_dependencies_in_session(self, missing_deps: List[Dict], objects_to_create: Dict) -> Dict:
#         """Check if missing dependencies will be created in this session"""
#         resolution_status = {}
        
#         for dep in missing_deps:
#             dep_name = dep.get('name', '')
#             dep_type = dep.get('type', 'Host').lower()
            
#             found_in_session = False
            
#             # Map FMC types to our object categories
#             if dep_type in ['host']:
#                 found_in_session = any(obj.get('name') == dep_name for obj in objects_to_create['hosts'])
#             elif dep_type in ['network']:
#                 found_in_session = any(obj.get('name') == dep_name for obj in objects_to_create['networks'])
#             elif dep_type in ['service', 'protocolport']:
#                 found_in_session = any(obj.get('name') == dep_name for obj in objects_to_create['services'])
#             elif dep_type in ['networkgroup', 'group']:
#                 found_in_session = any(obj.get('name') == dep_name for obj in objects_to_create['groups'])
            
#             resolution_status[dep_name] = {
#                 'will_be_created': found_in_session,
#                 'type': dep_type
#             }
        
#         return resolution_status
    
#     def _find_object_in_cache_only(self, object_type: str, name: str, existing_objects: Dict) -> Optional[Dict]:
#         """Find an existing object by name using cached data only (no live API calls)"""
#         type_mapping = {
#             'host': 'hosts',
#             'network': 'networks',
#             'range': 'ranges',
#             'fqdn': 'fqdns',
#             'service': 'protocol_ports', 
#             'object_group': 'network_groups',
#             'service_group': 'protocol_port_groups'
#         }
        
#         objects_list = existing_objects.get(type_mapping.get(object_type), [])
        
#         # First try exact match
#         for obj in objects_list:
#             if obj.get('name') == name:
#                 return obj
        
#         # If no exact match, try case-insensitive match
#         name_lower = name.lower()
#         for obj in objects_list:
#             if obj.get('name', '').lower() == name_lower:
#                 self.log(f"Found case-insensitive match for '{name}' -> '{obj.get('name')}'", "DEBUG")
#                 return obj
        
#         # Log that object was not found in cache with debugging info
#         total_objects = len(objects_list)
#         sample_names = [obj.get('name', 'NO_NAME') for obj in objects_list[:5]]
#         self.log(f"Object '{name}' of type '{object_type}' not found in cache (skipping live lookup during bulk operations)", "DEBUG")
#         self.log(f"Cache has {total_objects} {type_mapping.get(object_type, 'unknown')} objects. Sample names: {sample_names}", "DEBUG")
        
#         # Check for case-insensitive matches for debugging
#         case_matches = [obj.get('name') for obj in objects_list if obj.get('name', '').lower() == name.lower()]
#         if case_matches:
#             self.log(f"Found case-insensitive matches for '{name}': {case_matches}", "WARN")
        
#         return None

#     def find_existing_object_by_name(self, object_type: str, name: str, existing_objects: Dict) -> Optional[Dict]:
#         """Find an existing object by name and return its details including UUID"""
#         type_mapping = {
#             'host': 'hosts',
#             'network': 'networks',
#             'range': 'ranges',
#             'fqdn': 'fqdns',
#             'service': 'protocol_ports', 
#             'object_group': 'network_groups',
#             'service_group': 'protocol_port_groups'
#         }
        
#         objects_list = existing_objects.get(type_mapping.get(object_type), [])
        
#         # First try exact match
#         for obj in objects_list:
#             if obj.get('name') == name:
#                 return obj
        
#         # If no exact match, try case-insensitive match
#         name_lower = name.lower()
#         for obj in objects_list:
#             if obj.get('name', '').lower() == name_lower:
#                 self.log(f"Found case-insensitive match for '{name}' -> '{obj.get('name')}'", "DEBUG")
#                 return obj
        
#         # If not found in cache, try live lookup from FMC API
#         self.log(f"Object '{name}' not found in cache, attempting live lookup...", "DEBUG")
#         live_obj = self._live_lookup_object_by_name(object_type, name)
#         if live_obj:
#             # Add to cache for future lookups
#             objects_list.append(live_obj)
#             return live_obj
        
#         # Log that object was not found for debugging
#         self.log(f"Object '{name}' of type '{object_type}' not found in existing objects cache or live lookup", "DEBUG")
#         return None
    
#     def _live_lookup_object_by_name(self, object_type: str, name: str) -> Optional[Dict]:
#         """Perform live API lookup for object by name (cached) with robust FMC filter handling"""
#         # Check cache first
#         cached_result = self._get_cached_lookup(object_type, name)
#         if cached_result is not None:
#             return cached_result

#         endpoint_mapping = {
#             'host': '/api/fmc_config/v1/domain/{domain_uuid}/object/hosts',
#             'network': '/api/fmc_config/v1/domain/{domain_uuid}/object/networks',
#             'range': '/api/fmc_config/v1/domain/{domain_uuid}/object/ranges',
#             'fqdn': '/api/fmc_config/v1/domain/{domain_uuid}/object/fqdns',
#             'service': '/api/fmc_config/v1/domain/{domain_uuid}/object/protocolportobjects',
#             'object_group': '/api/fmc_config/v1/domain/{domain_uuid}/object/networkgroups',
#             'service_group': '/api/fmc_config/v1/domain/{domain_uuid}/object/portobjectgroups'
#         }

#         endpoint = endpoint_mapping.get(object_type)
#         if not endpoint:
#             self._cache_object_lookup(object_type, name, None)
#             return None

#         try:
#             self.log(f"Performing live lookup for {object_type}: {name}", "DEBUG")

#             found_object = None
            
#             # Try multiple filter approaches that FMC might support
#             # Note: Some endpoints (like networks) don't support name-based filtering
#             if object_type == 'network':
#                 # Networks endpoint doesn't support nameOrValue or name query parameters
#                 filter_attempts = [None]  # Go directly to full lookup for networks
#             else:
#                 filter_attempts = [
#                     {'nameOrValue': name},  # FMC often uses nameOrValue
#                     {'name': name},         # Sometimes just name works
#                     None                    # Fallback to no filter
#                 ]
            
#             for i, filter_params in enumerate(filter_attempts):
#                 try:
#                     if filter_params:
#                         # URL encode the name to handle special characters
#                         try:
#                             import urllib.parse
#                         except ImportError:
#                             # Fallback for older Python versions
#                             import urllib
#                             urllib.parse = urllib
                        
#                         if 'nameOrValue' in filter_params:
#                             encoded_name = urllib.parse.quote(name, safe='')
#                             params = {'nameOrValue': encoded_name, 'limit': 25}
#                         else:
#                             encoded_name = urllib.parse.quote(name, safe='')
#                             params = {'name': encoded_name, 'limit': 25}
                        
#                         self.log(f"Attempting filter approach {i+1}: {params}", "DEBUG")
#                         result = self.make_api_call('GET', endpoint, params=params)
#                     else:
#                         # Full lookup without filter as final fallback
#                         self.log(f"Using full lookup (no filter) for {object_type}: {name}", "DEBUG")
#                         result = self.make_api_call('GET', endpoint, params={'limit': 1000})

#                     if result and 'items' in result:
#                         # Look for exact match first
#                         for item in result['items']:
#                             if item.get('name') == name:
#                                 found_object = item
#                                 self.log(f"Found exact match for {object_type}: {name} using approach {i+1}", "DEBUG")
#                                 break

#                         if found_object:
#                             break  # Found it, no need to try other approaches

#                         # Try case-insensitive match if no exact match
#                         name_lower = name.lower()
#                         for item in result['items']:
#                             if item.get('name', '').lower() == name_lower:
#                                 found_object = item
#                                 self.log(f"Found case-insensitive match: {item.get('name')} using approach {i+1}", "DEBUG")
#                                 break

#                         if found_object:
#                             break  # Found it, no need to try other approaches

#                         # Cache all items from this search for future lookups (only if reasonable number)
#                         if len(result['items']) < 500:  # Don't cache massive results
#                             for item in result['items']:
#                                 item_name = item.get('name', '')
#                                 if item_name:  # Only cache items with valid names
#                                     self._cache_object_lookup(object_type, item_name, item)

#                 except Exception as filter_error:
#                     self.log(f"Filter approach {i+1} failed for {object_type} '{name}': {filter_error}", "DEBUG")
#                     continue  # Try next approach

#             # Final cache of the result (even if None)
#             self._cache_object_lookup(object_type, name, found_object)
            
#             if found_object:
#                 self.log(f"✅ Live lookup successful for {object_type}: {name} (ID: {found_object.get('id', 'unknown')[:12]}...)", "DEBUG")
#             else:
#                 self.log(f"❌ Live lookup failed to find {object_type}: {name} - object may not exist", "DEBUG")
            
#             return found_object

#         except Exception as e:
#             self.log(f"Live lookup completely failed for {object_type} '{name}': {e}", "WARN")
#             # Cache the failure
#             self._cache_object_lookup(object_type, name, None)
#             return None
    
#     def _comprehensive_object_search(self, object_type: str, name: str) -> Optional[Dict]:
#         """Comprehensive search for phantom objects reported as existing but not found via normal lookup"""
#         self.log(f"🔍 Comprehensive search for phantom {object_type}: {name}", "DEBUG")
        
#         endpoint_mapping = {
#             'host': '/api/fmc_config/v1/domain/{domain_uuid}/object/hosts',
#             'network': '/api/fmc_config/v1/domain/{domain_uuid}/object/networks',
#             'range': '/api/fmc_config/v1/domain/{domain_uuid}/object/ranges',
#             'fqdn': '/api/fmc_config/v1/domain/{domain_uuid}/object/fqdns',
#             'service': '/api/fmc_config/v1/domain/{domain_uuid}/object/protocolportobjects',
#             'object_group': '/api/fmc_config/v1/domain/{domain_uuid}/object/networkgroups',
#             'service_group': '/api/fmc_config/v1/domain/{domain_uuid}/object/portobjectgroups'
#         }
        
#         endpoint = endpoint_mapping.get(object_type)
#         if not endpoint:
#             return None
        
#         try:
#             # Strategy 1: Full scan with pagination to catch all objects
#             self.log(f"Strategy 1: Full paginated scan for {object_type}: {name}", "DEBUG")
#             offset = 0
#             limit = 100
#             while offset < 5000:  # Reasonable upper limit
#                 try:
#                     params = {'offset': offset, 'limit': limit}
#                     result = self.make_api_call('GET', endpoint, params=params)
                    
#                     if not result or 'items' not in result:
#                         break
                    
#                     # Check all items in this page
#                     for item in result['items']:
#                         if item.get('name') == name or item.get('name', '').lower() == name.lower():
#                             self.log(f"✅ Found phantom object via full scan: {name} (ID: {item.get('id', 'unknown')[:12]}...)", "DEBUG")
#                             return item
                    
#                     # Check if we've reached the end
#                     if len(result['items']) < limit:
#                         break
                    
#                     offset += limit
                    
#                 except Exception as page_error:
#                     self.log(f"Full scan page failed at offset {offset}: {page_error}", "DEBUG")
#                     break
            
#             # Strategy 2: Try different case variations
#             self.log(f"Strategy 2: Case variation search for {object_type}: {name}", "DEBUG")
#             name_variations = [
#                 name.upper(),
#                 name.lower(),
#                 name.title(),
#                 name.capitalize()
#             ]
            
#             for variation in name_variations:
#                 if variation == name:  # Skip the original name
#                     continue
#                 try:
#                     params = {'limit': 50}
#                     result = self.make_api_call('GET', endpoint, params=params)
                    
#                     if result and 'items' in result:
#                         for item in result['items']:
#                             if item.get('name') == variation:
#                                 self.log(f"✅ Found phantom object via case variation '{variation}': (ID: {item.get('id', 'unknown')[:12]}...)", "DEBUG")
#                                 return item
#                 except Exception as case_error:
#                     self.log(f"Case variation search failed for '{variation}': {case_error}", "DEBUG")
#                     continue
            
#             # Strategy 3: Search by partial name match
#             self.log(f"Strategy 3: Partial name search for {object_type}: {name}", "DEBUG")
#             try:
#                 params = {'limit': 200}
#                 result = self.make_api_call('GET', endpoint, params=params)
                
#                 if result and 'items' in result:
#                     # Look for partial matches
#                     name_lower = name.lower()
#                     for item in result['items']:
#                         item_name_lower = item.get('name', '').lower()
#                         if name_lower in item_name_lower or item_name_lower in name_lower:
#                             # If it's a very close match, consider it
#                             if abs(len(item_name_lower) - len(name_lower)) <= 2:
#                                 self.log(f"✅ Found phantom object via partial match '{item.get('name')}': (ID: {item.get('id', 'unknown')[:12]}...)", "DEBUG")
#                                 return item
                                
#             except Exception as partial_error:
#                 self.log(f"Partial name search failed: {partial_error}", "DEBUG")
            
#             self.log(f"❌ Comprehensive search exhausted - phantom object {object_type}: {name} remains unfindable", "DEBUG")
#             return None
            
#         except Exception as e:
#             self.log(f"Comprehensive search completely failed for {object_type} '{name}': {e}", "WARN")
#             return None
    
#     def delete_existing_object(self, object_type: str, object_uuid: str) -> bool:
#         """Delete an existing object by UUID"""
#         endpoint_mapping = {
#             'service': f'/api/fmc_config/v1/domain/{{domain_uuid}}/object/protocolportobjects/{object_uuid}',
#             'object_group': f'/api/fmc_config/v1/domain/{{domain_uuid}}/object/networkgroups/{object_uuid}'
#         }
        
#         if object_type not in endpoint_mapping:
#             self.log(f"Delete not supported for object type: {object_type}", "WARN")
#             return False
            
#         endpoint = endpoint_mapping[object_type]
#         result = self.make_api_call('DELETE', endpoint)
        
#         if result is not None:  # DELETE often returns empty response on success
#             self.log(f"Successfully deleted existing {object_type} object: {object_uuid}")
#             return True
#         else:
#             self.log(f"Failed to delete existing {object_type} object: {object_uuid}", "ERROR")
#             return False
    
#     def update_existing_object(self, object_type: str, object_uuid: str, new_data: Dict) -> bool:
#         """Update an existing object with new data"""
#         endpoint_mapping = {
#             'host': f'/api/fmc_config/v1/domain/{{domain_uuid}}/object/hosts/{object_uuid}',
#             'network': f'/api/fmc_config/v1/domain/{{domain_uuid}}/object/networks/{object_uuid}',
#             'service': f'/api/fmc_config/v1/domain/{{domain_uuid}}/object/protocolportobjects/{object_uuid}'
#         }
        
#         if object_type not in endpoint_mapping:
#             self.log(f"Update not supported for object type: {object_type}", "WARN")
#             return False
            
#         # Add the UUID to the data for the update
#         update_data = new_data.copy()
#         update_data['id'] = object_uuid
        
#         endpoint = endpoint_mapping[object_type]
#         result = self.make_api_call('PUT', endpoint, update_data)
        
#         if result:
#             self.log(f"Successfully updated existing {object_type} object: {new_data.get('name', object_uuid)}")
#             return True
#         else:
#             self.log(f"Failed to update existing {object_type} object: {new_data.get('name', object_uuid)}", "ERROR")
#             return False

#     def build_dependency_graph(self, objects: List[Dict]) -> tuple:
#         """Build a dependency graph for proper ordering"""
#         graph = {}
#         in_degree = {}
        
#         # Initialize graph
#         for obj in objects:
#             obj_name = obj['name']
#             graph[obj_name] = []
#             in_degree[obj_name] = 0
        
#         # Build edges (dependencies)
#         for obj in objects:
#             obj_name = obj['name']
#             if 'objects' in obj:
#                 for ref in obj['objects']:
#                     dep_name = ref.get('name')
#                     if dep_name and dep_name in graph:
#                         graph[dep_name].append(obj_name)
#                         in_degree[obj_name] += 1
        
#         return graph, in_degree

#     def topological_sort(self, objects: List[Dict]) -> List[Dict]:
#         """Sort objects using topological ordering for proper dependency resolution"""
#         if not objects:
#             return []
        
#         try:
#             graph, in_degree = self.build_dependency_graph(objects)
            
#             # Kahn's algorithm
#             queue = [name for name, degree in in_degree.items() if degree == 0]
#             result = []
            
#             while queue:
#                 current = queue.pop(0)
#                 result.append(current)
                
#                 for neighbor in graph[current]:
#                     in_degree[neighbor] -= 1
#                     if in_degree[neighbor] == 0:
#                         queue.append(neighbor)
            
#             # Check for cycles
#             if len(result) != len(objects):
#                 self.log("Circular dependency detected in object groups", "WARN")
#                 # Fall back to simple dependency count sorting
#                 return sorted(objects, key=lambda x: self.count_dependencies(x))
            
#             # Return objects in sorted order
#             obj_map = {obj['name']: obj for obj in objects}
#             return [obj_map[name] for name in result]
            
#         except Exception as e:
#             self.log(f"Topological sort failed: {e}, falling back to simple sorting", "WARN")
#             return sorted(objects, key=lambda x: self.count_dependencies(x))

#     def count_dependencies(self, obj: Dict) -> int:
#         """Count the number of dependencies for an object"""
#         dep_count = 0
#         if 'objects' in obj:
#             dep_count += len(obj['objects'])
#         if 'literals' in obj:
#             dep_count += len(obj['literals'])
#         return dep_count

#     def check_circular_dependencies(self, obj_data: Dict, existing_objects: Dict, visited: set = None, path: List[str] = None) -> Dict:
#         """Check for circular dependencies in object groups"""
#         if visited is None:
#             visited = set()
#         if path is None:
#             path = []
        
#         obj_name = obj_data.get('name')
#         if obj_name in visited:
#             return {'has_cycle': True, 'cycle_path': path + [obj_name]}
        
#         visited.add(obj_name)
#         path.append(obj_name)
        
#         # Check references in this object
#         if 'objects' in obj_data:
#             for ref in obj_data['objects']:
#                 ref_name = ref.get('name')
#                 if ref_name:
#                     # Find the referenced object (cache-only during bulk operations)
#                     ref_obj = self._find_object_in_cache_only('object_group', ref_name, existing_objects)
#                     if ref_obj and ref_obj.get('type') == 'NetworkGroup':
#                         cycle_check = self.check_circular_dependencies(ref_obj, existing_objects, visited.copy(), path.copy())
#                         if cycle_check['has_cycle']:
#                             return cycle_check
        
#         return {'has_cycle': False, 'cycle_path': []}

#     def resolve_object_reference(self, name: str, obj_type: str, existing_objects: Dict) -> Optional[Dict]:
#         """Resolve object reference with comprehensive lookup"""
        
#         # Define type mappings for different reference patterns
#         type_mappings = {
#             'host': ['hosts'],
#             'network': ['networks'], 
#             'range': ['ranges'],
#             'fqdn': ['fqdns'],
#             'service': ['protocol_ports'],
#             'protocolport': ['protocol_ports'],
#             'networkgroup': ['network_groups'],
#             'protocolportgroup': ['protocol_port_groups']
#         }
        
#         search_lists = type_mappings.get(obj_type.lower(), [obj_type.lower() + 's'])
        
#         for search_list in search_lists:
#             objects = existing_objects.get(search_list, [])
            
#             # Exact match
#             for obj in objects:
#                 if obj.get('name') == name:
#                     return obj
            
#             # Case-insensitive match
#             for obj in objects:
#                 if obj.get('name', '').lower() == name.lower():
#                     self.log(f"Found case-insensitive match: {obj.get('name')}", "DEBUG")
#                     return obj
        
#         # Live lookup as fallback
#         return self._live_lookup_object_by_name(obj_type, name)

#     def validate_object_dependencies(self, obj_data: Dict, existing_objects: Dict, objects_to_create: Dict = None) -> Dict:
#         """Enhanced dependency validation with comprehensive reference resolution"""
#         validation_result = {
#             'valid': True,
#             'missing_dependencies': [],
#             'resolved_references': [],
#             'warnings': []
#         }
        
#         # Check object references
#         if 'objects' in obj_data:
#             for ref_obj in obj_data['objects']:
#                 if 'name' in ref_obj:
#                     ref_name = ref_obj['name']
#                     ref_type = ref_obj.get('type', 'Host').lower()
                    
#                     # Try to resolve reference in existing objects first
#                     found_obj = self.resolve_object_reference(ref_name, ref_type, existing_objects)
                    
#                     # If not found in existing objects, check objects to be created
#                     if not found_obj and objects_to_create:
#                         found_obj = self._find_in_objects_to_create(ref_name, ref_type, objects_to_create)
                    
#                     if found_obj:
#                         # Update reference with UUID for API call
#                         ref_obj['id'] = found_obj['id']
#                         ref_obj['type'] = found_obj['type']
#                         validation_result['resolved_references'].append({
#                             'name': ref_name,
#                             'id': found_obj['id'],
#                             'type': found_obj['type']
#                         })
#                     else:
#                         validation_result['valid'] = False
#                         validation_result['missing_dependencies'].append({
#                             'name': ref_name,
#                             'type': ref_type,
#                             'context': f"Referenced in {obj_data.get('name', 'unknown')}"
#                         })
#                 elif 'id' in ref_obj:
#                     # This is a reference to an existing object by UUID
#                     obj_uuid = ref_obj['id']
#                     obj_type = ref_obj.get('type', 'unknown')
                    
#                     # Try to find the referenced object
#                     found = False
#                     type_mapping = {
#                         'Host': 'hosts',
#                         'Network': 'networks', 
#                         'Service': 'protocol_ports',
#                         'NetworkGroup': 'network_groups'
#                     }
                    
#                     search_list = existing_objects.get(type_mapping.get(obj_type, 'hosts'), [])
#                     for existing_obj in search_list:
#                         if existing_obj.get('id') == obj_uuid:
#                             found = True
#                             break
                    
#                     if not found:
#                         validation_result['valid'] = False
#                         validation_result['missing_dependencies'].append({
#                             'id': obj_uuid,
#                             'type': obj_type,
#                             'name': ref_obj.get('name', 'unknown')
#                         })
        
#         # Check for circular references in groups
#         if obj_data.get('type') in ['NetworkGroup', 'ProtocolPortGroup']:
#             circular_check = self.check_circular_dependencies(obj_data, existing_objects)
#             if circular_check['has_cycle']:
#                 validation_result['valid'] = False
#                 validation_result['warnings'].append(f"Circular dependency detected: {circular_check['cycle_path']}")
        
#         # Check for literal values that might need validation
#         if 'literals' in obj_data:
#             for literal in obj_data['literals']:
#                 if 'value' in literal:
#                     # Validate IP addresses, networks, etc.
#                     try:
#                         import ipaddress
#                         ipaddress.ip_network(literal['value'], strict=False)
#                     except ValueError:
#                         validation_result['warnings'].append(f"Invalid network literal: {literal['value']}")
        
#         return validation_result
    
#     def create_object_with_validation(self, obj_type: str, obj_data: Dict, existing_objects: Dict) -> Dict:
#         """Create an object with comprehensive validation and error recovery"""
#         result = {
#             'success': False,
#             'action': 'none',
#             'message': '',
#             'object_id': None
#         }
        
#         object_name = obj_data.get('name', 'unknown')
        
#         try:
#             # Step 0: Apply automatic data format corrections and handle type changes
#             corrected_obj_data = self.correct_data_format(obj_data)
            
#             # Handle detected type issues by changing object type
#             if '_type_issues' in corrected_obj_data:
#                 for type_issue in corrected_obj_data['_type_issues']:
#                     if type_issue['suggested_type'] == 'Range' and obj_type in ['network', 'Network']:
#                         # Change to Range object for IP ranges
#                         corrected_obj_data['type'] = 'Range'
#                         obj_type = 'range'
#                         self.log(f"Converting {object_name} from Network to Range object", "INFO")
#                     elif type_issue['suggested_type'] == 'FQDN' and obj_type in ['network', 'Network']:
#                         # Change to FQDN object for domain names
#                         corrected_obj_data['type'] = 'FQDN'
#                         obj_type = 'fqdn'
#                         self.log(f"Converting {object_name} from Network to FQDN object", "INFO")
#                 # Remove the type issues metadata
#                 del corrected_obj_data['_type_issues']
            
#             # Step 1: Check if object exists if overwrite is enabled (cache-only during bulk operations)
#             existing_obj = None
#             if self.overwrite:
#                 existing_obj = self._find_object_in_cache_only(obj_type, object_name, existing_objects)
#                 if existing_obj:
#                     self.log(f"Found existing {obj_type} '{object_name}' with ID: {existing_obj.get('id')}", "DEBUG")
#                 else:
#                     self.log(f"No existing {obj_type} '{object_name}' found in cache - will attempt creation", "DEBUG")
            
#             # Step 2: Validate dependencies for groups with fallback handling
#             if obj_type in ['object_group', 'networkgroup'] and 'objects' in corrected_obj_data:
#                 validation = self.validate_object_dependencies(corrected_obj_data, existing_objects)
                
#                 if not validation['valid']:
#                     # Try to create group with available dependencies only
#                     self.log(f"Object group '{object_name}' has missing dependencies - attempting partial creation", "WARN")
#                     for missing in validation['missing_dependencies']:
#                         self.log(f"  - Missing: {missing.get('name', missing.get('id', 'unknown'))} (type: {missing['type']})", "WARN")
                    
#                     # Remove invalid references and keep only valid ones + literals
#                     original_objects = corrected_obj_data.get('objects', [])
#                     valid_objects = []
                    
#                     for obj_ref in original_objects:
#                         # Check if this reference is valid
#                         is_missing = False
#                         for missing_dep in validation['missing_dependencies']:
#                             if (obj_ref.get('name') == missing_dep.get('name') or 
#                                 obj_ref.get('id') == missing_dep.get('id')):
#                                 is_missing = True
#                                 break
                        
#                         if not is_missing:
#                             valid_objects.append(obj_ref)
                    
#                     # If we have some valid objects or literals, create a partial group
#                     has_literals = bool(corrected_obj_data.get('literals'))
#                     if valid_objects or has_literals:
#                         corrected_obj_data['objects'] = valid_objects
#                         self.log(f"Creating partial group '{object_name}' with {len(valid_objects)} objects + {len(corrected_obj_data.get('literals', []))} literals", "INFO")
#                     else:
#                         # No valid references - skip for now and try in later pass
#                         result['message'] = f"No valid dependencies found for group - deferring to later pass"
#                         return result
                
#                 if validation['warnings']:
#                     for warning in validation['warnings']:
#                         self.log(f"Warning for {obj_type} '{object_name}': {warning}", "WARN")
            
#             # Step 3: Handle existing objects with overwrite logic
#             if existing_obj and self.overwrite:
#                 if obj_type in ['service', 'object_group']:
#                     # Delete and recreate for services and groups
#                     if self.delete_existing_object(obj_type, existing_obj['id']):
#                         # Track the deletion
#                         self.track_operation('delete', {
#                             'type': obj_type, 
#                             'name': object_name, 
#                             'id': existing_obj['id']
#                         }, success=True)
                        
#                         api_result = self._make_creation_api_call(obj_type, corrected_obj_data)
#                         if api_result:
#                             result['success'] = True
#                             result['action'] = 'overwritten'
#                             result['object_id'] = api_result.get('id')
#                             result['message'] = f"Successfully overwritten {obj_type}: {object_name}"
#                             self.log(result['message'])
                            
#                             # Track the creation
#                             self.track_operation('create', {
#                                 'type': obj_type,
#                                 'name': object_name,
#                                 'id': result['object_id']
#                             }, success=True)
#                         else:
#                             result['message'] = f"Failed to recreate {obj_type} after deletion: {object_name}"
#                             # Track the failed creation
#                             self.track_operation('create', {
#                                 'type': obj_type,
#                                 'name': object_name,
#                                 'id': None
#                             }, success=False, error_message=result['message'])
#                     else:
#                         result['message'] = f"Failed to delete existing {obj_type}: {object_name} (ID: {existing_obj['id']}) - check FMC permissions and object dependencies"
#                         # Track the failed deletion
#                         self.track_operation('delete', {
#                             'type': obj_type,
#                             'name': object_name,
#                             'id': existing_obj['id']
#                         }, success=False, error_message=result['message'])
#                 else:
#                     # Update for hosts and networks
#                     if self.update_existing_object(obj_type, existing_obj['id'], corrected_obj_data):
#                         result['success'] = True
#                         result['action'] = 'updated'
#                         result['object_id'] = existing_obj['id']
#                         result['message'] = f"Successfully updated {obj_type}: {object_name}"
#                         self.log(result['message'])
                        
#                         # Track the update
#                         self.track_operation('update', {
#                             'type': obj_type,
#                             'name': object_name,
#                             'id': existing_obj['id']
#                         }, success=True)
#                     else:
#                         result['message'] = f"Failed to update existing {obj_type}: {object_name} (ID: {existing_obj['id']}) - check object format compatibility and FMC permissions"
#                         # Track the failed update
#                         self.track_operation('update', {
#                             'type': obj_type,
#                             'name': object_name,
#                             'id': existing_obj['id']
#                         }, success=False, error_message=result['message'])
#             else:
#                 # Step 4: Create new object
#                 api_result = self._make_creation_api_call(obj_type, corrected_obj_data)
#                 if api_result:
#                     result['success'] = True
#                     result['action'] = 'created'
#                     result['object_id'] = api_result.get('id')
#                     result['message'] = f"Successfully created {obj_type}: {object_name}"
#                     self.log(result['message'])
                    
#                     # Track the creation
#                     self.track_operation('create', {
#                         'type': obj_type,
#                         'name': object_name,
#                         'id': result['object_id']
#                     }, success=True)
#                 else:
#                     # Step 4.1: Fallback - if creation failed due to name conflict, try to update
#                     if self.overwrite and self.last_api_error and 'already exists' in str(self.last_api_error.get('response_text', '')).lower():
#                         self.log(f"Creation failed due to name conflict, attempting to find and update {obj_type}: {object_name}", "WARN")
                        
#                         # Use existing objects cache only (no live lookups during bulk operations to avoid rate limiting)
#                         conflicting_obj = self._find_object_in_cache_only(obj_type, object_name, existing_objects)
                        
#                         if conflicting_obj:
#                             self.log(f"Found conflicting object in cache, attempting update for {obj_type}: {object_name}")
#                             if self.update_existing_object(obj_type, conflicting_obj['id'], corrected_obj_data):
#                                 result['success'] = True
#                                 result['action'] = 'updated_fallback'
#                                 result['object_id'] = conflicting_obj['id']
#                                 result['message'] = f"Successfully updated {obj_type} via fallback: {object_name}"
#                                 self.log(result['message'])
                                
#                                 # Track the update
#                                 self.track_operation('update', {
#                                     'type': obj_type,
#                                     'name': object_name,
#                                     'id': conflicting_obj['id']
#                                 }, success=True)
#                             else:
#                                 result['message'] = f"Failed to update {obj_type} via fallback: {object_name} - object found but update operation failed"
#                         else:
#                             # Cache miss during conflict resolution - try targeted live lookup first
#                             self.log(f"Object '{object_name}' reported as existing by FMC but not found in cache - attempting targeted lookup", "DEBUG")
                            
#                             # Try targeted live lookup first (much more efficient than full cache refresh)
#                             conflicting_obj = self._live_lookup_object_by_name(obj_type, object_name)
                            
#                             if conflicting_obj:
#                                 self.log(f"Found conflicting object via targeted lookup, attempting update for {obj_type}: {object_name}")
#                                 if self.update_existing_object(obj_type, conflicting_obj['id'], corrected_obj_data):
#                                     result['success'] = True
#                                     result['action'] = 'updated_after_lookup'
#                                     result['object_id'] = conflicting_obj['id']
#                                     result['message'] = f"Successfully updated {obj_type} after targeted lookup: {object_name}"
#                                     self.log(result['message'])
                                    
#                                     # Track the update
#                                     self.track_operation('update', {
#                                         'type': obj_type,
#                                         'name': object_name,
#                                         'id': conflicting_obj['id']
#                                     }, success=True)
                                    
#                                     # Add to existing objects cache to avoid future lookups
#                                     type_mapping = {
#                                         'host': 'hosts',
#                                         'network': 'networks',
#                                         'service': 'protocol_ports',
#                                         'object_group': 'network_groups'
#                                     }
#                                     cache_key = type_mapping.get(obj_type)
#                                     if cache_key and cache_key in existing_objects:
#                                         existing_objects[cache_key].append(conflicting_obj)
#                                 else:
#                                     result['message'] = f"Failed to update {obj_type} after targeted lookup: {object_name} - object found but update operation failed"
#                             else:
#                                 # FMC inconsistency: object reported as existing but not found
#                                 # Try a more aggressive search and handle this edge case
#                                 self.log(f"FMC API inconsistency for {obj_type}: '{object_name}' - reported as existing but not findable", "WARN")
                                
#                                 # Wait a moment for FMC to synchronize its state
#                                 time.sleep(2)
                                
#                                 # Try one more comprehensive search attempt
#                                 self.log(f"Attempting comprehensive search for phantom object: {object_name}", "DEBUG")
#                                 comprehensive_search = self._comprehensive_object_search(obj_type, object_name)
                                
#                                 if comprehensive_search:
#                                     self.log(f"Comprehensive search found object, attempting update", "DEBUG")
#                                     if self.update_existing_object(obj_type, comprehensive_search['id'], corrected_obj_data):
#                                         result['success'] = True
#                                         result['action'] = 'updated_after_comprehensive_search'
#                                         result['message'] = f"Successfully updated {obj_type}: {object_name} after comprehensive search"
#                                         result['object_id'] = comprehensive_search['id']
#                                         self.track_operation('update', comprehensive_search, success=True)
#                                     else:
#                                         result['message'] = f"Found {obj_type}: {object_name} via comprehensive search but update failed"
#                                 else:
#                                     # This is a genuine FMC API inconsistency - try to force creation with slight variation
#                                     self.log(f"FMC API inconsistency confirmed for {object_name} - attempting recovery strategy", "WARN")
#                                     result['message'] = f"FMC API inconsistency for {obj_type}: {object_name} - object exists but unfindable, requires manual intervention"
#                                     # Set a flag to indicate this needs special handling
#                                     result['api_inconsistency'] = True
#                     else:
#                         result['message'] = f"Failed to create {obj_type}: {object_name}"
                    
#                     if not result['success']:
#                         # Track the failed creation
#                         self.track_operation('create', {
#                             'type': obj_type,
#                             'name': object_name,
#                             'id': None
#                         }, success=False, error_message=result['message'])
        
#         except Exception as e:
#             result['message'] = f"Exception creating {obj_type} '{object_name}': {str(e)}"
#             self.log(result['message'], "ERROR")
            
#             # Track the exception
#             self.track_operation('create', {
#                 'type': obj_type,
#                 'name': object_name,
#                 'id': None
#             }, success=False, error_message=result['message'])
        
#         return result
    
#     def get_bulk_endpoint(self, obj_type: str) -> Optional[str]:
#         """Get the appropriate bulk API endpoint for object type"""
#         endpoint_mapping = {
#             'host': '/api/fmc_config/v1/domain/{domain_uuid}/object/hosts',
#             'network': '/api/fmc_config/v1/domain/{domain_uuid}/object/networks',
#             'range': '/api/fmc_config/v1/domain/{domain_uuid}/object/ranges',
#             'fqdn': '/api/fmc_config/v1/domain/{domain_uuid}/object/fqdns',
#             'service': '/api/fmc_config/v1/domain/{domain_uuid}/object/protocolportobjects',
#             'object_group': '/api/fmc_config/v1/domain/{domain_uuid}/object/networkgroups',
#             'networkgroup': '/api/fmc_config/v1/domain/{domain_uuid}/object/networkgroups',
#             'service_group': '/api/fmc_config/v1/domain/{domain_uuid}/object/portobjectgroups',
#             'network_group': '/api/fmc_config/v1/domain/{domain_uuid}/object/networkgroups'
#         }
        
#         return endpoint_mapping.get(obj_type)

#     def _fix_object_group_format(self, group_obj: Dict, existing_objects: Dict) -> Optional[Dict]:
#         """Fix object group format to comply with FMC API requirements"""
        
#         # Fix type name case
#         if group_obj.get('type') == 'networkGroup':
#             group_obj['type'] = 'NetworkGroup'
#         elif group_obj.get('type') == 'serviceGroup':
#             group_obj['type'] = 'ServiceGroup'
            
#         # Fix object references in the group
#         if 'objects' in group_obj:
#             fixed_objects = []
#             for member in group_obj['objects']:
#                 if 'name' in member and 'type' in member:
#                     # Try to find the object ID from existing objects
#                     object_id = self._find_object_id_by_name(member['name'], existing_objects)
#                     if object_id:
#                         # Convert to proper FMC API format
#                         fixed_member = {
#                             'id': object_id,
#                             'type': self._convert_member_type(member['type'])
#                         }
#                         fixed_objects.append(fixed_member)
#                     else:
#                         self.log(f"Warning: Could not find ID for object '{member['name']}' in group '{group_obj.get('name')}'", "WARN")
#                         # For group references, try to find in groups specifically
#                         if member['type'] == 'NetworkGroupObject':
#                             group_id = self._find_group_id_by_name(member['name'], existing_objects)
#                             if group_id:
#                                 fixed_member = {
#                                     'id': group_id,
#                                     'type': 'NetworkGroup'
#                                 }
#                                 fixed_objects.append(fixed_member)
#                                 continue
                        
#                         # Keep original format for now, let FMC handle it
#                         fixed_objects.append(member)
#                 else:
#                     # Already in correct format or unknown format
#                     fixed_objects.append(member)
            
#             if not fixed_objects:
#                 self.log(f"Group '{group_obj.get('name')}' has no valid members after format fixing")
#                 return None
                
#             group_obj['objects'] = fixed_objects
            
#         return group_obj
    
#     def _find_object_id_by_name(self, name: str, existing_objects: Dict) -> Optional[str]:
#         """Find object UUID by name from existing objects"""
#         # Search in different object types
#         for obj_type in ['hosts', 'networks', 'ranges', 'fqdns', 'protocol_ports']:
#             if obj_type in existing_objects:
#                 for obj in existing_objects[obj_type]:
#                     if obj.get('name') == name:
#                         return obj.get('id')
#         return None
    
#     def _find_group_id_by_name(self, name: str, existing_objects: Dict) -> Optional[str]:
#         """Find group UUID by name from existing objects"""
#         # Search in group types
#         for group_type in ['network_groups', 'service_groups']:
#             if group_type in existing_objects:
#                 for group in existing_objects[group_type]:
#                     if group.get('name') == name:
#                         return group.get('id')
#         return None
    
#     def _convert_member_type(self, member_type: str) -> str:
#         """Convert member type to FMC API format"""
#         type_mapping = {
#             'NetworkObject': 'Network',
#             'NetworkGroupObject': 'NetworkGroup',
#             'HostObject': 'Host', 
#             'RangeObject': 'Range',
#             'FQDNObject': 'FQDN',
#             'ProtocolPortObject': 'ProtocolPortObject',
#             'ServiceObject': 'ProtocolPortObject'
#         }
#         return type_mapping.get(member_type, member_type)
    
#     def create_objects_bulk(self, obj_type: str, objects: List[Dict], existing_objects: Dict) -> Dict:
#         """Create objects in bulk following Cisco's recommendations"""
        
#         # Cisco limits: max 1,000 objects per bulk operation, max 2MB payload
#         MAX_BULK_SIZE = 1000
#         MAX_PAYLOAD_SIZE = 2 * 1024 * 1024  # 2MB
        
#         results = {
#             'created': 0, 
#             'updated': 0, 
#             'errors': 0, 
#             'skipped': 0,
#             'overwritten': 0,
#             'failed_objects': []
#         }
        
#         if not objects:
#             return results
        
#         self.log(f"Processing {len(objects)} {obj_type} objects in bulk...")
        
#         # Fix data format for FMC API compliance 
#         processed_objects = []
#         fixed_format_count = 0
        
#         for obj in objects:
#             # Fix object group data format for FMC API compliance
#             if obj.get('type') in ['networkGroup', 'serviceGroup']:
#                 obj = self._fix_object_group_format(obj, existing_objects)
#                 fixed_format_count += 1
                
#             processed_objects.append(obj)
            
#         if fixed_format_count > 0:
#             self.log(f"Fixed data format for {fixed_format_count} object groups (API compliance)")
        
#         # Pre-validate all objects for dependencies
#         validated_objects = []
#         for obj in processed_objects:
#             validation = self.validate_object_dependencies(obj, existing_objects)
#             if validation['valid']:
#                 validated_objects.append(obj)
#                 # Log resolved references for debugging
#                 if validation['resolved_references']:
#                     self.log(f"Resolved {len(validation['resolved_references'])} references for {obj.get('name')}", "DEBUG")
#             else:
#                 results['skipped'] += 1
#                 missing_deps = [dep.get('name', dep.get('id', 'unknown')) for dep in validation['missing_dependencies']]
#                 self.log(f"Skipping {obj.get('name', 'unknown')} due to missing dependencies: {missing_deps}")
        
#         if not validated_objects:
#             self.log(f"No {obj_type} objects passed validation", "WARN")
#             return results
        
#         # Get bulk endpoint
#         endpoint = self.get_bulk_endpoint(obj_type)
#         if not endpoint:
#             self.log(f"Bulk operations not supported for {obj_type}, falling back to individual creation", "WARN")
#             return self._create_objects_individually(obj_type, validated_objects, existing_objects)
        
#         # Process in batches
#         for i in range(0, len(validated_objects), MAX_BULK_SIZE):
#             batch = validated_objects[i:i + MAX_BULK_SIZE]
            
#             # Check payload size
#             payload_size = len(json.dumps(batch).encode('utf-8'))
#             if payload_size > MAX_PAYLOAD_SIZE:
#                 self.log(f"Batch payload too large ({payload_size} bytes), processing individually", "WARN")
#                 # Fall back to individual creation for large objects
#                 batch_results = self._create_objects_individually(obj_type, batch, existing_objects)
#             else:
#                 # Use bulk API
#                 batch_results = self._execute_bulk_creation(obj_type, endpoint, batch, existing_objects)
            
#             # Aggregate results
#             for key in results:
#                 if key in batch_results:
#                     if isinstance(results[key], list):
#                         results[key].extend(batch_results[key])
#                     else:
#                         results[key] += batch_results[key]
        
#         self.log(f"Bulk {obj_type} creation completed: {results['created']} created, {results['updated']} updated, {results['skipped']} skipped, {results['errors']} errors")
#         return results

#     def _execute_bulk_creation(self, obj_type: str, endpoint: str, objects: List[Dict], existing_objects: Dict) -> Dict:
#         """Execute bulk creation API call"""
#         results = {'created': 0, 'updated': 0, 'errors': 0, 'skipped': 0, 'overwritten': 0, 'failed_objects': []}
        
#         # Apply data format corrections before sending
#         corrected_objects = []
#         for obj in objects:
#             corrected_obj = self.correct_data_format(obj)
#             corrected_objects.append(corrected_obj)
        
#         try:
#             # Make bulk API call
#             bulk_response = self.make_api_call('POST', endpoint + '?bulk=true', corrected_objects)
            
#             if bulk_response and 'items' in bulk_response:
#                 # Process successful creations
#                 for item in bulk_response['items']:
#                     if 'id' in item:
#                         results['created'] += 1
                        
#                         # Track operation for rollback
#                         self.track_operation('create', {
#                             'type': obj_type,
#                             'name': item.get('name', 'unknown'),
#                             'id': item['id']
#                         }, success=True)
            
#             elif bulk_response is None and self.last_api_error:
#                 # Handle bulk operation failures
#                 error_status = self.last_api_error.get('status_code', 0)
#                 error_message = self.last_api_error.get('response_text', '')
                
#                 if error_status == 400 and 'already exists' in str(error_message).lower():
#                     # Handle naming conflicts in bulk
#                     if self.overwrite:
#                         self.log(f"Bulk creation failed due to conflicts despite --overwrite being enabled", "WARN")
#                         self.log(f"Processing individually to attempt delete-and-recreate for each conflicting object", "INFO")
#                         return self._create_objects_individually(obj_type, objects, existing_objects)
#                     else:
#                         self.log(f"Bulk creation failed due to naming conflicts. Use --overwrite flag to resolve", "ERROR")
#                         results['errors'] = len(objects)
#                         results['failed_objects'] = [obj.get('name', 'unknown') for obj in objects]
#                 else:
#                     self.log(f"Bulk creation failed: HTTP {error_status} - {error_message}", "ERROR")
#                     results['errors'] = len(objects)
#                     results['failed_objects'] = [obj.get('name', 'unknown') for obj in objects]
        
#         except Exception as e:
#             self.log(f"Exception during bulk creation: {e}", "ERROR")
#             # Fall back to individual creation
#             return self._create_objects_individually(obj_type, objects, existing_objects)
        
#         # Invalidate cache once per batch if objects were created/modified
#         if results.get('created', 0) > 0 or results.get('updated', 0) > 0 or results.get('overwritten', 0) > 0:
#             self.log(f"Invalidating cache after bulk operation: {results.get('created', 0)} created, {results.get('updated', 0)} updated", "DEBUG")
#             self._invalidate_cache()
        
#         return results

#     def create_objects_bulk_guaranteed(self, obj_type: str, objects: List[Dict], existing_objects: Dict) -> Dict:
#         """Create objects in bulk with 100% success guarantee - no failures allowed"""
        
#         results = {
#             'created': 0, 
#             'updated': 0, 
#             'errors': 0, 
#             'skipped': 0,
#             'overwritten': 0,
#             'failed_objects': []
#         }
        
#         if not objects:
#             return results
        
#         self.log(f"🎯 GUARANTEED BULK MODE: Processing {len(objects)} {obj_type} objects with 100% success requirement...", "INFO")
        
#         # In guaranteed mode, process individually to ensure each object succeeds
#         # Bulk operations are harder to recover from if they fail
        
#         for i, obj in enumerate(objects):
#             obj_name = obj.get('name', f'object_{i}')
#             self.log(f"🔄 Processing {i+1}/{len(objects)}: {obj_type} '{obj_name}'", "DEBUG")
            
#             # Use aggressive retry for each object
#             result = self.create_object_with_retry(obj_type, obj, existing_objects, max_retries=10)
            
#             if result['success']:
#                 if result['action'] == 'created':
#                     results['created'] += 1
#                 elif result['action'] == 'updated':
#                     results['updated'] += 1
#                 elif result['action'] in ['overwritten', 'force_updated']:
#                     results['overwritten'] += 1
                
#                 self.log(f"✅ {i+1}/{len(objects)} SUCCESS: {obj_type} '{obj_name}' {result['action']}", "DEBUG")
                
#                 # Track object creation for batch cache invalidation
#                 if result['object_id']:
#                     # Update existing_objects cache immediately to help with dependency resolution
#                     type_mapping = {
#                         'host': 'hosts',
#                         'network': 'networks',
#                         'service': 'protocol_ports',
#                         'object_group': 'network_groups'
#                     }
#                     cache_key = type_mapping.get(obj_type)
#                     if cache_key and cache_key in existing_objects:
#                         new_obj = {'id': result['object_id'], 'name': obj_name, 'type': obj.get('type', obj_type)}
#                         existing_objects[cache_key].append(new_obj)
            
#             elif result.get('requires_rollback'):
#                 # Critical failure - initiate immediate rollback
#                 self.log(f"❌ CRITICAL FAILURE in bulk processing: {obj_type} '{obj_name}'", "ERROR")
#                 self.log("🔄 Initiating automatic rollback for 100% synchronization guarantee", "ERROR")
                
#                 # Set rollback flag in results
#                 results['requires_rollback'] = True
#                 results['failed_object'] = obj_name
#                 results['critical_failure'] = True
#                 return results
            
#             else:
#                 # Check if this is a phantom object that should be skipped
#                 if result.get('phantom_object'):
#                     self.log(f"👻 Phantom object detected: {obj_type} '{obj_name}' - skipping to prevent infinite loop", "WARN")
#                     results['skipped'] += 1
#                     continue
                
#                 # This should not happen in guaranteed mode, but handle gracefully
#                 self.log(f"❌ Unexpected failure in guaranteed mode: {obj_type} '{obj_name}' - {result.get('message', 'Unknown error')}", "ERROR")
#                 results['errors'] += 1
#                 results['failed_objects'].append(obj_name)
                
#                 # In 100% synchronization mode, any failure is critical
#                 results['requires_rollback'] = True
#                 results['failed_object'] = obj_name
#                 results['critical_failure'] = True
#                 return results
        
#         # Invalidate cache once per batch if objects were created/modified
#         objects_modified = results.get('created', 0) + results.get('updated', 0) + results.get('overwritten', 0)
#         if objects_modified > 0:
#             self.log(f"🔄 Invalidating cache after guaranteed bulk operation: {objects_modified} objects modified", "DEBUG")
#             self._invalidate_cache()
        
#         self.log(f"✅ GUARANTEED BULK COMPLETE: {obj_type} - {results['created']} created, {results['updated']} updated, {results['overwritten']} overwritten", "INFO")
#         return results

#     def _create_objects_individually(self, obj_type: str, objects: List[Dict], existing_objects: Dict) -> Dict:
#         """Fallback to create objects individually when bulk fails"""
#         results = {'created': 0, 'updated': 0, 'errors': 0, 'skipped': 0, 'overwritten': 0, 'failed_objects': []}
        
#         for obj in objects:
#             result = self.create_object_with_validation(obj_type, obj, existing_objects)
            
#             if result['success']:
#                 if result['action'] == 'created':
#                     results['created'] += 1
#                 elif result['action'] == 'updated':
#                     results['updated'] += 1
#                 elif result['action'] == 'overwritten':
#                     results['overwritten'] += 1
                
#                 # Track object creation for batch cache invalidation
#                 if result['object_id']:
#                     results['objects_created'] = results.get('objects_created', 0) + 1
#             else:
#                 if 'missing dependencies' in result.get('message', '').lower():
#                     results['skipped'] += 1
#                 else:
#                     results['errors'] += 1
#                     results['failed_objects'].append(obj.get('name', 'unknown'))
        
#         # Invalidate cache once per batch if objects were created/modified
#         objects_modified = results.get('created', 0) + results.get('updated', 0) + results.get('overwritten', 0)
#         if objects_modified > 0:
#             self.log(f"Invalidating cache after individual operations: {objects_modified} objects modified", "DEBUG")
#             self._invalidate_cache()
        
#         return results

#     def _comprehensive_pre_flight_validation(self, config: Dict) -> Dict[str, Any]:
#         """Comprehensive validation to guarantee 100% synchronization success"""
#         self.log("Running comprehensive pre-flight validation for 100% success guarantee...", "INFO")
        
#         validation_result = {
#             'can_guarantee_success': True,
#             'blocking_issues': [],
#             'warnings': [],
#             'corrective_actions': [],
#             'estimated_operations': 0
#         }
        
#         api_calls = config.get('api_calls', {})
#         existing_objects = self.get_existing_objects()
        
#         # Check 1: API connectivity and permissions (minimal testing to avoid rate limiting)
#         try:
#             self.log("Validating FMC API connectivity and permissions (optimized)...", "DEBUG")
#             # Test one endpoint with minimal request to avoid rate limiting in pre-flight
#             test_endpoint = '/api/fmc_config/v1/domain/{domain_uuid}/object/hosts'
#             test_result = self.make_api_call('GET', test_endpoint, params={'limit': 1})
#             if not test_result:
#                 validation_result['blocking_issues'].append(f"Cannot access API endpoints - check API permissions")
#                 validation_result['can_guarantee_success'] = False
#             else:
#                 self.log("✅ API connectivity and permissions verified", "DEBUG")
                    
#         except Exception as e:
#             validation_result['blocking_issues'].append(f"API connectivity test failed: {e}")
#             validation_result['can_guarantee_success'] = False
        
#         # Check 2: Comprehensive dependency validation with resolution planning
#         self.log("Validating all object dependencies and resolution paths...", "DEBUG")
#         dependency_validation = self._validate_all_dependencies_comprehensive(api_calls, existing_objects)
#         if not dependency_validation['all_resolvable']:
#             validation_result['blocking_issues'].extend(dependency_validation['unresolvable_dependencies'])
#             validation_result['can_guarantee_success'] = False
        
#         # Check 3: Data format validation and auto-correction capability
#         self.log("Validating data formats and correction capabilities...", "DEBUG")
#         format_validation = self._validate_all_data_formats(api_calls)
#         if not format_validation['all_correctable']:
#             validation_result['blocking_issues'].extend(format_validation['uncorrectable_issues'])
#             validation_result['can_guarantee_success'] = False
        
#         # Check 4: Conflict resolution capability (optimized to prevent rate limiting)
#         if self.overwrite:
#             self.log("Validating conflict resolution capabilities (optimized)...", "DEBUG")
#             # Use optimized conflict validation that doesn't make individual API calls
#             conflicts = self._check_naming_conflicts(api_calls, existing_objects)
#             if conflicts:
#                 self.log(f"Found {len(conflicts)} naming conflicts - will be resolved during migration with aggressive retry", "DEBUG")
#                 validation_result['warnings'].append(f"{len(conflicts)} naming conflicts found - will be resolved during migration")
#                 # Don't mark as blocking since we can resolve conflicts during migration
#             else:
#                 self.log("✅ No naming conflicts detected", "DEBUG")
#         else:
#             # Check for naming conflicts without overwrite
#             conflicts = self._check_naming_conflicts(api_calls, existing_objects)
#             if conflicts:
#                 validation_result['blocking_issues'].append(f"Naming conflicts detected but overwrite mode disabled - {len(conflicts)} conflicts found")
#                 validation_result['can_guarantee_success'] = False
        
#         # Check 5: Resource limits and capacity
#         self.log("Validating FMC resource limits and capacity...", "DEBUG")
#         capacity_check = self._validate_fmc_capacity(api_calls, existing_objects)
#         if not capacity_check['sufficient_capacity']:
#             validation_result['blocking_issues'].extend(capacity_check['capacity_issues'])
#             validation_result['can_guarantee_success'] = False
        
#         # Check 6: Circular dependency detection
#         self.log("Checking for circular dependencies...", "DEBUG")
#         circular_deps = self._detect_all_circular_dependencies(api_calls)
#         if circular_deps:
#             validation_result['blocking_issues'].extend([f"Circular dependency detected: {dep}" for dep in circular_deps])
#             validation_result['can_guarantee_success'] = False
        
#         # Calculate estimated operations for progress tracking
#         validation_result['estimated_operations'] = self._calculate_total_operations(api_calls, existing_objects)
        
#         # Generate corrective actions if issues found
#         if not validation_result['can_guarantee_success']:
#             validation_result['corrective_actions'] = self._generate_corrective_actions(validation_result['blocking_issues'])
        
#         # Summary with optimization notes
#         if validation_result['can_guarantee_success']:
#             self.log(f"✅ Pre-flight validation PASSED: {validation_result['estimated_operations']} operations can be guaranteed to succeed", "INFO")
#             if validation_result['warnings']:
#                 self.log(f"⚠️ {len(validation_result['warnings'])} warnings noted - will be handled during migration", "INFO")
#         else:
#             self.log(f"❌ Pre-flight validation FAILED: {len(validation_result['blocking_issues'])} blocking issues found", "ERROR")
#             self.log("Suggested corrective actions:", "INFO")
#             for action in validation_result.get('corrective_actions', []):
#                 self.log(f"  - {action}", "INFO")
        
#         return validation_result
    
#     def _validate_all_dependencies_comprehensive(self, api_calls: Dict, existing_objects: Dict) -> Dict[str, Any]:
#         """Validate all dependencies can be resolved with comprehensive path planning"""
#         result = {
#             'all_resolvable': True,
#             'unresolvable_dependencies': [],
#             'dependency_tree': {},
#             'resolution_order': []
#         }
        
#         # Build complete dependency graph including objects to be created
#         all_objects = {}
#         objects_to_create = {}
        
#         # Map existing objects
#         for obj_type, obj_list in existing_objects.items():
#             for obj in obj_list:
#                 all_objects[obj['name']] = {'id': obj['id'], 'type': obj_type, 'exists': True}
        
#         # Map objects to be created
#         for obj_type, obj_data in api_calls.items():
#             if obj_type.endswith('_objects') and 'data' in obj_data:
#                 for obj in obj_data['data']:
#                     obj_name = obj.get('name')
#                     if obj_name:
#                         all_objects[obj_name] = {'type': obj_type, 'exists': False, 'data': obj}
#                         objects_to_create[obj_name] = obj
        
#         # Validate each object's dependencies
#         for obj_name, obj_info in objects_to_create.items():
#             obj_data = obj_info
#             dependencies = []
            
#             # Check object references
#             if 'objects' in obj_data:
#                 for ref in obj_data['objects']:
#                     if 'name' in ref:
#                         dep_name = ref['name']
#                         if dep_name not in all_objects:
#                             result['unresolvable_dependencies'].append(f"Object '{obj_name}' references unknown object '{dep_name}'")
#                             result['all_resolvable'] = False
#                         else:
#                             dependencies.append(dep_name)
            
#             result['dependency_tree'][obj_name] = dependencies
        
#         # Check for dependency cycles and create resolution order
#         try:
#             result['resolution_order'] = self._topological_sort_dependencies(result['dependency_tree'])
#         except Exception as e:
#             result['unresolvable_dependencies'].append(f"Circular dependency prevents resolution: {e}")
#             result['all_resolvable'] = False
        
#         return result
    
#     def _validate_all_data_formats(self, api_calls: Dict) -> Dict[str, Any]:
#         """Validate all data formats can be automatically corrected"""
#         result = {
#             'all_correctable': True,
#             'uncorrectable_issues': [],
#             'corrections_needed': [],
#             'problematic_objects': []
#         }
        
#         for obj_type, obj_data in api_calls.items():
#             if obj_type.endswith('_objects') and 'data' in obj_data:
#                 for obj in obj_data['data']:
#                     obj_name = obj.get('name', 'unknown')
                    
#                     # Test correction capability
#                     try:
#                         corrected_obj = self.correct_data_format(obj.copy())
                        
#                         # Check for uncorrectable type mismatches
#                         if '_type_issues' in corrected_obj:
#                             for type_issue in corrected_obj['_type_issues']:
#                                 if type_issue['suggested_type'] != obj.get('type'):
#                                     # This is correctable by type conversion
#                                     result['corrections_needed'].append({
#                                         'object': obj_name,
#                                         'issue': f"Type mismatch: {obj.get('type')} -> {type_issue['suggested_type']}",
#                                         'correctable': True
#                                     })
                        
#                         # Check for data format issues
#                         if 'value' in obj and 'value' in corrected_obj:
#                             if obj['value'] != corrected_obj['value']:
#                                 # Validate the correction actually works
#                                 if not self._validate_corrected_value(corrected_obj['value'], obj.get('type')):
#                                     result['uncorrectable_issues'].append(f"Object '{obj_name}' has uncorrectable value format: {obj['value']}")
#                                     result['all_correctable'] = False
#                                     result['problematic_objects'].append(obj_name)
                        
#                     except Exception as e:
#                         result['uncorrectable_issues'].append(f"Object '{obj_name}' cannot be corrected: {e}")
#                         result['all_correctable'] = False
#                         result['problematic_objects'].append(obj_name)
        
#         return result
    
#     def _validate_conflict_resolution_capability(self, api_calls: Dict, existing_objects: Dict) -> Dict[str, Any]:
#         """Validate all naming conflicts can be resolved via update/overwrite"""
#         result = {
#             'all_conflicts_resolvable': True,
#             'unresolvable_conflicts': [],
#             'conflicts_found': []
#         }
        
#         # Build lookup of existing object names by type
#         existing_by_type = {}
#         for obj_type, obj_list in existing_objects.items():
#             existing_by_type[obj_type] = {obj['name'].lower(): obj for obj in obj_list}
        
#         type_mapping = {
#             'host_objects': 'hosts',
#             'network_objects': 'networks',
#             'service_objects': 'protocol_ports',
#             'object_groups': 'network_groups'
#         }
        
#         for obj_type, obj_data in api_calls.items():
#             if obj_type in type_mapping and 'data' in obj_data:
#                 existing_type = type_mapping[obj_type]
#                 existing_names = existing_by_type.get(existing_type, {})
                
#                 for obj in obj_data['data']:
#                     obj_name = obj.get('name')
#                     if obj_name and obj_name.lower() in existing_names:
#                         existing_obj = existing_names[obj_name.lower()]
#                         conflict_info = {
#                             'object_name': obj_name,
#                             'object_type': obj_type,
#                             'existing_id': existing_obj['id'],
#                             'resolvable': True
#                         }
                        
#                         # Assume all existing objects are updatable to avoid excessive API calls
#                         # Individual update capability testing causes rate limiting
#                         # Objects that can't be updated will be handled during actual update attempts
#                         try:
#                             # Quick validation - just check that we have an ID
#                             if existing_obj.get('id'):
#                                 conflict_info['resolvable'] = True
#                                 self.log(f"Conflict resolvable for '{obj_name}' (ID: {existing_obj['id'][:12]}...)", "DEBUG")
#                             else:
#                                 conflict_info['resolvable'] = False
#                                 result['unresolvable_conflicts'].append(f"Object '{obj_name}' exists but has no ID")
#                                 result['all_conflicts_resolvable'] = False
#                         except Exception as e:
#                             conflict_info['resolvable'] = False
#                             result['unresolvable_conflicts'].append(f"Cannot validate conflict for '{obj_name}': {e}")
#                             result['all_conflicts_resolvable'] = False
                        
#                         result['conflicts_found'].append(conflict_info)
        
#         return result
    
#     def _validate_fmc_capacity(self, api_calls: Dict, existing_objects: Dict) -> Dict[str, Any]:
#         """Validate FMC has sufficient capacity for all objects"""
#         result = {
#             'sufficient_capacity': True,
#             'capacity_issues': [],
#             'estimated_totals': {}
#         }
        
#         # Calculate totals after migration
#         current_counts = {obj_type: len(obj_list) for obj_type, obj_list in existing_objects.items()}
#         new_counts = {}
        
#         type_mapping = {
#             'host_objects': 'hosts',
#             'network_objects': 'networks', 
#             'service_objects': 'protocol_ports',
#             'object_groups': 'network_groups'
#         }
        
#         for obj_type, obj_data in api_calls.items():
#             if obj_type in type_mapping and 'data' in obj_data:
#                 mapped_type = type_mapping[obj_type]
#                 new_counts[mapped_type] = len(obj_data['data'])
        
#         # FMC typical limits (these may vary by model/license)
#         fmc_limits = {
#             'hosts': 50000,
#             'networks': 50000,
#             'protocol_ports': 10000,
#             'network_groups': 5000
#         }
        
#         for obj_type, new_count in new_counts.items():
#             current_count = current_counts.get(obj_type, 0)
#             estimated_total = current_count + new_count  # Worst case if no overwrites
#             limit = fmc_limits.get(obj_type, float('inf'))
            
#             result['estimated_totals'][obj_type] = {
#                 'current': current_count,
#                 'new': new_count,
#                 'estimated_total': estimated_total,
#                 'limit': limit,
#                 'within_limit': estimated_total <= limit
#             }
            
#             if estimated_total > limit:
#                 result['capacity_issues'].append(f"{obj_type} would exceed limit: {estimated_total} > {limit}")
#                 result['sufficient_capacity'] = False
        
#         return result
    
#     def _detect_all_circular_dependencies(self, api_calls: Dict) -> List[str]:
#         """Detect circular dependencies in object groups"""
#         circular_deps = []
        
#         # Build graph of group dependencies
#         group_graph = {}
        
#         for obj_type in ['object_groups', 'service_groups']:
#             if obj_type in api_calls and 'data' in api_calls[obj_type]:
#                 for group in api_calls[obj_type]['data']:
#                     group_name = group.get('name')
#                     if group_name:
#                         dependencies = []
#                         if 'objects' in group:
#                             for ref in group['objects']:
#                                 ref_name = ref.get('name')
#                                 if ref_name and ref.get('type', '').endswith('Group'):
#                                     dependencies.append(ref_name)
#                         group_graph[group_name] = dependencies
        
#         # Detect cycles using DFS
#         visited = set()
#         rec_stack = set()
        
#         def has_cycle(node, path):
#             if node in rec_stack:
#                 cycle_start = path.index(node)
#                 cycle = path[cycle_start:] + [node]
#                 return cycle
            
#             if node in visited:
#                 return None
            
#             visited.add(node)
#             rec_stack.add(node)
            
#             for neighbor in group_graph.get(node, []):
#                 cycle = has_cycle(neighbor, path + [node])
#                 if cycle:
#                     return cycle
            
#             rec_stack.remove(node)
#             return None
        
#         for node in group_graph:
#             if node not in visited:
#                 cycle = has_cycle(node, [])
#                 if cycle:
#                     circular_deps.append(" -> ".join(cycle))
        
#         return circular_deps
    
#     def _calculate_total_operations(self, api_calls: Dict, existing_objects: Dict) -> int:
#         """Calculate total number of operations for progress tracking"""
#         total_ops = 0
        
#         for obj_type, obj_data in api_calls.items():
#             if obj_type.endswith('_objects') and 'data' in obj_data:
#                 total_ops += len(obj_data['data'])
        
#         return total_ops
    
#     def _generate_corrective_actions(self, blocking_issues: List[str]) -> List[str]:
#         """Generate specific corrective actions for blocking issues"""
#         actions = []
        
#         for issue in blocking_issues:
#             if "API permissions" in issue:
#                 actions.append("Grant required API permissions: read/write access to object endpoints")
#             elif "naming conflicts" in issue and "overwrite mode disabled" in issue:
#                 actions.append("Enable overwrite mode with --overwrite flag to resolve naming conflicts")
#             elif "Circular dependency" in issue:
#                 actions.append("Resolve circular dependencies in object group references")
#             elif "exceed limit" in issue:
#                 actions.append("Reduce number of objects or increase FMC capacity limits")
#             elif "unknown object" in issue:
#                 actions.append("Ensure all referenced objects are included in migration or exist in FMC")
#             elif "uncorrectable" in issue:
#                 actions.append("Fix data format issues that cannot be automatically corrected")
#             else:
#                 actions.append(f"Address issue: {issue}")
        
#         return actions
    
#     def _topological_sort_dependencies(self, dependency_tree: Dict[str, List[str]]) -> List[str]:
#         """Perform topological sort on dependency tree"""
#         in_degree = {node: 0 for node in dependency_tree}
        
#         # Calculate in-degrees
#         for node, deps in dependency_tree.items():
#             for dep in deps:
#                 if dep in in_degree:
#                     in_degree[dep] += 1
        
#         # Find nodes with no dependencies
#         queue = [node for node, degree in in_degree.items() if degree == 0]
#         result = []
        
#         while queue:
#             node = queue.pop(0)
#             result.append(node)
            
#             # Remove this node and update in-degrees
#             for dep in dependency_tree.get(node, []):
#                 if dep in in_degree:
#                     in_degree[dep] -= 1
#                     if in_degree[dep] == 0:
#                         queue.append(dep)
        
#         # Check for cycles
#         if len(result) != len(dependency_tree):
#             remaining = [node for node in dependency_tree if node not in result]
#             raise Exception(f"Circular dependency detected involving: {remaining}")
        
#         return result
    
#     def _validate_corrected_value(self, value: str, obj_type: str) -> bool:
#         """Validate that a corrected value is actually valid"""
#         try:
#             if obj_type in ['Network', 'network']:
#                 import ipaddress
#                 ipaddress.ip_network(value, strict=False)
#             elif obj_type in ['Host', 'host']:
#                 import ipaddress
#                 ipaddress.ip_address(value)
#             elif obj_type in ['Range', 'range']:
#                 if '-' in value:
#                     start, end = value.split('-', 1)
#                     import ipaddress
#                     ipaddress.ip_address(start.strip())
#                     ipaddress.ip_address(end.strip())
#             return True
#         except Exception:
#             return False
    
#     def _test_object_update_capability(self, object_id: str, obj_type: str) -> Dict[str, Any]:
#         """Test if an object can be updated (without actually updating it)"""
#         result = {'updatable': True, 'reason': ''}
        
#         # Map object types to endpoints
#         endpoint_mapping = {
#             'host_objects': f'/api/fmc_config/v1/domain/{{domain_uuid}}/object/hosts/{object_id}',
#             'network_objects': f'/api/fmc_config/v1/domain/{{domain_uuid}}/object/networks/{object_id}',
#             'service_objects': f'/api/fmc_config/v1/domain/{{domain_uuid}}/object/protocolportobjects/{object_id}',
#             'object_groups': f'/api/fmc_config/v1/domain/{{domain_uuid}}/object/networkgroups/{object_id}'
#         }
        
#         endpoint = endpoint_mapping.get(obj_type)
#         if not endpoint:
#             result['updatable'] = False
#             result['reason'] = f"Unknown object type: {obj_type}"
#             return result
        
#         try:
#             # Try to GET the object to see if we can access it
#             test_result = self.make_api_call('GET', endpoint)
#             if not test_result:
#                 result['updatable'] = False
#                 if self.last_api_error:
#                     status_code = self.last_api_error.get('status_code', 0)
#                     if status_code == 403:
#                         result['reason'] = "Insufficient permissions to access object"
#                     elif status_code == 404:
#                         result['reason'] = "Object not found or no longer exists"
#                     else:
#                         result['reason'] = f"Cannot access object (HTTP {status_code})"
#                 else:
#                     result['reason'] = "Cannot access object for unknown reason"
#         except Exception as e:
#             result['updatable'] = False
#             result['reason'] = f"Exception testing object access: {e}"
        
#         return result
    
#     def _check_naming_conflicts(self, api_calls: Dict, existing_objects: Dict) -> List[Dict]:
#         """Check for naming conflicts between migration objects and existing objects"""
#         conflicts = []
        
#         type_mapping = {
#             'host_objects': 'hosts',
#             'network_objects': 'networks',
#             'service_objects': 'protocol_ports',
#             'object_groups': 'network_groups'
#         }
        
#         for obj_type, obj_data in api_calls.items():
#             if obj_type in type_mapping and 'data' in obj_data:
#                 existing_type = type_mapping[obj_type]
#                 existing_names = {obj['name'].lower() for obj in existing_objects.get(existing_type, [])}
                
#                 for obj in obj_data['data']:
#                     obj_name = obj.get('name')
#                     if obj_name and obj_name.lower() in existing_names:
#                         conflicts.append({
#                             'object_name': obj_name,
#                             'object_type': obj_type,
#                             'existing_type': existing_type
#                         })
        
#         return conflicts

#     def initialize_cache(self, force: bool = False) -> bool:
#         """Initialize the object cache at startup for optimal performance"""
#         if self._cache_initialized and not force:
#             self.log("Cache already initialized, skipping", "DEBUG")
#             return True
            
#         if not self.access_token:
#             self.log("Cannot initialize cache - not authenticated", "DEBUG")
#             return False
        
#         if getattr(self, 'skip_existing_check', False):
#             self.log("Skipping cache initialization due to fast mode", "DEBUG")
#             return True
            
#         try:
#             self.log("Initializing object cache for optimal performance...", "INFO")
#             start_time = time.time()
            
#             # Populate the cache with existing objects
#             existing_objects = self.get_existing_objects()
            
#             cache_time = time.time() - start_time
#             total_objects = sum(len(v) for v in existing_objects.values()) if existing_objects else 0
            
#             self.log(f"Cache initialized successfully in {cache_time:.1f}s with {total_objects} objects", "INFO")
#             self.log(f"  - Hosts: {len(existing_objects.get('hosts', []))}", "DEBUG")
#             self.log(f"  - Networks: {len(existing_objects.get('networks', []))}", "DEBUG")
#             self.log(f"  - Services: {len(existing_objects.get('protocol_ports', []))}", "DEBUG")
#             self.log(f"  - Groups: {len(existing_objects.get('network_groups', []))}", "DEBUG")
            
#             self._cache_initialized = True
#             return True
            
#         except Exception as e:
#             self.log(f"Failed to initialize cache: {e}", "WARN")
#             self.log("Continuing without cache initialization - performance may be reduced", "WARN")
#             return False

#     def get_cache_stats(self) -> Dict[str, Any]:
#         """Get caching system statistics"""
#         stats = {
#             'cache_valid': self._is_cache_valid(),
#             'cached_existing_objects': self._object_cache['existing_objects'] is not None,
#             'lookup_cache_size': len(self._object_cache['lookup_cache']),
#             'cache_age_seconds': 0,
#             'cache_ttl_seconds': self._object_cache['cache_ttl']
#         }
        
#         if self._object_cache['cache_timestamp']:
#             stats['cache_age_seconds'] = time.time() - self._object_cache['cache_timestamp']
        
#         if self._object_cache['existing_objects']:
#             total_objects = sum(len(v) for v in self._object_cache['existing_objects'].values())
#             stats['total_cached_objects'] = total_objects
        
#         return stats

#     def _make_creation_api_call(self, obj_type: str, obj_data: Dict) -> Optional[Dict]:
#         """Make the appropriate API call to create an object"""
#         endpoint = self.get_bulk_endpoint(obj_type)
#         if not endpoint:
#             self.log(f"Unknown object type for creation: {obj_type}", "ERROR")
#             return None
        
#         return self.make_api_call('POST', endpoint, obj_data)

#     def create_object_with_retry(self, obj_type: str, obj_data: Dict, existing_objects: Dict, max_retries: int = 10) -> Dict:
#         """Create object with guaranteed success - aggressive retry and recovery for 100% synchronization"""
        
#         original_max_retries = max_retries
#         obj_name = obj_data.get('name', 'unknown')
        
#         self.log(f"🎯 GUARANTEED SUCCESS MODE: Creating {obj_type} '{obj_name}' with up to {max_retries} retries", "DEBUG")
        
#         for attempt in range(max_retries):
#             result = self.create_object_with_validation(obj_type, obj_data, existing_objects)
            
#             if result['success']:
#                 if attempt > 0:
#                     self.log(f"✅ SUCCESS after {attempt + 1} attempts: {obj_type} '{obj_name}'", "INFO")
#                 return result
            
#             # Analyze failure reason and determine recovery strategy
#             error_message = result.get('message', '').lower()
#             self.log(f"🔄 Attempt {attempt + 1}/{max_retries} failed for '{obj_name}': {result.get('message', 'Unknown error')}", "WARN")
            
#             # Handle missing dependencies with aggressive resolution
#             if 'missing dependencies' in error_message:
#                 self.log(f"🔍 Attempting aggressive dependency resolution for '{obj_name}'...", "DEBUG")
#                 resolved = self.attempt_dependency_resolution(obj_data, existing_objects)
#                 if resolved:
#                     self.log(f"✅ Dependencies resolved for {obj_name}, retrying...")
#                     continue
#                 else:
#                     # Try cache refresh as last resort for dependencies
#                     self.log(f"🔄 Cache refresh for dependency resolution: {obj_name}", "DEBUG")
#                     self._invalidate_cache()
#                     existing_objects.update(self.get_existing_objects())
#                     continue
            
#             # Handle reference errors with multiple recovery strategies
#             elif self.last_api_error and self.last_api_error.get('status_code') == 400:
#                 error_text = str(self.last_api_error.get('response_text', '')).lower()
                
#                 if 'already exists' in error_text and self.overwrite:
#                     # Check if we've already identified this as an API inconsistency
#                     if result.get('api_inconsistency') or hasattr(self, '_phantom_objects_detected') and obj_name in self._phantom_objects_detected:
#                         self.log(f"🚨 API inconsistency detected for {obj_name} - aborting retry loop to prevent infinite attempts", "WARN")
#                         # Return a failure result but mark as recoverable
#                         return {
#                             'success': False, 
#                             'action': 'phantom_object_skip', 
#                             'object_id': None, 
#                             'message': f"Phantom object {obj_name} - FMC reports exists but unfindable via API. Skipping to prevent infinite loop.",
#                             'phantom_object': True
#                         }
                    
#                     # Force conflict resolution with targeted lookup
#                     self.log(f"🔧 Force resolving naming conflict for '{obj_name}' (attempt {attempt + 1})...", "DEBUG")
#                     conflicting_obj = self._live_lookup_object_by_name(obj_type, obj_name)
#                     if conflicting_obj:
#                         # Force update the conflicting object
#                         if self.update_existing_object(obj_type, conflicting_obj['id'], obj_data):
#                             return {'success': True, 'action': 'force_updated', 'object_id': conflicting_obj['id'], 'message': f"Force updated {obj_type}: {obj_name}"}
#                         else:
#                             self.log(f"⚠️ Update failed for found object {obj_name} on attempt {attempt + 1}", "WARN")
#                     else:
#                         # Object reported as existing but unfindable - try comprehensive search after a few attempts
#                         if attempt >= 3:
#                             self.log(f"🔍 Triggering comprehensive search for phantom object: {obj_name}", "DEBUG")
#                             comprehensive_obj = self._comprehensive_object_search(obj_type, obj_name)
#                             if comprehensive_obj:
#                                 if self.update_existing_object(obj_type, comprehensive_obj['id'], obj_data):
#                                     return {'success': True, 'action': 'updated_after_comprehensive_search', 'object_id': comprehensive_obj['id'], 'message': f"Successfully updated {obj_type}: {obj_name} after comprehensive search"}
#                             else:
#                                 self.log(f"💀 Phantom object {obj_name} remains unfindable - genuine API inconsistency", "WARN")
#                                 # Mark as phantom object to prevent future attempts
#                                 self._phantom_objects_detected.add(obj_name)
#                                 result['api_inconsistency'] = True
#                     continue
                
#                 elif 'reference' in error_text or 'not found' in error_text:
#                     self.log(f"🔍 Reference error - refreshing cache and retrying: {obj_name}", "DEBUG")
#                     # Invalidate cache and refresh
#                     self._invalidate_cache()
#                     existing_objects.update(self.get_existing_objects())
#                     continue
                
#                 elif 'invalid' in error_text:
#                     # Try data format correction again with more aggressive approach
#                     self.log(f"🔧 Attempting enhanced data format correction: {obj_name}", "DEBUG")
#                     corrected_obj = self.correct_data_format(obj_data.copy())
#                     if corrected_obj != obj_data:
#                         obj_data.update(corrected_obj)
#                         self.log(f"✅ Applied enhanced corrections to {obj_name}, retrying...")
#                         continue
            
#             # Handle rate limiting with more aggressive backoff
#             elif self.last_api_error and self.last_api_error.get('status_code') == 429:
#                 base_delay = 5  # More aggressive base delay for guaranteed success
#                 wait_time = min(base_delay * (2 ** attempt), 300)  # Cap at 5 minutes
#                 # Add jitter (±25%) to prevent thundering herd
#                 jitter = wait_time * 0.25 * (random.random() - 0.5)
#                 wait_time = max(2, wait_time + jitter)
#                 self.log(f"⏱️ Rate limited - waiting {wait_time:.1f}s for guaranteed success (attempt {attempt + 1})")
#                 time.sleep(wait_time)
#                 continue
            
#             # Handle authentication errors
#             elif self.last_api_error and self.last_api_error.get('status_code') == 401:
#                 self.log(f"🔐 Authentication failed - re-authenticating for guaranteed success")
#                 if self.authenticate():
#                     self.log(f"✅ Re-authentication successful, retrying {obj_name}")
#                     continue
#                 else:
#                     self.log(f"❌ Re-authentication failed - cannot guarantee success for {obj_name}", "ERROR")
#                     break
            
#             # Handle temporary server errors with exponential backoff
#             elif self.last_api_error and self.last_api_error.get('status_code') >= 500:
#                 base_delay = 3  # Start with 3 seconds for server errors
#                 wait_time = min(base_delay * (2 ** attempt), 180)  # Cap at 3 minutes
#                 # Add jitter (±25%) to prevent thundering herd
#                 jitter = wait_time * 0.25 * (random.random() - 0.5)
#                 wait_time = max(2, wait_time + jitter)
#                 self.log(f"🛠️ Server error - waiting {wait_time:.1f}s for recovery (attempt {attempt + 1})")
#                 time.sleep(wait_time)
#                 continue
            
#             # For other errors, use progressive wait times
#             if attempt < max_retries - 1:
#                 wait_time = min((attempt + 1) * 2, 30)  # Progressive wait, cap at 30s
#                 self.log(f"⏳ Waiting {wait_time}s before retry {attempt + 2}/{max_retries} for {obj_name}")
#                 time.sleep(wait_time)
        
#         # If we reach here, all retries failed - this violates 100% synchronization
#         error_msg = f"❌ CRITICAL: Failed to create {obj_type} '{obj_name}' after {original_max_retries} attempts - 100% synchronization cannot be guaranteed"
#         self.log(error_msg, "ERROR")
        
#         # In 100% synchronization mode, we should consider this a critical failure
#         return {
#             'success': False,
#             'action': 'failed_guaranteed_mode',
#             'message': error_msg,
#             'object_id': None,
#             'requires_rollback': True  # Flag to indicate rollback needed
#         }

#     def attempt_dependency_resolution(self, obj_data: Dict, existing_objects: Dict) -> bool:
#         """Attempt to resolve missing dependencies through live lookups"""
#         if 'objects' not in obj_data:
#             return False
        
#         resolved_count = 0
#         for ref_obj in obj_data['objects']:
#             if 'name' in ref_obj and 'id' not in ref_obj:
#                 ref_name = ref_obj['name']
#                 ref_type = ref_obj.get('type', 'Host').lower()
                
#                 # Use cache-only lookup during bulk operations to prevent rate limiting
#                 found_obj = self._find_object_in_cache_only(ref_type, ref_name, existing_objects)
#                 if found_obj:
#                     ref_obj['id'] = found_obj['id']
#                     ref_obj['type'] = found_obj['type']
#                     resolved_count += 1
#                     self.log(f"Resolved reference to {ref_name} via cache lookup", "DEBUG")
        
#         return resolved_count > 0

#     def refresh_object_cache(self, existing_objects: Dict) -> bool:
#         """Refresh the object cache with latest data from FMC"""
#         try:
#             self.log("Refreshing object cache from FMC...", "DEBUG")
#             fresh_objects = self.get_existing_objects()
            
#             # Update cache with fresh data
#             for obj_type, obj_list in fresh_objects.items():
#                 existing_objects[obj_type] = obj_list
            
#             self.log(f"Refreshed cache with {sum(len(v) for v in fresh_objects.values())} objects", "DEBUG")
#             return True
            
#         except Exception as e:
#             self.log(f"Failed to refresh object cache: {e}", "ERROR")
#             return False

#     def track_operation(self, operation_type: str, object_data: Dict, success: bool = True, error_message: str = None):
#         """Track operations for rollback capabilities"""
#         operation_record = {
#             'timestamp': time.time(),
#             'operation': operation_type,
#             'object_type': object_data.get('type', 'unknown'),
#             'object_name': object_data.get('name', 'unknown'),
#             'object_id': object_data.get('id'),
#             'success': success,
#             'error_message': error_message
#         }
        
#         if success:
#             if operation_type == 'create':
#                 self.migration_checkpoint['created_objects'].append(operation_record)
#             elif operation_type == 'update':
#                 self.migration_checkpoint['modified_objects'].append(operation_record)
#             elif operation_type == 'delete':
#                 self.migration_checkpoint['deleted_objects'].append(operation_record)
#         else:
#             self.migration_checkpoint['failed_operations'].append(operation_record)
    
#     def mark_phase_complete(self, phase_name: str, phase_results: Dict):
#         """Mark a phase as completed and save its results"""
#         self.migration_checkpoint['completed_phases'][phase_name] = {
#             'completed_at': time.time(),
#             'timestamp': datetime.datetime.now().isoformat(),
#             'results': phase_results
#         }
#         self.migration_checkpoint['phase_results'][phase_name] = phase_results
#         self.log(f"✅ Phase {phase_name} marked as complete", "INFO")
        
#         # Save checkpoint after each phase completion
#         checkpoint_file = self.create_migration_checkpoint()
#         self.log(f"📁 Phase checkpoint saved: {checkpoint_file}", "INFO")
#         return checkpoint_file
    
#     def is_phase_completed(self, phase_name: str) -> bool:
#         """Check if a phase has already been completed"""
#         return phase_name in self.migration_checkpoint['completed_phases']
    
#     def get_phase_results(self, phase_name: str) -> Dict:
#         """Get results from a completed phase"""
#         if phase_name in self.migration_checkpoint['phase_results']:
#             return self.migration_checkpoint['phase_results'][phase_name]
#         return {}
    
#     def load_checkpoint(self, checkpoint_file: str) -> bool:
#         """Load an existing migration checkpoint for resumption"""
#         try:
#             with open(checkpoint_file, 'r') as f:
#                 checkpoint_data = json.load(f)
            
#             self.migration_checkpoint = checkpoint_data
#             self.log(f"📂 Loaded checkpoint from {checkpoint_file}", "INFO")
            
#             completed_phases = list(self.migration_checkpoint.get('completed_phases', {}).keys())
#             if completed_phases:
#                 self.log(f"✅ Found completed phases: {', '.join(completed_phases)}", "INFO")
            
#             return True
#         except Exception as e:
#             self.log(f"Failed to load checkpoint {checkpoint_file}: {e}", "ERROR")
#             return False
    
#     def get_last_checkpoint_file(self) -> Optional[str]:
#         """Get the most recent checkpoint file for this session"""
#         import glob
#         session_id = self.migration_checkpoint['session_id']
#         pattern = f"migration_checkpoint_{session_id}_*.json"
#         checkpoint_files = glob.glob(pattern)
        
#         if checkpoint_files:
#             # Return the most recent checkpoint
#             return max(checkpoint_files, key=os.path.getctime)
#         return None

#     def create_migration_checkpoint(self) -> str:
#         """Create a checkpoint file for rollback purposes"""
#         import json
#         import datetime
        
#         checkpoint_filename = f"migration_checkpoint_{self.migration_checkpoint['session_id']}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
#         try:
#             with open(checkpoint_filename, 'w') as f:
#                 json.dump(self.migration_checkpoint, f, indent=2, default=str)
            
#             self.log(f"Migration checkpoint saved to: {checkpoint_filename}")
#             return checkpoint_filename
        
#         except Exception as e:
#             self.log(f"Failed to create checkpoint file: {e}", "ERROR")
#             return None
    
#     def rollback_migration(self, checkpoint_file: str = None, dry_run: bool = True) -> Dict[str, Any]:
#         """Rollback migration using checkpoint data"""
#         self.log(f"Starting migration rollback (dry_run={dry_run})...")
        
#         rollback_data = self.migration_checkpoint
        
#         # Load from checkpoint file if provided
#         if checkpoint_file:
#             try:
#                 import json
#                 with open(checkpoint_file, 'r') as f:
#                     rollback_data = json.load(f)
#                 self.log(f"Loaded rollback data from: {checkpoint_file}")
#             except Exception as e:
#                 self.log(f"Failed to load checkpoint file: {e}", "ERROR")
#                 return {'error': f"Could not load checkpoint: {e}"}
        
#         rollback_results = {
#             'objects_to_delete': len(rollback_data['created_objects']),
#             'objects_to_restore': len(rollback_data['deleted_objects']),
#             'operations_processed': 0,
#             'operations_failed': 0,
#             'dry_run': dry_run
#         }
        
#         if not self.authenticate():
#             return {'error': 'Authentication failed for rollback operation'}
        
#         # Step 1: Delete objects that were created during migration
#         self.log("Rolling back created objects...")
#         for obj_record in reversed(rollback_data['created_objects']):  # Reverse order for dependencies
#             rollback_results['operations_processed'] += 1
            
#             if dry_run:
#                 self.log(f"[DRY RUN] Would delete {obj_record['object_type']}: {obj_record['object_name']} (ID: {obj_record['object_id']})")
#             else:
#                 try:
#                     if self._rollback_delete_object(obj_record):
#                         self.log(f"Rolled back (deleted): {obj_record['object_name']}")
#                     else:
#                         rollback_results['operations_failed'] += 1
#                         self.log(f"Failed to rollback: {obj_record['object_name']}", "ERROR")
#                 except Exception as e:
#                     rollback_results['operations_failed'] += 1
#                     self.log(f"Exception during rollback of {obj_record['object_name']}: {e}", "ERROR")
        
#         # Step 2: Note about manual restoration needed for updated objects
#         if rollback_data['modified_objects']:
#             self.log(f"WARNING: {len(rollback_data['modified_objects'])} objects were modified during migration")
#             self.log("These objects cannot be automatically restored to their previous state")
#             self.log("Manual review and restoration may be required for:")
#             for obj_record in rollback_data['modified_objects']:
#                 self.log(f"  - {obj_record['object_name']} (ID: {obj_record['object_id']})")
        
#         if dry_run:
#             self.log("Rollback dry run completed. Use dry_run=False to execute actual rollback.")
#         else:
#             self.log("Rollback operation completed.")
        
#         return rollback_results
    
#     def _rollback_delete_object(self, obj_record: Dict) -> bool:
#         """Delete an object that was created during migration"""
#         obj_type = obj_record['object_type'].lower()
#         obj_id = obj_record['object_id']
        
#         if not obj_id:
#             self.log(f"Cannot rollback {obj_record['object_name']} - no object ID available", "ERROR")
#             return False
        
#         endpoint_mapping = {
#             'host': f'/api/fmc_config/v1/domain/{{domain_uuid}}/object/hosts/{obj_id}',
#             'network': f'/api/fmc_config/v1/domain/{{domain_uuid}}/object/networks/{obj_id}',
#             'service': f'/api/fmc_config/v1/domain/{{domain_uuid}}/object/protocolportobjects/{obj_id}',
#             'networkgroup': f'/api/fmc_config/v1/domain/{{domain_uuid}}/object/networkgroups/{obj_id}',
#             'object_group': f'/api/fmc_config/v1/domain/{{domain_uuid}}/object/networkgroups/{obj_id}'
#         }
        
#         endpoint = endpoint_mapping.get(obj_type)
#         if not endpoint:
#             self.log(f"Unknown object type for rollback: {obj_type}", "ERROR")
#             return False
        
#         result = self.make_api_call('DELETE', endpoint)
#         return result is not None  # DELETE operations often return empty response on success
    
#     def get_migration_summary(self) -> Dict[str, Any]:
#         """Get a summary of the current migration session"""
#         return {
#             'session_id': self.migration_checkpoint['session_id'],
#             'created_objects': len(self.migration_checkpoint['created_objects']),
#             'modified_objects': len(self.migration_checkpoint['modified_objects']),
#             'deleted_objects': len(self.migration_checkpoint['deleted_objects']),
#             'failed_operations': len(self.migration_checkpoint['failed_operations']),
#             'total_operations': (len(self.migration_checkpoint['created_objects']) + 
#                                len(self.migration_checkpoint['modified_objects']) + 
#                                len(self.migration_checkpoint['deleted_objects']) +
#                                len(self.migration_checkpoint['failed_operations']))
#         }

#     def correct_data_format(self, obj_data: Dict) -> Dict:
#         """Automatically correct common data format issues"""
#         corrected_data = obj_data.copy()
#         corrections_made = []
        
#         # Detect object type based on value format
#         detected_type_issues = []
        
#         # Handle network objects with IP range format issues
#         if corrected_data.get('type') in ['Network', 'network']:
#             if 'value' in corrected_data:
#                 original_value = corrected_data['value']
#                 corrected_value, correction_info = self._correct_network_value(original_value)
                
#                 # Check if this should be a different object type
#                 if '-' in original_value and self._is_ip_range(original_value):
#                     detected_type_issues.append({
#                         'current_type': 'Network',
#                         'suggested_type': 'Range',
#                         'reason': 'IP range format detected'
#                     })
#                     # Keep original value for Range objects
#                     corrected_value = original_value
                    
#                 elif '.' in original_value and not self._is_valid_ip_or_cidr(original_value):
#                     detected_type_issues.append({
#                         'current_type': 'Network', 
#                         'suggested_type': 'FQDN',
#                         'reason': 'Domain name detected'
#                     })
#                     # Keep original value for FQDN objects
#                     corrected_value = original_value
                
#                 if corrected_value != original_value or detected_type_issues:
#                     corrected_data['value'] = corrected_value
#                     corrections_made.append({
#                         'field': 'value',
#                         'original': original_value,
#                         'corrected': corrected_value,
#                         'reason': correction_info
#                     })
        
#         # Handle host objects that might have network ranges instead of single IPs
#         elif corrected_data.get('type') in ['Host', 'host']:
#             if 'value' in corrected_data:
#                 original_value = corrected_data['value']
#                 if '-' in original_value and self._is_ip_range(original_value):
#                     # For host objects with ranges, we need to decide how to handle this
#                     # Option 1: Convert to network object
#                     # Option 2: Use first IP in range
#                     # Option 3: Flag as error
                    
#                     start_ip, end_ip = original_value.split('-')
#                     corrected_data['value'] = start_ip.strip()
#                     corrections_made.append({
#                         'field': 'value',
#                         'original': original_value,
#                         'corrected': start_ip.strip(),
#                         'reason': 'IP range detected in host object - using start IP'
#                     })
        
#         # Handle service objects with port range format issues
#         elif corrected_data.get('type') in ['Service', 'service', 'TCP', 'UDP', 'TCPPortObject', 'UDPPortObject']:
#             if 'port' in corrected_data:
#                 original_port = corrected_data['port']
#                 corrected_port, correction_info = self._correct_port_value(original_port)
                
#                 if corrected_port != original_port:
#                     corrected_data['port'] = corrected_port
#                     corrections_made.append({
#                         'field': 'port',
#                         'original': original_port,
#                         'corrected': corrected_port,
#                         'reason': correction_info
#                     })
        
#         # Add type suggestions to corrections if needed
#         if detected_type_issues:
#             corrected_data['_type_issues'] = detected_type_issues
        
#         # Log corrections if any were made
#         if corrections_made or detected_type_issues:
#             obj_name = corrected_data.get('name', 'unknown')
            
#             # Log to console for immediate feedback
#             self.log(f"Applied automatic format corrections to '{obj_name}':")
#             for correction in corrections_made:
#                 self.log(f"  - {correction['field']}: '{correction['original']}' → '{correction['corrected']}'")
#                 self.log(f"    Reason: {correction['reason']}")
            
#             for type_issue in detected_type_issues:
#                 self.log(f"  - TYPE ISSUE: {type_issue['current_type']} → {type_issue['suggested_type']}")
#                 self.log(f"    Reason: {type_issue['reason']}")
            
#             # Log to dedicated format corrections file
#             self.log_format_correction(obj_name, corrections_made + [{'type_issues': detected_type_issues}] if detected_type_issues else corrections_made)
        
#         return corrected_data
    
#     def _correct_network_value(self, value: str) -> tuple:
#         """Correct network value format issues"""
#         value = value.strip()
        
#         # Check if it's an IP range format (x.x.x.x-y.y.y.y) - should be sent as Range, not Network
#         if '-' in value and self._is_ip_range(value):
#             # Don't convert ranges to CIDR for networks - flag this as needing Range object type
#             return value, "IP range detected - should be created as Range object, not Network"
        
#         # Check if it contains domain name (FQDN) - should be FQDN object
#         if '.' in value and not self._is_valid_ip_or_cidr(value):
#             return value, "FQDN detected - should be created as FQDN object, not Network"
        
#         # Check if it's already a valid CIDR or single IP
#         try:
#             import ipaddress
#             # Try parsing as network
#             ipaddress.ip_network(value, strict=False)
#             return value, "Already valid network format"
#         except ValueError:
#             try:
#                 # Try parsing as single IP - convert to /32 or /128
#                 ip = ipaddress.ip_address(value)
#                 if ip.version == 4:
#                     return f"{value}/32", "Single IP converted to /32 network"
#                 else:
#                     return f"{value}/128", "Single IPv6 converted to /128 network"
#             except ValueError:
#                 return value, "Unable to correct - invalid IP format"
    
#     def _is_ip_range(self, value: str) -> bool:
#         """Check if value is in IP range format (x.x.x.x-y.y.y.y)"""
#         if '-' not in value:
#             return False
        
#         parts = value.split('-', 1)
#         if len(parts) != 2:
#             return False
        
#         try:
#             import ipaddress
#             ipaddress.ip_address(parts[0].strip())
#             ipaddress.ip_address(parts[1].strip())
#             return True
#         except ValueError:
#             return False
    
#     def _is_valid_ip_or_cidr(self, value: str) -> bool:
#         """Check if value is a valid IP address or CIDR notation"""
#         try:
#             import ipaddress
#             # Try as IP address first
#             ipaddress.ip_address(value)
#             return True
#         except ValueError:
#             try:
#                 # Try as network
#                 ipaddress.ip_network(value, strict=False)
#                 return True
#             except ValueError:
#                 return False
    
#     def _convert_ip_range_to_cidr(self, ip_range: str) -> tuple:
#         """Convert IP range to CIDR notation"""
#         try:
#             import ipaddress
            
#             start_ip_str, end_ip_str = ip_range.split('-', 1)
#             start_ip = ipaddress.ip_address(start_ip_str.strip())
#             end_ip = ipaddress.ip_address(end_ip_str.strip())
            
#             # Calculate the range
#             if start_ip.version != end_ip.version:
#                 return ip_range, "Mixed IP versions - cannot convert"
            
#             # Convert to integers for calculation
#             start_int = int(start_ip)
#             end_int = int(end_ip)
            
#             if start_int > end_int:
#                 return ip_range, "Invalid range - start IP greater than end IP"
            
#             # Try to find the best CIDR representation
#             cidrs = list(ipaddress.summarize_address_range(start_ip, end_ip))
            
#             if len(cidrs) == 1:
#                 # Perfect CIDR match
#                 cidr_network = cidrs[0]
#                 return str(cidr_network), f"Converted IP range to CIDR: {cidr_network.num_addresses} addresses"
#             elif len(cidrs) <= 3:
#                 # Multiple CIDRs but manageable - use the first one and note the limitation
#                 primary_cidr = str(cidrs[0])
#                 total_addresses = sum(cidr.num_addresses for cidr in cidrs)
#                 return primary_cidr, f"Range spans {len(cidrs)} CIDRs ({total_addresses} addresses) - using primary: {primary_cidr}"
#             else:
#                 # Too many CIDRs - calculate a covering supernet
#                 # Find the smallest network that covers the range
                
#                 # Try a more systematic approach - check common subnet boundaries
#                 # For IPv4, check standard subnet sizes
#                 if start_ip.version == 4:
#                     # Check if it matches standard subnet boundaries
#                     num_ips = end_int - start_int + 1
                    
#                     # Check powers of 2 for subnet size
#                     import math
#                     if num_ips & (num_ips - 1) == 0:  # Is power of 2
#                         prefix_len = 32 - int(math.log2(num_ips))
                        
#                         # Check if start IP aligns with this subnet boundary
#                         subnet_size = 2**(32 - prefix_len)
#                         if start_int % subnet_size == 0:
#                             cidr = f"{start_ip}/{prefix_len}"
#                             return cidr, f"Perfect CIDR match: {cidr} ({num_ips} addresses)"
                
#                 # Find the smallest supernet that covers this range
#                 for prefix_len in range(32 if start_ip.version == 4 else 128, -1, -1):
#                     try:
#                         # Try creating a network starting from start_ip
#                         network = ipaddress.ip_network(f"{start_ip}/{prefix_len}", strict=False)
#                         if network.network_address <= start_ip and network.broadcast_address >= end_ip:
#                             return str(network), f"Range approximated as {network} (may include additional IPs)"
#                     except ValueError:
#                         continue
                
#                 # Fallback - use the start IP as a /32 or /128
#                 suffix = "/32" if start_ip.version == 4 else "/128"
#                 return f"{start_ip}{suffix}", "Complex range - using start IP only"
                
#         except Exception as e:
#             return ip_range, f"Conversion failed: {str(e)}"
    
#     def _correct_port_value(self, port_value) -> tuple:
#         """Correct port value format issues"""
#         if isinstance(port_value, int):
#             return port_value, "Already valid integer port"
        
#         port_str = str(port_value).strip()
        
#         # Handle port ranges
#         if '-' in port_str:
#             parts = port_str.split('-', 1)
#             try:
#                 start_port = int(parts[0].strip())
#                 end_port = int(parts[1].strip())
                
#                 if start_port == end_port:
#                     return start_port, "Port range with same start/end converted to single port"
#                 else:
#                     # Return as range - FMC should accept this format
#                     return f"{start_port}-{end_port}", "Port range formatted"
#             except ValueError:
#                 return port_value, "Invalid port range format"
        
#         # Handle single port
#         try:
#             port_int = int(port_str)
#             if 0 <= port_int <= 65535:
#                 return port_int, "Port converted to integer"
#             else:
#                 return port_value, "Port number out of valid range (0-65535)"
#         except ValueError:
#             return port_value, "Invalid port format"

# def main():
#     """Main function for FMC API executor"""
#     if len(sys.argv) < 2:
#         print("Usage: python fmc_api_executor.py <migration_config.json> [--validate-only] [--overwrite] [--fast]")
#         print("       python fmc_api_executor.py --interactive")
#         print("       python fmc_api_executor.py --rollback <checkpoint_file> [--dry-run]")
#         print("       python fmc_api_executor.py --resume [checkpoint_file]")
#         print("")
#         print("Options:")
#         print("  --fast             Skip existing objects check for faster migration")
#         print("  --validate-only    Only validate configuration, don't migrate")
#         print("  --overwrite        Overwrite existing objects with same names")
#         print("  --resume           Resume migration from last checkpoint or specified checkpoint file")
#         sys.exit(1)
    
#     # Handle rollback operations
#     if sys.argv[1] == '--rollback':
#         if len(sys.argv) < 3:
#             print("Error: Rollback requires a checkpoint file")
#             print("Usage: python fmc_api_executor.py --rollback <checkpoint_file> [--dry-run]")
#             sys.exit(1)
        
#         checkpoint_file = sys.argv[2]
#         dry_run = '--dry-run' in sys.argv
        
#         # Use hardcoded credentials for rollback (you may want to make this configurable)
#         fmc_host = "https://*************"
#         username = "admin"
#         password = "!Techn0l0gy01!"
        
#         executor = FMCAPIExecutor(fmc_host, username, password, verify_ssl=False, overwrite=dry_run)
        
#         print(f"\n🔄 Starting rollback operation from checkpoint: {checkpoint_file}")
#         print(f"Dry run mode: {'ENABLED' if dry_run else 'DISABLED'}")
        
#         rollback_results = executor.rollback_migration(checkpoint_file, dry_run)
        
#         if 'error' in rollback_results:
#             print(f"❌ Rollback failed: {rollback_results['error']}")
#             sys.exit(1)
        
#         print(f"\n📊 Rollback Results:")
#         print(f"Objects to delete: {rollback_results['objects_to_delete']}")
#         print(f"Operations processed: {rollback_results['operations_processed']}")
#         print(f"Operations failed: {rollback_results['operations_failed']}")
        
#         if rollback_results['operations_failed'] == 0:
#             print("✅ Rollback completed successfully!")
#         else:
#             print(f"⚠️ Rollback completed with {rollback_results['operations_failed']} failures")
        
#         sys.exit(0)
    
#     # Handle resume operations
#     if sys.argv[1] == '--resume':
#         checkpoint_file = None
#         if len(sys.argv) >= 3 and not sys.argv[2].startswith('--'):
#             checkpoint_file = sys.argv[2]
        
#         # Use hardcoded credentials for resume (you may want to make this configurable)
#         fmc_host = "https://*************"
#         username = "admin"
#         password = "!Techn0l0gy01!"
        
#         overwrite = '--overwrite' in sys.argv
#         fast_mode = '--fast' in sys.argv
        
#         executor = FMCAPIExecutor(fmc_host, username, password, verify_ssl=False, overwrite=overwrite)
#         if fast_mode:
#             executor.skip_existing_check = True
        
#         # Try to find and load checkpoint
#         if checkpoint_file:
#             if not os.path.exists(checkpoint_file):
#                 print(f"❌ Error: Checkpoint file '{checkpoint_file}' not found")
#                 sys.exit(1)
#         else:
#             # Find the most recent checkpoint
#             import glob
#             checkpoint_files = glob.glob("migration_checkpoint_*.json")
#             if not checkpoint_files:
#                 print("❌ Error: No checkpoint files found")
#                 print("Available files:")
#                 for f in os.listdir('.'):
#                     if f.startswith('migration_checkpoint_'):
#                         print(f"  - {f}")
#                 sys.exit(1)
#             checkpoint_file = max(checkpoint_files, key=os.path.getctime)
        
#         print(f"\n🔄 Resuming migration from checkpoint: {checkpoint_file}")
        
#         if not executor.load_checkpoint(checkpoint_file):
#             print(f"❌ Failed to load checkpoint file")
#             sys.exit(1)
        
#         # Find config file - look for recent migration config files
#         config_file = None
#         for arg in sys.argv:
#             if arg.endswith('.json') and not arg.startswith('migration_checkpoint_'):
#                 config_file = arg
#                 break
        
#         if not config_file:
#             # Try to find a recent migration config file
#             config_candidates = ['fmc_migration_config.json', 'demo_migration_config.json']
#             for candidate in config_candidates:
#                 if os.path.exists(candidate):
#                     config_file = candidate
#                     break
        
#         if not config_file:
#             print("❌ Error: Could not find migration config file")
#             print("Usage: python fmc_api_executor.py --resume [checkpoint_file] [config_file] [options]")
#             print("Or ensure fmc_migration_config.json exists in current directory")
#             sys.exit(1)
        
#         print(f"📄 Using configuration file: {config_file}")
        
#         # Execute migration with resume capability
#         print("🚀 Resuming migration execution...")
#         results = executor.execute_migration_config_improved(config_file)
        
#         # Print results summary
#         success_count = (results.get('host_objects_created', 0) +
#                         results.get('host_objects_updated', 0) +
#                         results.get('network_objects_created', 0) +
#                         results.get('network_objects_updated', 0) +
#                         results.get('service_objects_created', 0) +
#                         results.get('service_objects_overwritten', 0) +
#                         results.get('object_groups_created', 0) +
#                         results.get('object_groups_overwritten', 0))
        
#         error_count = results.get('errors', 0)
        
#         print(f"\n📊 Resume Results:")
#         print(f"✅ Successfully processed: {success_count} objects")
#         print(f"❌ Errors: {error_count}")
        
#         if error_count == 0:
#             print("🎉 Migration resumed and completed successfully!")
#         else:
#             print("⚠️ Migration resumed but encountered some errors. Check logs for details.")
        
#         sys.exit(0)
    
#     if sys.argv[1] == '--interactive':
#         # Interactive mode
#         print("FMC API Executor - Interactive Mode")
#         print("=" * 40)
        
#         fmc_host = input("FMC Host (e.g., https://fmc.example.com): ").strip()
#         username = input("Username: ").strip()
#         password = input("Password: ").strip()
#         config_file = input("Migration config file (default: fmc_migration_config.json): ").strip()
        
#         if not config_file:
#             config_file = "fmc_migration_config.json"
        
#         validate_only = input("Validate only? (y/N): ").strip().lower() == 'y'
#         overwrite = input("Overwrite existing objects? (y/N): ").strip().lower() == 'y'
        
#     else:
#         # Command line mode
#         config_file = sys.argv[1]
#         validate_only = '--validate-only' in sys.argv
#         overwrite = '--overwrite' in sys.argv
#         fast_mode = '--fast' in sys.argv
        
#         # You would typically set these via environment variables or config file
#         fmc_host = "https://*************"  # Update this
#         username = "admin"                   # Update this  
#         password = "!Techn0l0gy01!"         # Update this
    
#     # Set fast mode if specified (skip existing objects check for speed)
#     skip_existing = fast_mode if 'fast_mode' in locals() else False
#     executor = FMCAPIExecutor(fmc_host, username, password, verify_ssl=False, overwrite=overwrite, skip_existing_check=skip_existing)
    
#     if validate_only:
#         print("\nValidating migration configuration...")
#         validation = executor.validate_migration(config_file, offline_mode=True)
        
#         if 'error' in validation:
#             print(f"❌ Validation failed: {validation['error']}")
#             sys.exit(1)
        
#         # Validate data formats and show correction opportunities
#         print(f"\n🔧 Data Format Validation:")
#         format_validation = executor.validate_data_formats(config_file)
        
#         if 'error' in format_validation:
#             print(f"⚠️ Format validation failed: {format_validation['error']}")
#         else:
#             correctable_count = format_validation.get('objects_to_correct', 0)
#             if correctable_count > 0:
#                 print(f"Found {correctable_count} objects with correctable format issues:")
                
#                 # Show summary by correction type
#                 for issue_type, count in format_validation.get('correction_summary', {}).items():
#                     print(f"  - {issue_type}: {count} objects")
                
#                 print(f"\n🔧 Format Corrections (showing first 10):")
#                 for i, issue in enumerate(format_validation.get('correctable_issues', [])[:10]):
#                     print(f"  {i+1}. {issue['object_name']} ({issue['object_type']})")
#                     print(f"     {issue['field']}: '{issue['original']}' → '{issue['corrected']}'")
                
#                 if len(format_validation.get('correctable_issues', [])) > 10:
#                     remaining = len(format_validation['correctable_issues']) - 10
#                     print(f"     ... and {remaining} more")
                
#                 print(f"✅ All format issues will be automatically corrected during migration")
#             else:
#                 print(f"✅ No format issues detected - all data appears correctly formatted")
        
#         # Print pagination check results
#         print(f"\n📋 Pagination Check:")
#         if 'offline_mode' in validation['pagination_check']:
#             print(f"  📴 Offline Mode: {validation['pagination_check']['offline_mode']}")
#         else:
#             for obj_type, check_info in validation['pagination_check'].items():
#                 status = "✅" if check_info['appears_complete'] else "⚠️"
#                 print(f"  {status} {obj_type}: {check_info['note']}")
        
#         # Print summary
#         summary = validation['summary']
#         print(f"\n📊 Validation Summary:")
#         print(f"Total objects to create: {validation.get('total_objects_to_create', 0)}")
#         print(f"Safe to migrate: {summary['total_safe']}")
#         print(f"Conflicts found: {summary['total_conflicts']}")
#         print(f"Dependency issues: {summary['total_dependency_issues']}")
#         print(f"Warnings: {summary['total_warnings']}")
#         if correctable_count > 0:
#             print(f"Format corrections: {correctable_count} (automatic)")
        
#         # Print conflicts
#         if validation.get('conflicts'):
#             print(f"\n🔴 Conflicts ({len(validation['conflicts'])}):")
#             for conflict in validation['conflicts']:
#                 print(f"  - {conflict['type']}: {conflict['name']} - {conflict['reason']}")
#                 if 'existing_id' in conflict:
#                     print(f"    Existing object ID: {conflict['existing_id']}")
        
#         # Print dependency issues
#         if validation.get('dependency_issues'):
#             print(f"\n🔗 Dependency Issues ({len(validation['dependency_issues'])}):")
#             for issue in validation['dependency_issues']:
#                 print(f"  - Group: {issue['group_name']}")
#                 print(f"    Missing dependencies: {len(issue['missing_dependencies'])}")
#                 for dep in issue['missing_dependencies']:
#                     dep_name = dep.get('name', dep.get('id', 'unknown'))
#                     resolution = issue['can_resolve_in_session'].get(dep_name, {})
#                     status = "✅ Will be created" if resolution.get('will_be_created') else "❌ Not in migration"
#                     print(f"      - {dep_name} ({dep['type']}) - {status}")
        
#         # Print warnings
#         if validation.get('warnings'):
#             print(f"\n⚠️ Warnings ({len(validation['warnings'])}):")
#             for warning in validation['warnings']:
#                 print(f"  - {warning}")
        
#         # Migration readiness assessment
#         print(f"\n🎯 Migration Readiness:")
        
#         total_issues = summary['total_conflicts'] + summary['total_dependency_issues']
#         if total_issues == 0:
#             print("✅ Ready to migrate - no conflicts or dependency issues detected")
#             if correctable_count > 0:
#                 print(f"🔧 {correctable_count} format issues will be automatically corrected")
#         else:
#             if summary['overwrite_required']:
#                 if overwrite:
#                     print(f"🔄 {summary['total_conflicts']} conflicts will be resolved with --overwrite flag")
#                 else:
#                     print(f"⚠️ {summary['total_conflicts']} conflicts require --overwrite flag to resolve")
            
#             if summary['dependency_resolution_needed']:
#                 resolvable_deps = 0
#                 unresolvable_deps = 0
#                 for issue in validation.get('dependency_issues', []):
#                     for dep_name, resolution in issue['can_resolve_in_session'].items():
#                         if resolution.get('will_be_created'):
#                             resolvable_deps += 1
#                         else:
#                             unresolvable_deps += 1
                
#                 if unresolvable_deps == 0:
#                     print(f"✅ All {resolvable_deps} dependency issues can be resolved during migration")
#                 else:
#                     print(f"❌ {unresolvable_deps} dependency issues cannot be resolved (missing objects not in migration)")
#                     print(f"✅ {resolvable_deps} dependency issues will be resolved during migration")
        
#         # Performance estimates
#         total_api_calls = validation.get('total_objects_to_create', 0)
#         if overwrite and summary['total_conflicts'] > 0:
#             total_api_calls += summary['total_conflicts'] * 2  # Delete + Create for each conflict
        
#         estimated_time = (total_api_calls * 0.5) / 60  # 0.5 seconds per call average
#         print(f"\n⏱️ Performance Estimate:")
#         print(f"Estimated API calls: {total_api_calls}")
#         print(f"Estimated completion time: {estimated_time:.1f} minutes")
        
#         if summary['total_warnings'] > 0 or correctable_count > 0:
#             print(f"\n💡 Recommendations:")
#             if any("pagination" in w.lower() for w in validation.get('warnings', [])):
#                 print("  - Review pagination warnings - some existing objects may not have been detected")
#                 print("  - Consider running validation again to ensure completeness")
#             if summary['dependency_resolution_needed']:
#                 print("  - Review dependency issues before proceeding")
#                 print("  - Ensure referenced objects will be created or already exist")
#             if correctable_count > 0:
#                 print(f"  - {correctable_count} format issues detected but will be automatically corrected")
#                 print("  - Review format corrections above to ensure they meet your requirements")
    
#     else:
#         print(f"\nExecuting migration from {config_file}...")
#         if overwrite:
#             print("🔄 Overwrite mode enabled - existing objects will be updated/replaced")
        
#         results = executor.execute_migration_config(config_file)
        
#         print("\nMigration Results:")
#         print("=" * 40)
        
#         # Categorize results for better reporting
#         success_count = (results.get('host_objects_created', 0) + 
#                         results.get('host_objects_updated', 0) +
#                         results.get('network_objects_created', 0) + 
#                         results.get('network_objects_updated', 0) +
#                         results.get('service_objects_created', 0) + 
#                         results.get('service_objects_overwritten', 0) +
#                         results.get('object_groups_created', 0) + 
#                         results.get('object_groups_overwritten', 0))
        
#         skipped_count = (results.get('host_objects_skipped', 0) +
#                         results.get('network_objects_skipped', 0) +
#                         results.get('service_objects_skipped', 0) +
#                         results.get('object_groups_skipped', 0))
        
#         error_count = results.get('errors', 0)
#         warning_count = results.get('warnings', 0)
        
#         # Print detailed results
#         print(f"✅ Successfully processed: {success_count} objects")
#         if results.get('host_objects_created', 0) > 0:
#             print(f"   - Host objects created: {results['host_objects_created']}")
#         if results.get('host_objects_updated', 0) > 0:
#             print(f"   - Host objects updated: {results['host_objects_updated']}")
#         if results.get('network_objects_created', 0) > 0:
#             print(f"   - Network objects created: {results['network_objects_created']}")
#         if results.get('network_objects_updated', 0) > 0:
#             print(f"   - Network objects updated: {results['network_objects_updated']}")
#         if results.get('service_objects_created', 0) > 0:
#             print(f"   - Service objects created: {results['service_objects_created']}")
#         if results.get('service_objects_overwritten', 0) > 0:
#             print(f"   - Service objects overwritten: {results['service_objects_overwritten']}")
#         if results.get('object_groups_created', 0) > 0:
#             print(f"   - Object groups created: {results['object_groups_created']}")
#         if results.get('object_groups_overwritten', 0) > 0:
#             print(f"   - Object groups overwritten: {results['object_groups_overwritten']}")
        
#         if skipped_count > 0:
#             print(f"\n⏭️ Skipped due to dependencies: {skipped_count} objects")
#             if results.get('host_objects_skipped', 0) > 0:
#                 print(f"   - Host objects skipped: {results['host_objects_skipped']}")
#             if results.get('network_objects_skipped', 0) > 0:
#                 print(f"   - Network objects skipped: {results['network_objects_skipped']}")
#             if results.get('service_objects_skipped', 0) > 0:
#                 print(f"   - Service objects skipped: {results['service_objects_skipped']}")
#             if results.get('object_groups_skipped', 0) > 0:
#                 print(f"   - Object groups skipped: {results['object_groups_skipped']}")
        
#         if warning_count > 0:
#             print(f"\n⚠️ Warnings: {warning_count}")
        
#         if error_count > 0:
#             print(f"\n❌ Errors: {error_count}")
        
#         # Overall status
#         if error_count == 0 and skipped_count == 0:
#             print("\n🎉 Migration completed successfully!")
#         elif error_count == 0:
#             print(f"\n✅ Migration completed with {skipped_count} objects skipped due to missing dependencies")
#             print("💡 Consider resolving dependencies and re-running for complete migration")
#         else:
#             print(f"\n⚠️ Migration completed with {error_count} errors and {skipped_count} skipped objects")
#             print("💡 Review the logs above for detailed error information")
        
#         # Transaction safety information
#         session_summary = results.get('session_summary', {})
#         if session_summary.get('total_operations', 0) > 0:
#             print(f"\n🔒 Transaction Safety:")
#             print(f"Operations tracked: {session_summary['total_operations']}")
#             if results.get('checkpoint_file'):
#                 print(f"Rollback checkpoint: {results['checkpoint_file']}")
#                 print(f"To rollback: python fmc_api_executor.py --rollback {results['checkpoint_file']}")
        
#         # Display log file locations
#         if hasattr(executor, 'log_files'):
#             print(f"\n📋 Log Files Created:")
#             for log_type, log_path in executor.log_files.items():
#                 if os.path.exists(log_path) and os.path.getsize(log_path) > 0:
#                     size_kb = os.path.getsize(log_path) / 1024
#                     print(f"  - {log_type.title()}: {log_path} ({size_kb:.1f} KB)")
                    
#                     # Show purpose of each log file
#                     if log_type == 'main':
#                         print(f"    Purpose: Complete migration activity log")
#                     elif log_type == 'errors':
#                         print(f"    Purpose: Error-only log for troubleshooting")
#                     elif log_type == 'corrections':
#                         print(f"    Purpose: Data format corrections applied")
#                     elif log_type == 'debug':
#                         print(f"    Purpose: Detailed API calls and responses")
        
#         # Performance calculation removed - start_time not available in this scope
        
#         print(f"\n💡 Next Steps:")
#         if error_count > 0:
#             print(f"  - Review error log: {executor.log_files.get('errors', 'N/A')}")
#             print(f"  - Check debug log for API details: {executor.log_files.get('debug', 'N/A')}")
#         if results.get('checkpoint_file'):
#             print(f"  - Rollback available if needed")
#         print(f"  - All logs preserved for audit trail")
#         print(f"  - Main log contains complete session summary")

# if __name__ == "__main__":
#     main() 