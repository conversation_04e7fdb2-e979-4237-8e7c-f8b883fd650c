{"session_id": "migration_1754298041", "timestamp": "2025-08-04T02:00:41.316115", "connection_type": "custom", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 0, "failed": 629, "skipped": 0, "details": ["❌ Failed to create host: RadSaratoga - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: RadAmsMem - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: RadStMarys - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: RadSeton - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: RadBellevue - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CITRIXFS02 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: XENAPP30 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MAGIC_C-iDRAC - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHNAS12 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MAGIC-D_iDRAC - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: XENAPP02 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHNAS11 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MAGIC_A-iDRAC - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MAGIC_E-iDRAC - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MAGIC_D-NEWiSCSI - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: UNITY-SDC_iSCSI - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLH-WEB01-WS01 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MAGICA - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MAGICC - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MAGICD - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MAGICE - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MAGICB - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MAGICF - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MAGICG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DICTATION02 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MDILIVE.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_FS_SEC - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CTScanner - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Mammo - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: RandF - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PetLinks1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PetLinks2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MAGICD3_DRAC - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LIEBERT.2FL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Cardinal132 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Cardinal133 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Cardinal144 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Cardinal145 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Cardinal176 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Cardinal177 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Cardinal194 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Cardinal195 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Medinote - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: medinote2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: medinote1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NATHAN3.dmz - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NATHAN5.dmz - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NATHAN1.dmz - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NATHAN4.dmz - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NATHAN9.dmz - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NATHAN10.dmz - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NATHAN11.dmz - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MilleniumPACS2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MilleniumPACS1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MilleniumPACS3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MilleniumPACS4 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MilleniumPACS5 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHEXCHANGE.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLH-ISWEB_VIP_NETSCALER1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PACS - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PACS_CACHE - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PACS_STORE1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PACS_STORE2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PACS_STORE144 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: ResnickPacs1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: ResnickPACS2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: TeleRadPC - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CatScan - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PERTH_MRI - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PETScanCT - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: XELERIS - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: INFINIA - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: D5000 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Ultrasound1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Ultrasound2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Ultrasound3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: KonicaJM - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Konicardr1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: KonicaRdr2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: KonicaRdr3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PACS_NEW - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: US_LOGI_E9 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NexTalk242 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NexTalk243 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NexTalk244 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NexTalk245 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NexTalk246 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NexTalk247 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NexTalkPrime - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NexTalkSec - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Spantel.Prod - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SpantelHL7.test - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: eRXcenter2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: eRXcenter3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: eRXcenter1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: eRxChicago - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: eRxDallas - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MedentRemote - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Medent.RPTS - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: STpc.rtr - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: spc.rtr - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: ppc.pix - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.ps1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.ps2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.ps3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.ps4 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.syn1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.syn2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.orpc1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.orpc2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.orpc3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.read1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.read2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.read3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.KPServer - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.read4 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: smha.mammo - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: smha.pacsed30 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: smha.pacrd06 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.read5 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.read6 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SHMA.read7 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.read8 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.read9 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.read10 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.Synapse.Dest - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P-DI-MGR - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PACS_READ3_NEW - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_CIO1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_DI_NUMED - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MAMMO40 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MOMMO41 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: philipstst - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHSP19WEB.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_PAT_REP1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_PAT_REP5 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_PCC_BILL1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_PAT_REP6 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_PAT_REP3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_PAT_REP4 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SOPHOSEMAIL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SOPHOSWEB - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NATHAN6.dmz - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: ORTIZ_LT - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: p_mis_netadmin - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PACS_OR3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PACS_OR1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PACS_OR2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PACS_DI - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHDC1_IPMI - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHDC2_IPMI - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: AAI.120 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: AAI.124 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: AAI.125 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: AAI.52 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: INTERLACE - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHUTILITY - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHTEST01-NIC2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: BPC-UPS - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: ALBANYMED.IN.2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: ALBANYMED.IN - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: hixny.com_integration - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: hixny.com_prod - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: webservices.hixny.com - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MDITEST - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MEDENT - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MEDENT03 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLH-ISWEB_VIRTUALIP - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLH-ISWEB_VIRTUALIP_NETSCALER2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MEDENT05 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_PHA_WS3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_PHA_WS2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_MR_SCAN1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: easyeeg - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: VENUE50_p_pacs_cdburn - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: integration.hixny.com - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Hixney.net_2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CITRIX_STOREFRONT - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P-IT-MGR - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NETSCALER.VPX - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NETSCALER.WEB - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NETSCALERSUBNETIP - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHDC01.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHDC02.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_CISCO_01 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: p_mis_netadmin2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Direct.Hixny.Com - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Healthstream.SMPT.Peer - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Hixny.net - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Hixny.com - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: statrad.hl7.test - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_PAT_FIN - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHENDO01 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHENDO01.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: ENDOWORKS02 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: ENDOWORKS03 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_IS_PACS - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: retsolinc2.com - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: retsolinc3.com - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SophosMailExt - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: IRIS - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: smtp.biz.rr.com - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Hypertype - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MVP - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LeaderHFTPsite - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LeaderHFTPsite2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: stentor.com - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: TOGARM - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Infotrak - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: sftp.lifethc.org - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: TeleVideo1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Televid2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CONNECTPLUS01 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: VeriquestPC - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: VeriquestSite - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PATIENT_PORTAL_1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: HYPER-_REPLICA_BROKER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Sodexho - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Provation-out - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: VeriquestServer - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Harland - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: IMO_2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: WWW.UPTODATE.COM - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: XENAPP22 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: HYPER-V_CLUSTER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PATIENTPORTAL.EXTERNAL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: remote.nlh.org - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: mail.nlh.org - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DIRECT.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: TeleMed_1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MDI.dmz - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHCISCO - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LabCorp3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LabCorpDev - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LabCorpProd - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: TheOutsourceGroup - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: TeleradIT_Millenium1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: TeleradIT_Millenium2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: <PERSON>Chart.Inside - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: <PERSON>.inside - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: STATRAD.DR.SVR - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHTEST01 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLH.ORG.EXTERNAL.FORMS - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: obj-************ - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: obj-*********** - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: obj-*********** - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: obj-************ - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: obj-*********** - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: obj-************ - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: obj-************ - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: <PERSON><PERSON>.New - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: HEALTHTOUCH.PEER.INTERNAL.1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Medent.Peer.New. - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: HIXNY.MBMS.MILLENIUMBILLING.PEER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: HIXNY.MBMS.MILLENIUMBILLING.INTERNAL1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MCKESSON.MC.PHARM - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: newsync3.mkesson.com - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: obj-0.0.0.0 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.pacs1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.pacs2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.pacs3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PACS.VCE1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PACS.VCE2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PACS.VCE3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PACS.VCE4 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MEDENT_NAS_INTERNAL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: HEALTHTOUCH01 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: HEALTHTOUCH02 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PDX.Internal - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PDX.External - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: HIXNY.PEER.NEW - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: HIXNY.INTERNAL1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: XCHANGEWORX.PEER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NETSCALER.NLHRESTAPI - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NUVODIA_VPN_NLH_PEER1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NUVODIA_VPN_NLH_PEER2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: FIREPOWER_VM_ESXI - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.READ.10 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: ESRS_EMC_VIRTUAL_APPLIANCE - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLH-ISWEB.INTERNAL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLH-ISWEB.DMZ - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: RESTFULAPI.DMZ - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NYOH.INTERNAL.1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NYOH.INTERNAL.2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NYOH.EXTERNAL.PEER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NUVODIA.INTERNAL.1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NUVODIA.INTERNAL.2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_MIS52_DMZ - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MDITEST_SENDTRYDS - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: XENAPP25 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: skype.nlh.org_external - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: st_netadmin - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.RAD.EXTERNAL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MVO_AMST_PEER_NEW - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: GUEST_INTERFACE_EXTERNAL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: VENDOR_EXTERNAL_INTERFACE - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: p_mis_netadmin.dmz - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLH-ISWEB.DMZVR - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: AMC.PACS.NEW - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: BRIAN_DHCP - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: BPC.External - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_MIS_CISCOMON - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: XENAPP17 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: XENAPP18 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: XENAPP19 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_MIS52.WAYNE - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NETADMIN.DMZ.TEST - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: EUGENE10 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MEDITECHAPIVIP1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MEDITECHAPIVIP2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: StratSolution.Peer - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_PHA_PDX1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHPRTG01 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: RCARE-SERVER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHDMZ01_SWITCH - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: XENAPP01 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PRTG.NLH.ORG.EXTERNAL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: BACKLINE.VPN.PEER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: UNITEDLABNETWORK.VPN.PEER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NETWORK_OBJ_18.204.173.205 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: BACKLINE.LDAP.INTERNAL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MEDENT.NIMBLE.INSIDE.1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MEDENT.NIMBLE.OPENVPN.OUTSIDE.1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MEDENT.NIMBLE.OPENVPN.OUTSIDE.2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: ROBOT_GE_VOT_TRAIN - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: BILL_BAIRD - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS29-iDRAC - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DOLBEY - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DOLBEYTEST - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLH_DCDS_9300s - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Schumacher.Inside1.new.ADTPROD - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Schumacher.Inside2.new.ADTTEST - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Schumacher.VPN.Peer.New - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MEDENT-EXPORT - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: QUEST.VPN.PEER.2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: QUEST.VPN.INTERNAL.2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: <PERSON> <PERSON> <PERSON><PERSON> during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: HIXNY.PEER.INTERNAL.TEST - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: HIXNY.PEER.INTERNAL.PROD - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHSP19OFCWEB.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PATIENTPORTAL.DMZ - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: mtrestexpapis-live01.nlh.org.external - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: mtrestexpapis-test01.nlh.org.external - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: mtrestexpapis-test01.nlh.org.DMZ - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: mtrestexpapis-live01.nlh.org.DMZ - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CHANGE.HEALTHCARE.EXTERNAL.PEER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CHANGE.HEALTHCARE.EXTERNAL.IP1.PROD - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CHANGE.HEALTHCARE.EXTERNAL.IP2.TEST - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLI.T.BG01 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CHC.EXTERNAL.1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CHC.EXTERNAL.2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLI-T-BG01.CHC.NAT - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MDILIVE.CHC.NAT - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MDITEST.CHC.NAT - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLI-T-BG01 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHFTP01.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SR_STACK_01 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLI-BG01.nlh.org - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLI-BG04.CHC.NAT - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLI-BG04 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CHC.EXTERNAL.3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NYOH.INTERNAL.3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: WEBSSO.MEDITECH.COM - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: WEBSSO2FA.MEDITECH.COM - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: HIXNY.INTERNAL.PUSH_SERVER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: HIXNY.INTERNAL.TESTHUB - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: FIRECALL_JSC - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: FIRECALLSYSTEM_ENDPOINTS1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: FIRECALLSYSTEM_ENDPOINTS2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: BACKLINE.VPN.PEER2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: BACKLINE.LDAP.INTERNAL2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NETWORK_OBJ_35.155.201.32 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: BANDWIDTH_TEST - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_IS_1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_IS_3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_IT_COOR - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_IT_TECH1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: HIRAM - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: RYAN - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NICK - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: IT_TEST - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P-BOARDROOM1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS08 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS21-iLO - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHADMINCENTER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: XENAPP24 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SQL01.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: FAXSERVER.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHFUSION - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: BACKUPEXEC01 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: ARCHIVE.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PRINT - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHBACKUP - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: INTERLACETEST - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHMONITOR01 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SANPHNHM - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CENTRALINK_BCR - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CENTRALINK_VISTA2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CENTRALINK_VISTA1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CENTRALINK_LCM - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CENTRALINK - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS31-iDRAC - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: XENAPP21 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHCITRIXGATEWAY - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: ST_NETADMIN2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DR_CECIL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P_IS_RAMANI - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: US_LOGU_E9_2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NYOH.INTERNAL.4 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLI-BG13 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHTESTMOBILE - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NOVA.NLH.ORG.EXTERNAL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: BANDWIDTH_TEST_2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NETWORK_OBJ_192.168.253.161 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NETWORK_OBJ_172.16.41.10 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Barracuda.Web.NLH.Internal - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Barracuda.Email.NLH.Internal - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: HEALTHTOUCH.EXTERNAL.PEER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: HEALTHTOUCH.PEER.INTERNAL.2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NETWORK_OBJ_216.41.86.228 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DMZ_TEST.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DUOTEST.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DUOTEST.NLH.ORG.DMZ - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: BARRACUDA.EMAIL.INSIDE - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLH.CORE.INTERNAL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DCDS.CORE.INTERNAL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: GPC_STACK - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHSSI - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHBRAUNPUMPS.INTERNAL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHBRAUNPUMPS.EXTERNAL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: P-ITMGR - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MEDIVATOR66838147 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MEDIVATOR66838143 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: AMC.VPN.PEER.NEW - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NUVODIA.INTERNAL.NEW.1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NUVODIA.INTERNAL.NEW.2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: ULN.VPN.INTERNAL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CISCOPRIME.INTERNAL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CISCOPRIMEINF - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MIS_TEST2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CISCONMON - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SYSLOGSERVER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NOVA-QIE.INTERNAL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NOVA.INTERLACE.PEER.EXTERNAL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: WLC1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: sendgrid.net.virus - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NOVA.INTERLACE.PEER.EXTERNAL2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: love.explorethebest.com.spam.2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: love.explorethebest.com.spam.1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: love.explorethebest.com.spam.3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CISCO.WSA.INTERNAL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: HARRIET.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS32.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS10A.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS19B.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: WILLYWONKA.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHSYN01.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHSYN02.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHSYN03.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHSYN04.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHSP19APP.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS18C.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS19C.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS14.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS26D.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DDPC.FIREALARM - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCUIS16B.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS17B.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS19A.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SUMMIT.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS25A.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: ONEVIEW.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DR1.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS26B.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHBACKUP02.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: KRONOSNEW.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SMHA.RAD.EXTERNAL.NEW - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: BARRACUDA.LDAP.EXTERNAL.PEER.1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: BARRACUDA.LDAP.EXTERNAL.PEER.2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: BARRACUDA.LDAP.EXTERNAL.PEER.3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: REYHEALTH.EXTERNAL.EXTERNAL.1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: REYHEALTH.EXTERNAL.EXTERNAL.2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CHC.OPTUM.EXTERNAL.VPN.PEER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS18D.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: STREAMTASK.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: GPSUPPORT.VPN.EXTERNAL.PEER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DI.AWSERVER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DI.AWSERVER.ILO - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DI.CTSCANNER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DI.CT.ADV.WS - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DI.GE.MAMMO.INTERFACE - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DI.MAMMO.SHUTTLE - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DI.MRI.ALLIANCE - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DI.MUSE01 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DI.MUSE02 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DI.MUSE03 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DI.MAMMO - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DI.NUCMEDCAMERA - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DI.PETCTVIEWER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DI.PERTH.XRAY - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DI.R.AND.F - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DI.ROOMA - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DI.XELERIS.NM - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NYOH.INTERNAL.5 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CLEARWATER1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CLEARWATER2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: JELMENDORFSPAM - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS25C.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PROVMDAPP.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHPROVMDORACLE.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHMUSE01.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHMUSE02.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS16A.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: DESIGO.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PATIENT.CONNECT.ARTERA.EXTERNAL.PEER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PATIENT.CONNECT.ARTERA.INTERNAL.PEER.1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PATIENT.CONNECT.ARTERA.INTERNAL.PEER.2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIOUS01 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHPRTGPROBE04 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS10C - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS28 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PATIENT.CONNECT.ARTERA.INTERNAL.PEER.3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PATIENT.CONNECT.ARTERA.INTERNAL.PEER.4 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS07.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NURSECALLAPP.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHBRAUNPUMPS.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: BRAUNWEB - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS09A.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS09B.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS09C.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHCISCO.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS13.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SQLTEST.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHMONITOR.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHPRTG01.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHKIWISYSLOG01.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS17A.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: XENAPP01.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CITRIXSF.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHWEB01..NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: AVAYACALLACCT.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHSSI.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: TEMPTRAK.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PRINT.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: QUICKCHARGE.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLH3M.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS19D.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHAV01.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS23A.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS23B.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS23C.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS23D.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHDHCP01.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS25B.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS21.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CENTRALINK.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS27.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: HEALTHTOUCH02.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MUSE03.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: KRONOSTEST.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MUSE-TEST.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: INTERLACETEST.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHINT-TEST.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS29.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHFUSION.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MUSE-CCGHL7.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS31.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS10B.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS10C.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHCA.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHPRTGPROBE3.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS10D.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CODONICS.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MDITEST.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: CITRIXFS02.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: XENAPP02.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MEDENTPRINT01.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: HEALTHTOUCH01.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS18A.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: INTERLACE.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NOVA-QIE.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS18B.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHUTILITY.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHCODONICS.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHLICENSE.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: HPDMAN.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: SCVMM.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS24A.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS24B.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS24C.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS24D.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS26A.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: ESICALLACCT26A.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: ESRS.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHDRFIRST.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHELOCK.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS26C.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: COBAS.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: PRADEV.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLHADMINCENTER.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS28.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NURSECALLHD.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NLH-iUV.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: LUCIUS30.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MUSE-APP.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MUSE-NXWEB.NLH.ORG - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Clearwater.External.Peer - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: ASA01 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: ASA02 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NETWORK_OBJ_172.16.201.35 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NETWORK_OBJ_192.168.178.2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: QUICKCHARGE.EXTERNAL.WHITELIST.PEER.1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: QUICKCHARGE.EXTERNAL.WHITELIST.PEER.2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MMI.BILLING.EXTERNAL.PEER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: MMI.BILLING.INTERNAL.PEER - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NYOH.INTERNAL.MEDICOM - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NYOH.INTERNAL.AMBRA - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NYOH.INTERNAL.POWERSHARE - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NYOH.INTERNAL.CLOUD - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Nuvodia.OneOncology.Cloud.External.Peer - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: Nuvodia.OneOncology.Cloud.Internal.Peer - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NETWORK_OBJ_162.245.33.10 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: NUVODIA.INTERNAL.NEW.3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: FRESHWORKS.EXCLUSIONS.1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: FRESHWORKS.EXCLUSIONS.2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: FRESHWORKS.EXCLUSIONS.3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: FRESHWORKS.EXCLUSIONS.5 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: FRESHWORKS.EXCLUSIONS.6 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create host: FRESHWORKS.EXCLUSIONS.7 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'"], "duration_seconds": 0.010054349899291992, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 0, "failed": 63, "skipped": 0, "details": ["❌ Failed to create network: TeleMedVT3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: TelemedVT4 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: TelemedVT5 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: TeleMedVT1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: Medent.VPN.net - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: SMHApacsSUBNET - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: pacs.net - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: PACS_VCE - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: pacs.net_1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: Olympus.Inside.New - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: speculator - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: GEserviceNET - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: Mill.PACS.NET - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: DI.NET - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: STUDENT_VLAN - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: questlab - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: iPEOPLEremote - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: LAN - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: RALSplusLAN - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: PhilipsSupport - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: STRAT_SOL.NET.INTERNAL1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: MVOrtho.net - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: LAN_1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: MilleniumPACSnat - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: MVOatJSC.net - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: SENTRYDS.NET - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: SENTRYDS - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: pacs.net-01 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: LAN-01 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: MilleniumPACSnat-10.205.56.127 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: obj-10.205.56.128-10.205.56.255 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: obj_any - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: obj_any-03 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: NUVODIA_NETWORK_3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: GUEST_WLAN_NAT - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: GUEST_NETWORK - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: VENDOR_WLAN_NAT - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: CREDITCARD_CAFE_EXTERNAL1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: CREDITCARD_CAFE2_EXTERNAL2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: CREDITCARD_CAFE_EXTERNAL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: STRAT_SOL.NET.INTERNAL2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: EXPANSE_VLAN1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: EXPANSE_VLAN2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: EXPANSE_VLAN3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: EXPANSE_VLAN4 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: QUEST.VPN.EXTERNAL.2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: CHC.OPTUM.NAT.INTERNAL.SUB - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: ACRONIS.EXTERNAL.RANGE1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: ACRONIS.EXTERNAL.RANGE2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: BARRACUDA.CLOUD.EXTERNAL - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: ACRONIS.EXTERNAL.RANGE3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: ACRONIS.EXTERNAL.RANGE4 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: GESUPPORT.INTERNAL.NET - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: CLEARWATER3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: CLEARWATER4 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: backblazeb2.com - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: HANYS.EXTERNAL.1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: HANYS.INTERNAL.2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: HANYS.EXTERNAL.3 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: Clearwater.Internal.Peer.Range - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: NLH.Firewall.Range.Internal - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: SSI.EXTERNAL.PEER.1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create network: MICROSOFTSTREAM.COM - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'"], "duration_seconds": 0.0013701915740966797, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 29, "created": 0, "updated": 0, "failed": 29, "skipped": 0, "details": ["❌ Failed to create service: obj-tcp-eq-80 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-15002 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-15331 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-3389 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-2222 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-6544 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-2020 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-23 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-15031 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-5631 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-udp-eq-15032 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-udp-eq-5632 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-25 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-443 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-55443 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-3401 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-53048 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-53372 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-53050 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-53374 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-21 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: NLI-BG13-FTP - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: W32.MYDOOM.OLD - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: GREYCASTLE_VPN - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: IMO_CLOUD - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: NOVA-8070-TCP - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: REYHEALTH.EXTERNAL.PORT1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: REYHEALTH.EXTERNAL.PORT2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: NOVA.TOPAZ - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'"], "duration_seconds": 0.0007469654083251953, "success_rate": 0.0}}, "current_phase": "phase1_services", "phase_result": {"phase_name": "phase1_services", "total_objects": 29, "created": 0, "updated": 0, "failed": 29, "skipped": 0, "details": ["❌ Failed to create service: obj-tcp-eq-80 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-15002 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-15331 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-3389 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-2222 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-6544 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-2020 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-23 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-15031 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-5631 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-udp-eq-15032 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-udp-eq-5632 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-25 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-443 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-55443 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-3401 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-53048 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-53372 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-53050 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-53374 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: obj-tcp-eq-21 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: NLI-BG13-FTP - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: W32.MYDOOM.OLD - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: GREYCASTLE_VPN - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: IMO_CLOUD - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: NOVA-8070-TCP - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: REYHEALTH.EXTERNAL.PORT1 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: REYHEALTH.EXTERNAL.PORT2 - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'", "❌ Failed to create service: NOVA.TOPAZ - Exception during creation: FMCAPIExecutor.create_object_with_retry() missing 1 required positional argument: 'existing_objects'"], "duration_seconds": 0.0007469654083251953, "success_rate": 0.0}, "phantom_objects": []}