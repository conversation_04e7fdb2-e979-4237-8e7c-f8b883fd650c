#!/usr/bin/env python3
"""
ASA to FMC Configuration Translator
This script parses a Cisco ASA configuration file and translates it into 
FMC (Firepower Management Center) API calls with verbose output.
"""

import json
import re
import sys
import ipaddress
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum

# Configuration classes for FMC objects
@dataclass
class FMCNetworkObject:
    name: str
    type: str  # Host, Network, FQDN, Range
    value: str
    description: str = ""
    overridable: bool = False

@dataclass
class FMCServiceObject:
    name: str
    type: str  # Always ProtocolPortObject for FMC API
    protocol: str
    port: int = 80  # Port number as integer per FMC API spec
    description: str = ""

@dataclass
class FMCAccessRule:
    name: str
    action: str  # ALLOW, BLOCK, TRUST, MONITOR
    sourceNetworks: List[Dict] = None
    destinationNetworks: List[Dict] = None
    sourcePorts: List[Dict] = None
    destinationPorts: List[Dict] = None
    applications: List[Dict] = None
    enabled: bool = True
    logBegin: bool = False
    logEnd: bool = False

class ASAToFMCTranslator:
    def __init__(self, verbose: bool = True):
        self.verbose = verbose
        self.network_objects = {}
        self.object_groups = {}
        self.service_objects = {}
        self.access_lists = {}
        self.nat_rules = []
        self.vpn_configs = {}
        self.interface_configs = {}
        self.routes = []
        
        # FMC API structure
        self.fmc_network_objects = []
        self.fmc_service_objects = []
        self.fmc_object_groups = []
        self.fmc_access_rules = []
        self.fmc_nat_rules = []
        
    def log(self, message: str, level: str = "INFO"):
        """Verbose logging function"""
        if self.verbose:
            print(f"[{level}] {message}")
    
    def parse_asa_config(self, config_file: str):
        """Parse the ASA configuration file"""
        self.log("Starting ASA configuration parsing...")
        
        with open(config_file, 'r') as f:
            lines = f.readlines()
        
        current_section = None
        object_group_name = None
        
        for line_num, line in enumerate(lines, 1):
            original_line = line  # Keep original line with indentation
            line = line.strip()
            if not line or line.startswith('!'):
                continue
                
            self.log(f"Processing line {line_num}: {line[:80]}{'...' if len(line) > 80 else ''}")
            
            # Parse network objects
            if line.startswith('object network '):
                self._parse_network_object(line, lines, line_num)
            
            # Parse object groups
            elif line.startswith('object-group '):
                object_group_name = self._parse_object_group_start(line)
                current_section = 'object_group'
            
            # Parse service objects
            elif line.startswith('object service '):
                self._parse_service_object(line, lines, line_num)
            
            # Parse access lists
            elif line.startswith('access-list '):
                self._parse_access_list(line)
            
            # Parse NAT rules
            elif line.startswith('nat '):
                self._parse_nat_rule(line)
            
            # Parse interfaces
            elif line.startswith('interface '):
                self._parse_interface(line, lines, line_num)
            
            # Parse routes
            elif line.startswith('route '):
                self._parse_route(line)
            
            # Parse VPN configurations
            elif line.startswith('crypto ') or line.startswith('tunnel-group '):
                self._parse_vpn_config(line)
            
            # Parse object group members - use original line to check indentation
            elif current_section == 'object_group' and object_group_name:
                if original_line.startswith(' '):
                    self._parse_object_group_member(object_group_name, line)
                else:
                    # End of object group - reset state
                    current_section = None
                    object_group_name = None
                    
                    # Since this line doesn't belong to the group, we need to process it normally
                    # Check if it's a new object group
                    if line.startswith('object-group '):
                        object_group_name = self._parse_object_group_start(line)
                        current_section = 'object_group'
        
        self.log(f"Parsing completed. Found {len(self.network_objects)} network objects, "
                f"{len(self.object_groups)} object groups, {len(self.access_lists)} access lists")
    
    def _parse_network_object(self, line: str, lines: List[str], line_num: int):
        """Parse network object definitions"""
        match = re.match(r'object network (\S+)', line)
        if match:
            name = match.group(1)
            self.log(f"Found network object: {name}")
            
            # Look for the object definition in following lines
            for i in range(line_num, min(line_num + 10, len(lines))):
                next_line = lines[i].strip()
                if next_line.startswith('host '):
                    ip = next_line.split()[1]
                    self.network_objects[name] = {
                        'type': 'Host',
                        'value': ip,
                        'name': name
                    }
                    self.log(f"  - Host object: {ip}")
                    break
                elif next_line.startswith('subnet '):
                    parts = next_line.split()
                    network = parts[1]
                    mask = parts[2]
                    self.network_objects[name] = {
                        'type': 'Network',
                        'value': f"{network}/{self._mask_to_cidr(mask)}",
                        'name': name
                    }
                    self.log(f"  - Network object: {network}/{self._mask_to_cidr(mask)}")
                    break
                elif next_line.startswith('range '):
                    parts = next_line.split()
                    start_ip = parts[1]
                    end_ip = parts[2]
                    self.network_objects[name] = {
                        'type': 'Range',
                        'value': f"{start_ip}-{end_ip}",
                        'name': name
                    }
                    self.log(f"  - Range object: {start_ip}-{end_ip}")
                    break
                elif next_line.startswith('fqdn '):
                    fqdn = next_line.split(None, 2)[2]  # Skip 'fqdn v4'
                    self.network_objects[name] = {
                        'type': 'FQDN',
                        'value': fqdn,
                        'name': name
                    }
                    self.log(f"  - FQDN object: {fqdn}")
                    break
    
    def _parse_object_group_start(self, line: str) -> Optional[str]:
        """Parse object group definition start"""
        match = re.match(r'object-group (\S+) (\S+)', line)
        if match:
            group_type = match.group(1)
            group_name = match.group(2)
            self.object_groups[group_name] = {
                'type': group_type,
                'name': group_name,
                'members': []
            }
            self.log(f"  - Found object group: {group_name} (type: {group_type})")
            return group_name
        return None
    
    def _parse_object_group_member(self, group_name: str, line: str):
        """Parse object group member"""
        line = line.strip()
        
        # Skip description lines - they're part of the group but not members
        if line.startswith('description '):
            return
            
        if line.startswith('network-object '):
            parts = line.split()
            if parts[1] == 'object':
                member = {'type': 'object', 'name': parts[2]}
            elif parts[1] == 'host':
                member = {'type': 'host', 'value': parts[2]}
            else:
                # Network with mask
                member = {'type': 'network', 'value': f"{parts[1]}/{self._mask_to_cidr(parts[2])}"}
            
            self.object_groups[group_name]['members'].append(member)
            self.log(f"  - Added member to {group_name}: {member}")
        
        elif line.startswith('group-object '):
            # Handle nested group references
            parts = line.split()
            group_ref_name = parts[1]
            member = {'type': 'group', 'name': group_ref_name}
            
            self.object_groups[group_name]['members'].append(member)
            self.log(f"  - Added group reference to {group_name}: {group_ref_name}")
        
        elif line.startswith('port-object '):
            parts = line.split()
            if 'eq' in parts:
                port = parts[parts.index('eq') + 1]
                member = {'type': 'port', 'value': port}
            elif 'range' in parts:
                start_port = parts[parts.index('range') + 1]
                end_port = parts[parts.index('range') + 2]
                member = {'type': 'range', 'value': f"{start_port}-{end_port}"}
            else:
                member = {'type': 'port', 'value': ' '.join(parts[1:])}
            
            self.object_groups[group_name]['members'].append(member)
            self.log(f"  - Added port member to {group_name}: {member}")
    
    def _parse_service_object(self, line: str, lines: List[str], line_num: int):
        """Parse service object definitions"""
        match = re.match(r'object service (\S+)', line)
        if match:
            name = match.group(1)
            self.log(f"Found service object: {name}")
            
            # Look for service definition in following lines
            for i in range(line_num, min(line_num + 5, len(lines))):
                next_line = lines[i].strip()
                if next_line.startswith('service '):
                    parts = next_line.split()
                    protocol = parts[1]
                    
                    if 'destination' in next_line and 'eq' in next_line:
                        port = parts[parts.index('eq') + 1]
                        self.service_objects[name] = {
                            'name': name,
                            'protocol': protocol.upper(),
                            'port': port
                        }
                        self.log(f"  - Service: {protocol.upper()}/{port}")
                    break
    
    def _parse_access_list(self, line: str):
        """Parse access list entries"""
        parts = line.split()
        if len(parts) < 3:
            return
        
        acl_name = parts[1]
        if acl_name not in self.access_lists:
            self.access_lists[acl_name] = []
            self.log(f"Found access list: {acl_name}")
        
        # Parse the access control entry
        ace = {
            'line': line,
            'action': 'permit' if 'permit' in line else 'deny',
            'protocol': 'any',
            'source': 'any',
            'destination': 'any',
            'service': 'any'
        }
        
        # Extract protocol, source, destination, and service information
        if 'extended' in line:
            try:
                action_idx = next(i for i, part in enumerate(parts) if part in ['permit', 'deny'])
                ace['action'] = parts[action_idx]
                
                if action_idx + 1 < len(parts):
                    ace['protocol'] = parts[action_idx + 1]
                
                # Find source and destination (simplified parsing)
                if 'any4' in line:
                    ace['source'] = 'any4'
                if 'object-group' in line:
                    ace['has_object_groups'] = True
                    
            except StopIteration:
                pass
        
        self.access_lists[acl_name].append(ace)
        self.log(f"  - Added ACE: {ace['action']} {ace['protocol']}")
    
    def _parse_nat_rule(self, line: str):
        """Parse NAT rules"""
        self.nat_rules.append({'line': line})
        self.log(f"Found NAT rule: {line[:50]}...")
    
    def _parse_interface(self, line: str, lines: List[str], line_num: int):
        """Parse interface configurations"""
        match = re.match(r'interface (\S+)', line)
        if match:
            interface_name = match.group(1)
            self.interface_configs[interface_name] = {'name': interface_name}
            self.log(f"Found interface: {interface_name}")
    
    def _parse_route(self, line: str):
        """Parse static routes"""
        self.routes.append({'line': line})
        self.log(f"Found route: {line}")
    
    def _parse_vpn_config(self, line: str):
        """Parse VPN configurations"""
        if 'crypto map' in line:
            self.log(f"Found VPN config: {line[:50]}...")
    
    def _mask_to_cidr(self, mask: str) -> int:
        """Convert subnet mask to CIDR notation"""
        try:
            return ipaddress.IPv4Network(f"0.0.0.0/{mask}").prefixlen
        except:
            return 24  # Default fallback
    
    def _convert_port_to_number(self, port: str) -> int:
        """Convert port name to number or return integer value"""
        if isinstance(port, int):
            return port
        
        # Common port name to number mappings
        port_mappings = {
            'www': 80,
            'http': 80,
            'https': 443,
            'ssh': 22,
            'telnet': 23,
            'ftp': 21,
            'smtp': 25,
            'dns': 53,
            'tftp': 69,
            'pop3': 110,
            'nntp': 119,
            'ntp': 123,
            'snmp': 161,
            'ldap': 389,
            'syslog': 514,
            'pcanywhere-data': 5631,
            'pcanywhere-stat': 5632,
            'pcanywhere-status': 5632,  # Alternative name for pcAnywhere status port
            # Add more mappings as needed
        }
        
        # If it's a string but represents a number, convert it
        if isinstance(port, str):
            if port.isdigit():
                return int(port)
            
            # Check if it's a named port
            port_lower = port.lower()
            if port_lower in port_mappings:
                return port_mappings[port_lower]
            
            # Try to extract numbers from ranges like "1024-2048"
            if '-' in port:
                # For ranges, use the first port number
                try:
                    return int(port.split('-')[0])
                except ValueError:
                    pass
        
        # Default fallback - return 80 if we can't determine the port
        self.log(f"Warning: Could not convert port '{port}' to number, using default 80", "WARN")
        return 80
    
    def convert_to_fmc(self):
        """Convert parsed ASA configuration to FMC API format"""
        self.log("Converting ASA configuration to FMC format...")
        
        # Convert network objects
        for name, obj in self.network_objects.items():
            fmc_obj = FMCNetworkObject(
                name=name,
                type=obj['type'],
                value=obj['value'],
                description=f"Migrated from ASA - {obj.get('description', '')}"
            )
            self.fmc_network_objects.append(asdict(fmc_obj))
            self.log(f"Converted network object: {name}")
        
        # Convert service objects
        for name, obj in self.service_objects.items():
            # Convert port name to number if needed
            port_value = self._convert_port_to_number(obj.get('port', ''))
            
            fmc_obj = FMCServiceObject(
                name=name,
                type="ProtocolPortObject",  # Always use ProtocolPortObject per FMC API spec
                protocol=obj['protocol'],
                port=port_value,
                description="Migrated from ASA"
            )
            self.fmc_service_objects.append(asdict(fmc_obj))
            self.log(f"Converted service object: {name}")
        
        # Convert object groups
        for name, group in self.object_groups.items():
            fmc_group = {
                'name': name,
                'type': f"{group['type']}Group",
                'objects': []
            }
            
            for member in group['members']:
                if member['type'] == 'object':
                    fmc_group['objects'].append({'name': member['name'], 'type': 'NetworkObject'})
                elif member['type'] == 'group':
                    # Handle nested group references
                    fmc_group['objects'].append({'name': member['name'], 'type': 'NetworkGroupObject'})
                elif member['type'] in ['host', 'network']:
                    fmc_group['objects'].append({'value': member['value'], 'type': 'Literal'})
                elif member['type'] in ['port', 'range']:
                    # For service groups, add port objects as literals
                    fmc_group['objects'].append({'value': member['value'], 'type': 'Literal'})
            
            self.fmc_object_groups.append(fmc_group)
            self.log(f"Converted object group: {name} with {len(group['members'])} members")
        
        # Convert access lists to access rules (simplified)
        for acl_name, aces in self.access_lists.items():
            for i, ace in enumerate(aces):
                rule = FMCAccessRule(
                    name=f"{acl_name}_rule_{i+1}",
                    action="ALLOW" if ace['action'] == 'permit' else "BLOCK",
                    enabled=True,
                    logEnd=True
                )
                self.fmc_access_rules.append(asdict(rule))
            
            self.log(f"Converted access list {acl_name} to {len(aces)} access rules")
    
    def generate_api_calls(self) -> Dict[str, List[Dict]]:
        """Generate FMC API call structure"""
        self.log("Generating FMC API calls...")
        
        # Separate Host objects from Network objects
        host_objects = []
        network_objects = []
        
        for obj in self.fmc_network_objects:
            if obj['type'] == 'Host':
                host_objects.append(obj)
            else:
                network_objects.append(obj)
        
        # Separate network groups from service groups for proper FMC endpoints
        network_groups = []
        service_groups = []
        
        for group in self.fmc_object_groups:
            if group['type'] in ['serviceGroup', 'Service Group']:
                # Service groups should use portobjectgroups endpoint
                service_groups.append(group)
            else:
                # Network groups use networkgroups endpoint
                network_groups.append(group)
        
        api_calls = {
            'host_objects': {
                'endpoint': '/api/fmc_config/v1/domain/{domain_uuid}/object/hosts',
                'method': 'POST',
                'data': host_objects
            },
            'network_objects': {
                'endpoint': '/api/fmc_config/v1/domain/{domain_uuid}/object/networks',
                'method': 'POST',
                'data': network_objects
            },
            'service_objects': {
                'endpoint': '/api/fmc_config/v1/domain/{domain_uuid}/object/protocolportobjects',
                'method': 'POST',
                'data': self.fmc_service_objects
            },
            'object_groups': {
                'endpoint': '/api/fmc_config/v1/domain/{domain_uuid}/object/networkgroups',
                'method': 'POST',
                'data': network_groups
            },
            'access_rules': {
                'endpoint': '/api/fmc_config/v1/domain/{domain_uuid}/policy/accesspolicies/{policy_uuid}/accessrules',
                'method': 'POST',
                'data': self.fmc_access_rules
            }
        }
        
        # Add service groups separately if they exist
        if service_groups:
            api_calls['service_groups'] = {
                'endpoint': '/api/fmc_config/v1/domain/{domain_uuid}/object/portobjectgroups',
                'method': 'POST',
                'data': service_groups
            }
        
        return api_calls
    
    def export_configuration(self, output_file: str = "fmc_migration_config.json"):
        """Export the complete migration configuration"""
        self.log(f"Exporting configuration to {output_file}...")
        
        # Get the API calls structure to count objects properly
        api_calls = self.generate_api_calls()
        
        migration_config = {
            'metadata': {
                'source': 'ASA Configuration Migration',
                'timestamp': '2025-01-08',
                'total_objects': {
                    'host_objects': len(api_calls.get('host_objects', {}).get('data', [])),
                    'network_objects': len(api_calls.get('network_objects', {}).get('data', [])),
                    'service_objects': len(self.fmc_service_objects),
                    'object_groups': len(self.fmc_object_groups),
                    'access_rules': len(self.fmc_access_rules)
                }
            },
            'api_calls': api_calls,
            'original_asa_config': {
                'network_objects': self.network_objects,
                'object_groups': self.object_groups,
                'service_objects': self.service_objects,
                'access_lists': dict(list(self.access_lists.items())[:5]),  # Sample only
                'interface_configs': self.interface_configs,
                'routes_count': len(self.routes),
                'nat_rules_count': len(self.nat_rules)
            }
        }
        
        with open(output_file, 'w') as f:
            json.dump(migration_config, f, indent=2)
        
        self.log(f"Configuration exported successfully to {output_file}")
        return migration_config

def main():
    """Main function to run the ASA to FMC translator"""
    if len(sys.argv) < 2:
        print("Usage: python asa_to_fmc_translator.py <asa_config_file>")
        sys.exit(1)
    
    config_file = sys.argv[1]
    
    # Initialize translator with verbose output
    translator = ASAToFMCTranslator(verbose=True)
    
    try:
        # Parse ASA configuration
        translator.parse_asa_config(config_file)
        
        # Convert to FMC format
        translator.convert_to_fmc()
        
        # Export configuration
        migration_config = translator.export_configuration()
        
        # Print summary
        print("\n" + "="*60)
        print("MIGRATION SUMMARY")
        print("="*60)
        print(f"Network Objects: {len(translator.fmc_network_objects)}")
        print(f"Service Objects: {len(translator.fmc_service_objects)}")
        print(f"Object Groups: {len(translator.fmc_object_groups)}")
        print(f"Access Rules: {len(translator.fmc_access_rules)}")
        print(f"NAT Rules: {len(translator.nat_rules)}")
        print(f"Routes: {len(translator.routes)}")
        print(f"Interfaces: {len(translator.interface_configs)}")
        print("="*60)
        
        print("\nNext Steps:")
        print("1. Review the generated 'fmc_migration_config.json' file")
        print("2. Modify the FMC connection details in the API calls")
        print("3. Execute the API calls against your FMC instance")
        print("4. Validate the configuration in FMC")
        
    except FileNotFoundError:
        print(f"Error: Configuration file '{config_file}' not found.")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 